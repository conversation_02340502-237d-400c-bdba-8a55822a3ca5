#!/usr/bin/env python3
"""
Continuous Payment Checker for Jellyfin by <PERSON>
Runs payment verification every 5 seconds on production Linux
Target: 192.168.1.174 / embyjames.xyz
"""

import requests
import time
import json
import logging
import sys
import os
import signal
from datetime import datetime

class ContinuousPaymentChecker:
    def __init__(self):
        self.base_url = "https://embyjames.xyz"
        self.api_endpoints = {
            'payment_check': f"{self.base_url}/api/payment-check.php",
            'manual_check': f"{self.base_url}/api/manual-check.php",
            'health': f"{self.base_url}/api/health.php"
        }
        
        self.running = True
        self.check_interval = 5  # seconds
        
        # Setup logging
        self.setup_logging()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = "/var/log/jellyfin"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        log_file = f"{log_dir}/continuous_payment.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
        
    def call_api(self, endpoint_name, method='POST', data=None):
        """Call API endpoint with error handling"""
        try:
            url = self.api_endpoints[endpoint_name]
            
            if method == 'POST':
                response = requests.post(url, json=data, timeout=15)
            else:
                response = requests.get(url, timeout=15)
                
            if response.status_code == 200:
                try:
                    result = response.json()
                    return True, result
                except json.JSONDecodeError:
                    return True, {"message": response.text}
            else:
                return False, {"error": f"HTTP {response.status_code}"}
                
        except requests.exceptions.Timeout:
            return False, {"error": "Timeout"}
        except requests.exceptions.ConnectionError:
            return False, {"error": "Connection error"}
        except Exception as e:
            return False, {"error": str(e)}
    
    def check_system_health(self):
        """Quick health check"""
        success, result = self.call_api('health', 'GET')
        return success
    
    def process_payments(self):
        """Process both PayNoi and manual payments"""
        results = {
            'paynoi': False,
            'manual': False,
            'processed_count': 0
        }
        
        # Check PayNoi payments
        success, result = self.call_api('payment_check')
        if success:
            results['paynoi'] = True
            if 'processed' in result:
                results['processed_count'] += result.get('processed', 0)
        
        # Check manual payments
        success, result = self.call_api('manual_check')
        if success:
            results['manual'] = True
            if 'checked' in result:
                results['processed_count'] += result.get('checked', 0)
        
        return results
    
    def run_check_cycle(self):
        """Run one payment check cycle"""
        start_time = time.time()
        
        # Quick health check every 10 cycles
        if hasattr(self, 'cycle_count') and self.cycle_count % 10 == 0:
            if not self.check_system_health():
                self.logger.warning("⚠️ Health check failed")
                return False
        
        # Process payments
        results = self.process_payments()
        
        # Log results
        if results['processed_count'] > 0:
            self.logger.info(f"💰 Processed {results['processed_count']} payments")
        
        # Log any failures
        if not results['paynoi']:
            self.logger.warning("⚠️ PayNoi check failed")
        if not results['manual']:
            self.logger.warning("⚠️ Manual check failed")
        
        duration = time.time() - start_time
        if duration > 3:  # Log if taking too long
            self.logger.warning(f"⏱️ Slow cycle: {duration:.2f}s")
        
        return results['paynoi'] or results['manual']
    
    def run_continuous(self):
        """Main continuous loop"""
        self.logger.info("🚀 Starting continuous payment checker...")
        self.logger.info(f"⏰ Check interval: {self.check_interval} seconds")
        self.logger.info(f"🎯 Target: {self.base_url}")
        
        cycle_count = 0
        last_success_time = time.time()
        
        while self.running:
            try:
                cycle_count += 1
                self.cycle_count = cycle_count
                
                # Run check cycle
                success = self.run_check_cycle()
                
                if success:
                    last_success_time = time.time()
                else:
                    # Check if we've been failing for too long
                    time_since_success = time.time() - last_success_time
                    if time_since_success > 300:  # 5 minutes
                        self.logger.error(f"❌ No successful checks for {time_since_success:.0f} seconds")
                
                # Log status every 100 cycles (about 8 minutes)
                if cycle_count % 100 == 0:
                    self.logger.info(f"📊 Completed {cycle_count} cycles")
                
                # Sleep until next check
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                self.logger.info("⏹️ Interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"❌ Unexpected error: {str(e)}")
                time.sleep(self.check_interval)
        
        self.logger.info("🏁 Continuous payment checker stopped")
    
    def run_daemon(self):
        """Run as daemon process"""
        try:
            # Create PID file
            pid_file = "/var/run/jellyfin_payment_checker.pid"
            with open(pid_file, 'w') as f:
                f.write(str(os.getpid()))
            
            self.logger.info(f"📝 PID file created: {pid_file}")
            
            # Run continuous loop
            self.run_continuous()
            
        except Exception as e:
            self.logger.error(f"❌ Daemon error: {str(e)}")
        finally:
            # Clean up PID file
            if os.path.exists(pid_file):
                os.remove(pid_file)
                self.logger.info("🧹 PID file cleaned up")

def main():
    """Main function"""
    checker = ContinuousPaymentChecker()
    
    if len(sys.argv) > 1 and sys.argv[1] == "daemon":
        checker.run_daemon()
    else:
        checker.run_continuous()

if __name__ == "__main__":
    main()
