<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

$subscription_id = (int)($_GET['subscription_id'] ?? 0);

// Get subscription details
$stmt = $pdo->prepare("
    SELECT us.*, p.name as package_name, p.duration_days, p.price, p.description,
           pt.transaction_ref, pt.qr_code_url, pt.slip_image, pt.status as payment_status
    FROM user_subscriptions us
    JOIN packages p ON us.package_id = p.id
    LEFT JOIN payment_transactions pt ON us.id = pt.subscription_id
    WHERE us.id = ? AND us.user_id = ?
");
$stmt->execute([$subscription_id, $_SESSION['user_id']]);
$subscription = $stmt->fetch();

if (!$subscription) {
    header('Location: /packages');
    exit();
}

$message = '';
$message_type = 'info';

// Handle slip upload
if ($_POST && isset($_POST['upload_slip']) && isset($_FILES['slip_image'])) {
    try {
        $upload_dir = 'uploads/slips/';
        if (!is_dir($upload_dir)) {
            if (!mkdir($upload_dir, 0755, true)) {
                throw new Exception('ไม่สามารถสร้างโฟลเดอร์อัปโหลดได้');
            }
        }

        // Check if directory is writable
        if (!is_writable($upload_dir)) {
            throw new Exception('โฟลเดอร์อัปโหลดไม่สามารถเขียนไฟล์ได้');
        }

        $file = $_FILES['slip_image'];
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในระบบ',
                UPLOAD_ERR_FORM_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในฟอร์ม',
                UPLOAD_ERR_PARTIAL => 'ไฟล์อัปโหลดไม่สมบูรณ์',
                UPLOAD_ERR_NO_FILE => 'ไม่มีไฟล์ที่อัปโหลด',
                UPLOAD_ERR_NO_TMP_DIR => 'ไม่พบโฟลเดอร์ชั่วคราว',
                UPLOAD_ERR_CANT_WRITE => 'ไม่สามารถเขียนไฟล์ได้',
                UPLOAD_ERR_EXTENSION => 'การอัปโหลดถูกหยุดโดย extension'
            ];
            throw new Exception($upload_errors[$file['error']] ?? 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์');
        }

        // Validate file
        if (empty($file['name'])) {
            throw new Exception('กรุณาเลือกไฟล์ที่จะอัปโหลด');
        }

        // Validate file type by MIME type
        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('ประเภทไฟล์ไม่ถูกต้อง กรุณาอัปโหลดไฟล์รูปภาพ (JPG, PNG, GIF)');
        }

        // Validate file extension
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, $allowed_extensions)) {
            throw new Exception('นามสกุลไฟล์ไม่ถูกต้อง กรุณาใช้ไฟล์ .jpg, .jpeg, .png หรือ .gif');
        }

        // Validate file size
        if ($file['size'] <= 0) {
            throw new Exception('ไฟล์ว่างเปล่า กรุณาเลือกไฟล์ใหม่');
        }

        if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
            throw new Exception('ไฟล์มีขนาดใหญ่เกินไป (สูงสุด 5MB)');
        }

        // Validate that it's actually an image
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            throw new Exception('ไฟล์ที่อัปโหลดไม่ใช่รูปภาพที่ถูกต้อง');
        }

                // Check for duplicate slip by calculating file hash
                $file_hash = hash_file('sha256', $file['tmp_name']);

        // Calculate file hash for duplicate detection
        $file_hash = hash_file('sha256', $file['tmp_name']);
        if (!$file_hash) {
            throw new Exception('ไม่สามารถตรวจสอบไฟล์ได้ กรุณาลองใหม่');
        }

        // Check if this slip hash has been used before in verified payments
        $stmt = $pdo->prepare("
            SELECT pt.id, pt.slip_image, u.username
            FROM payment_transactions pt
            JOIN users u ON pt.user_id = u.id
            WHERE pt.slip_hash = ? AND pt.status IN ('completed', 'verified')
            LIMIT 1
        ");
        $stmt->execute([$file_hash]);
        $duplicate_slip = $stmt->fetch();

        if ($duplicate_slip) {
            throw new Exception('สลิปนี้เคยถูกใช้แล้วโดย: ' . htmlspecialchars($duplicate_slip['username']) . '<br>กรุณาแนบสลิปใหม่อีกรอบ');
        }

        // Generate unique filename
        $new_filename = 'slip_' . $subscription_id . '_' . time() . '_' . uniqid() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
            throw new Exception('ไม่สามารถบันทึกไฟล์ได้ กรุณาตรวจสอบสิทธิ์การเขียนไฟล์');
        }

        // Verify file was uploaded successfully
        if (!file_exists($upload_path)) {
            throw new Exception('ไฟล์ไม่ได้ถูกบันทึก กรุณาลองใหม่');
        }

        // Find the pending payment transaction for this subscription
        $stmt = $pdo->prepare("
            SELECT id FROM payment_transactions
            WHERE user_id = ? AND subscription_id = ? AND status = 'pending'
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$_SESSION['user_id'], $subscription_id]);
        $payment_transaction = $stmt->fetch();

        if (!$payment_transaction) {
            // Clean up uploaded file
            unlink($upload_path);
            throw new Exception('ไม่พบรายการชำระเงินที่รอดำเนินการ');
        }

        // Update payment transaction with slip image, hash, and set as manual payment
        $stmt = $pdo->prepare("
            UPDATE payment_transactions
            SET slip_image = ?, slip_hash = ?, payment_method = 'manual', updated_at = NOW()
            WHERE id = ?
        ");
        $update_result = $stmt->execute([$upload_path, $file_hash, $payment_transaction['id']]);

        if (!$update_result) {
            // Clean up uploaded file
            unlink($upload_path);
            throw new Exception('ไม่สามารถอัปเดตข้อมูลการชำระเงินได้');
        }

        $message = 'อัปโหลดสลิปเรียบร้อยแล้ว กรุณารอการอนุมัติจาก Admin';
        $message_type = 'success';

        log_activity($_SESSION['user_id'], 'slip_uploaded', "Uploaded payment slip for subscription ID: {$subscription_id}, file: {$new_filename}");

        // Redirect to refresh the page and show updated data
        header("Location: /payment?subscription_id=" . $subscription_id);
        exit();

    } catch (Exception $e) {
        $message = $e->getMessage();
        $message_type = 'error';

        // Log the error for debugging
        error_log("Slip upload error for user {$_SESSION['user_id']}: " . $e->getMessage());
    }
}

// Handle payment cancellation
if ($_POST && isset($_POST['cancel_payment'])) {
    try {
        $pdo->beginTransaction();

        // Update subscription status to cancelled
        $stmt = $pdo->prepare("
            UPDATE user_subscriptions
            SET status = 'cancelled', updated_at = NOW()
            WHERE id = ? AND user_id = ? AND status = 'pending'
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id']]);

        // Update payment transaction status
        $stmt = $pdo->prepare("
            UPDATE payment_transactions
            SET status = 'cancelled', updated_at = NOW()
            WHERE subscription_id = ? AND user_id = ?
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id']]);

        $pdo->commit();

        log_activity($_SESSION['user_id'], 'payment_cancelled', "Cancelled payment for subscription ID: {$subscription_id}");

        set_flash_message('ยกเลิกการชำระเงินเรียบร้อยแล้ว', 'info');
        header('Location: /packages');
        exit();

    } catch (Exception $e) {
        $pdo->rollBack();
        $message = 'เกิดข้อผิดพลาดในการยกเลิกการชำระเงิน';
        $message_type = 'error';
        error_log('Payment cancellation error: ' . $e->getMessage());
    }
}

// Handle payment confirmation
if ($_POST && isset($_POST['confirm_payment'])) {
    try {
        $pdo->beginTransaction();

        // Update payment transaction to pending verification
        $stmt = $pdo->prepare("
            UPDATE payment_transactions
            SET status = 'pending_verification', updated_at = NOW()
            WHERE subscription_id = ? AND user_id = ?
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id']]);

        // Keep subscription as pending until verification
        $stmt = $pdo->prepare("
            UPDATE user_subscriptions
            SET updated_at = NOW()
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id']]);

        $pdo->commit();

        // Log activity
        log_activity($_SESSION['user_id'], 'payment_confirmed', "Payment confirmed for package: {$subscription['package_name']} - Waiting for verification");

        set_flash_message('ยืนยันการชำระเงินเรียบร้อยแล้ว! กำลังตรวจสอบการโอนเงิน กรุณารอสักครู่...', 'info');
        header('Location: /dashboard');
        exit();

    } catch (Exception $e) {
        $pdo->rollBack();
        $message = 'เกิดข้อผิดพลาดในการยืนยันการชำระเงิน กรุณาลองใหม่อีกครั้ง';
        $message_type = 'error';
        error_log('Payment confirmation error: ' . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ชำระเงิน - Jellyfin by James Registration System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        /* Flash Messages */
        .flash-message {
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            border-radius: 6px;
            font-weight: 500;
            border-left: 4px solid;
            animation: slideInDown 0.3s ease-out;
        }

        .flash-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border-left-color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .flash-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-left-color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .flash-info {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
            border-left-color: #17a2b8;
            border: 1px solid rgba(23, 162, 184, 0.3);
        }

        .flash-warning {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border-left-color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        @keyframes slideInDown {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .payment-container {
            max-width: 600px;
            margin: 2rem auto;
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #333;
        }
        .package-summary {
            background: rgba(0, 0, 0, 0.3);
            padding: 1.5rem;
            border-radius: 4px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .qr-code-container {
            text-align: center;
            margin: 2rem 0;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
        }
        .qr-code-container img {
            max-width: 300px;
            width: 100%;
            height: auto;
            border: 2px solid #333;
            border-radius: 4px;
        }
        .payment-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #ffc107;
            margin: 1rem 0;
        }
        .payment-instructions {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            padding: 1.5rem;
            border-radius: 4px;
            margin: 2rem 0;
        }

        /* Hide payment instructions on mobile devices */
        @media (max-width: 768px) {
            .payment-instructions {
                display: none;
            }
        }

        .download-qr-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            text-transform: none;
        }

        .download-qr-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea080);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }

        .download-qr-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
        }

        /* แสดงปุ่มเฉพาะใน mobile */
        .download-qr-btn {
            display: none;
        }

        @media (max-width: 768px) {
            .download-qr-btn {
                display: inline-block;
                padding: 0.6rem 1.2rem;
                font-size: 0.95rem;
            }
        }
        .payment-instructions h4 {
            color: #ffc107;
            margin-bottom: 1rem;
        }
        .payment-instructions ol {
            color: #ccc;
            padding-left: 1.5rem;
        }
        .payment-instructions li {
            margin-bottom: 0.5rem;
        }
        .transaction-ref {
            background: rgba(0, 0, 0, 0.5);
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 1rem 0;
            border: 1px solid #333;
        }
        .confirm-payment-section {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid #28a745;
            padding: 1.5rem;
            border-radius: 4px;
            margin-top: 2rem;
            text-align: center;
        }
        .status-pending {
            color: #ffc107;
        }
        .status-completed {
            color: #28a745;
        }
        .file-upload-area {
            border: 2px dashed #666;
            border-radius: 4px;
            padding: 1rem;
            text-align: center;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            border-color: #999;
            background: rgba(255, 255, 255, 0.05);
        }
        .slip-preview {
            max-width: 200px;
            border: 1px solid #333;
            border-radius: 4px;
            margin: 1rem auto;
            display: block;
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                <a href="/packages" class="nav-link">แพ็คเกจ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php if (is_admin()): ?>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                            <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                            <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                            <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="payment-container fade-in-up">
                <h2 style="text-align: center; margin-bottom: 2rem;">ชำระเงิน</h2>
                
                <?php if ($message): ?>
                    <div class="flash-message flash-<?php echo $message_type; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Package Summary -->
                <div class="package-summary">
                    <h3><?php echo htmlspecialchars($subscription['package_name']); ?></h3>
                    <p><?php echo htmlspecialchars($subscription['description'] ?: 'แพ็คเกจ ' . $subscription['duration_days'] . ' วัน'); ?></p>
                    <div class="payment-amount">
                        <?php echo number_format($subscription['payment_amount'] ?? 0, 2); ?> บาท
                    </div>
                    <p style="color: #ccc;">ระยะเวลา: <?php echo $subscription['duration_days']; ?> วัน</p>
                </div>

                <!-- Transaction Reference -->
                <div class="transaction-ref">
                    <strong>หมายเลขอ้างอิง:</strong> <?php echo htmlspecialchars($subscription['transaction_ref']); ?>
                </div>

                <!-- QR Code -->
                <?php if ($subscription['qr_code_url']): ?>
                    <div class="qr-code-container">
                        <h4 style="color: #000; margin-bottom: 1rem;">สแกน QR Code เพื่อชำระเงิน</h4>
                        <img id="qr-code-image"
                             src="<?php echo htmlspecialchars($subscription['qr_code_url']); ?>"
                             alt="PromptPay QR Code"
                             onerror="this.style.display='none'; document.getElementById('qr-error').style.display='block';">
                        <div id="qr-error" style="display: none; color: #dc3545; padding: 1rem;">
                            ไม่สามารถโหลด QR Code ได้ กรุณาลองใหม่อีกครั้ง
                        </div>
                        <div style="margin-top: 1rem;">
                            <button onclick="downloadQRCode()" class="download-qr-btn">
                                💾 บันทึก QR Code
                            </button>
                        </div>
                        <p style="color: #666; margin-top: 1rem; font-size: 0.9rem;">
                            <strong>ชื่อผู้รับเงิน: นาย เอกลักษณ์ สงวนสินธ์</strong><br>
                            PromptPay: ************<br>
                            จำนวนเงิน: <?php echo number_format($subscription['payment_amount'] ?? 0, 2); ?> บาท
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Payment Instructions -->
                <div class="payment-instructions">
                    <h4>วิธีการชำระเงิน</h4>
                    <ol>
                        <li>เปิดแอปธนาคารหรือแอป Mobile Banking</li>
                        <li>เลือกเมนู "สแกน QR Code" หรือ "PromptPay"</li>
                        <li>สแกน QR Code ด้านบน</li>
                        <li>ตรวจสอบจำนวนเงิน: <strong><?php echo number_format($subscription['payment_amount'] ?? 0, 2); ?> บาท</strong></li>
                        <li>ยืนยันการโอนเงิน</li>
                        <li>กดปุ่ม "ยืนยันการชำระเงิน" ด้านล่าง</li>
                    </ol>
                </div>

                <!-- Payment Status -->
                <div style="text-align: center; margin: 2rem 0;">
                    <p>สถานะการชำระเงิน: 
                        <span class="status-<?php echo $subscription['payment_status']; ?>">
                            <?php 
                            switch($subscription['payment_status']) {
                                case 'pending': echo 'รอการชำระเงิน'; break;
                                case 'completed': echo 'ชำระเงินแล้ว'; break;
                                case 'failed': echo 'การชำระเงินล้มเหลว'; break;
                                case 'cancelled': echo 'ยกเลิกการชำระเงิน'; break;
                                default: echo 'ไม่ทราบสถานะ'; break;
                            }
                            ?>
                        </span>
                    </p>
                </div>

                <!-- Upload Slip Section -->
                <?php if ($subscription['payment_status'] === 'pending' && !$subscription['slip_image']): ?>
                    <div class="confirm-payment-section">
                        <h4 style="color: #17a2b8; margin-bottom: 1rem;">แนบสลิปการโอนเงิน</h4>
                        <p style="color: #ccc; margin-bottom: 1.5rem;">
                            กรุณาแนบสลิปการโอนเงินเพื่อยืนยันการชำระเงิน<br>
                            <small style="color: #ffc107;">⚠️ หมายเหตุ: ห้ามใช้สลิปซ้ำ กรุณาใช้สลิปใหม่ทุกครั้ง</small>
                        </p>
                        <form method="POST" enctype="multipart/form-data" style="margin-bottom: 1rem;">
                            <div class="form-group">
                                <label for="slip_image" style="color: #ccc;">เลือกไฟล์สลิป (JPG, PNG, GIF - สูงสุด 5MB)</label>
                                <input type="file" id="slip_image" name="slip_image" class="form-control"
                                       accept="image/*" required>
                            </div>
                            <button type="submit" name="upload_slip" class="btn btn-primary noir-button" style="width: 100%;">
                                อัปโหลดสลิป
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <!-- Status after slip upload -->
                <?php if ($subscription['payment_status'] === 'pending' && $subscription['slip_image']): ?>
                    <div class="confirm-payment-section" style="background: rgba(40, 167, 69, 0.1); border-color: #28a745;">
                        <h4 style="color: #28a745; margin-bottom: 1rem;">✓ แนบสลิปเรียบร้อยแล้ว</h4>
                        <div style="text-align: center; margin-bottom: 1rem;">
                            <img src="<?php echo htmlspecialchars($subscription['slip_image']); ?>"
                                 alt="Payment Slip"
                                 style="max-width: 200px; border: 1px solid #28a745; border-radius: 4px;">
                        </div>
                        <p style="color: #ccc; text-align: center; margin-bottom: 1.5rem;">
                            รอการอนุมัติจาก Admin<br>
                            <small>ระบบจะตรวจสอบการชำระเงินอัตโนมัติทุก 5 วินาที</small>
                        </p>
                        <div style="text-align: center;">
                            <button onclick="window.location.reload()" class="btn btn-primary noir-button" style="width: 100%; max-width: 300px;">
                                🔄 ตรวจสอบสถานะ
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                    <!-- Confirm Payment Section -->
                    <?php if ($subscription['slip_image'] && false): // ซ่อน section นี้ไว้ก่อน ?>
                        <div class="confirm-payment-section" style="background: rgba(40, 167, 69, 0.1); border-color: #28a745;">
                            <h4 style="color: #28a745; margin-bottom: 1rem;">ยืนยันการชำระเงิน</h4>
                            <p style="color: #ccc; margin-bottom: 1.5rem;">
                                หลังจากแนบสลิปแล้ว กรุณายืนยันการชำระเงิน
                            </p>
                            <form method="POST">
                                <input type="hidden" name="confirm_payment" value="1">
                                <button type="submit" class="btn btn-primary noir-button" style="width: 100%;"
                                        onclick="return confirm('คุณได้ทำการโอนเงินและแนบสลิปเรียบร้อยแล้วใช่หรือไม่?')">
                                    ยืนยันการชำระเงิน
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>

                <!-- Cancel Payment Section -->
                <?php if ($subscription['payment_status'] === 'pending' && !$subscription['slip_image']): ?>
                    <div class="confirm-payment-section" style="background: rgba(220, 53, 69, 0.1); border-color: #dc3545; margin-top: 1rem;">
                        <h4 style="color: #dc3545; margin-bottom: 1rem;">ยกเลิกการชำระเงิน</h4>
                        <p style="color: #ccc; margin-bottom: 1.5rem;">
                            หากต้องการเปลี่ยนแพ็คเกจ สามารถยกเลิกการชำระเงินนี้และเลือกแพ็คเกจใหม่ได้
                        </p>
                        <form method="POST">
                            <input type="hidden" name="cancel_payment" value="1">
                            <button type="submit" class="btn btn-secondary noir-button" style="width: 100%; background: #dc3545;"
                                    onclick="return confirm('คุณต้องการยกเลิกการชำระเงินนี้และเลือกแพ็คเกจใหม่ใช่หรือไม่?')">
                                ยกเลิกการชำระเงิน
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <?php if ($subscription['payment_status'] === 'completed'): ?>
                    <div class="confirm-payment-section" style="background: rgba(40, 167, 69, 0.2);">
                        <h4 style="color: #28a745;">การชำระเงินเสร็จสิ้น</h4>
                        <p style="color: #ccc;">บัญชีของคุณได้ถูกเปิดใช้งานแล้ว</p>
                        <a href="/dashboard" class="btn btn-primary noir-button">
                            กลับไปที่ Dashboard
                        </a>
                    </div>
                <?php endif; ?>

                <div style="text-align: center; margin-top: 2rem;">
                    <a href="/packages" style="color: #ccc;">← กลับไปเลือกแพ็คเกจ</a>
                </div>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
    <script>


        function downloadQRCode() {
            const qrImage = document.getElementById('qr-code-image');
            if (!qrImage || !qrImage.src) {
                alert('ไม่พบ QR Code ที่จะดาวน์โหลด');
                return;
            }

            // สร้าง canvas เพื่อแปลงรูปภาพ
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                canvas.width = img.naturalWidth || img.width;
                canvas.height = img.naturalHeight || img.height;
                ctx.drawImage(img, 0, 0);

                // แปลงเป็น data URL
                const dataURL = canvas.toDataURL('image/png');

                // ลองดาวน์โหลดด้วยวิธีต่างๆ
                if (downloadWithDataURL(dataURL)) {
                    return;
                }

                // หากไม่สำเร็จ ให้ใช้ blob method
                canvas.toBlob(function(blob) {
                    if (blob && downloadWithBlob(blob)) {
                        return;
                    }

                    // หากยังไม่สำเร็จ ให้เปิดรูปภาพในหน้าใหม่
                    openImageForSave(dataURL);
                }, 'image/png');
            };

            img.onerror = function() {
                // หากโหลดรูปภาพไม่ได้ ให้เปิด URL โดยตรง
                openImageForSave(qrImage.src);
            };

            img.crossOrigin = 'anonymous';
            img.src = qrImage.src;

            function downloadWithDataURL(dataURL) {
                try {
                    const a = document.createElement('a');
                    a.href = dataURL;
                    a.download = 'qr-code-payment-<?php echo $subscription['id'] ?? 'unknown'; ?>.png';
                    a.style.display = 'none';

                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);

                    return true;
                } catch (error) {
                    console.error('Data URL download failed:', error);
                    return false;
                }
            }

            function downloadWithBlob(blob) {
                try {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'qr-code-payment-<?php echo $subscription['id'] ?? 'unknown'; ?>.png';
                    a.style.display = 'none';

                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);

                    setTimeout(() => {
                        URL.revokeObjectURL(url);
                    }, 300);

                    return true;
                } catch (error) {
                    console.error('Blob download failed:', error);
                    return false;
                }
            }

            function openImageForSave(imageSrc) {
                // เปิดรูปภาพในหน้าใหม่พร้อมคำแนะนำ
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                    newWindow.document.write(`
                        <html>
                            <head>
                                <title>QR Code - บันทึกรูปภาพ</title>
                                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                <style>
                                    body {
                                        margin: 0;
                                        padding: 20px;
                                        background: #000;
                                        display: flex;
                                        flex-direction: column;
                                        align-items: center;
                                        justify-content: center;
                                        min-height: 100vh;
                                        font-family: Arial, sans-serif;
                                    }
                                    .instruction {
                                        color: #fff;
                                        text-align: center;
                                        margin-bottom: 20px;
                                        font-size: 18px;
                                        background: rgba(255, 193, 7, 0.2);
                                        padding: 20px;
                                        border-radius: 10px;
                                        border: 2px solid #ffc107;
                                        max-width: 90%;
                                        animation: pulse 2s infinite;
                                    }
                                    @keyframes pulse {
                                        0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
                                        70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
                                        100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
                                    }
                                    img {
                                        max-width: 90vw;
                                        max-height: 60vh;
                                        border: 3px solid #ffc107;
                                        border-radius: 10px;
                                        background: white;
                                        padding: 15px;
                                        box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
                                    }
                                    .close-btn {
                                        margin-top: 20px;
                                        padding: 15px 30px;
                                        background: #ffc107;
                                        color: #000;
                                        border: none;
                                        border-radius: 8px;
                                        font-size: 16px;
                                        font-weight: bold;
                                        cursor: pointer;
                                    }
                                </style>
                            </head>
                            <body>
                                <div class="instruction">
                                    📱 <strong>วิธีบันทึกรูปภาพ:</strong><br><br>
                                    <strong>📲 Android:</strong> กดค้างที่รูป → "บันทึกรูปภาพ"<br>
                                    <strong>🍎 iPhone:</strong> กดค้างที่รูป → "บันทึกลงในรูปภาพ"<br><br>
                                    รูปภาพจะถูกบันทึกเข้าอัลบั้มโทรศัพท์
                                </div>
                                <img src="${imageSrc}" alt="QR Code สำหรับชำระเงิน">
                                <button class="close-btn" onclick="window.close()">ปิดหน้าต่าง</button>
                            </body>
                        </html>
                    `);
                    newWindow.document.close();


                } else {
                    alert('❌ ไม่สามารถเปิดหน้าต่างใหม่ได้\nกรุณาอนุญาต popup ในเบราว์เซอร์');
                }
            }
        }
    </script>
</body>
</html>
