<?php
/**
 * API Users Management สำหรับ Admin Tools
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

session_start();

// ตรวจสอบ admin authentication
if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

require_once '../../includes/config.php';

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            // ดึงรายการผู้ใช้
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? '';
            
            $sql = "SELECT u.id, u.username, u.phone, u.created_at, u.status, 
                           p.name as package_name, u.expires_at
                    FROM users u 
                    LEFT JOIN packages p ON u.package_id = p.id 
                    WHERE 1=1";
            $params = [];
            
            if (!empty($search)) {
                $sql .= " AND (u.username LIKE ? OR u.phone LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }
            
            if (!empty($status) && $status !== 'all') {
                $sql .= " AND u.status = ?";
                $params[] = $status;
            }
            
            $sql .= " ORDER BY u.created_at DESC";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $users = $stmt->fetchAll();
            
            echo json_encode($users);
            break;
            
        case 'POST':
            // เพิ่มผู้ใช้ใหม่
            $data = json_decode(file_get_contents('php://input'), true);
            
            $username = $data['username'] ?? '';
            $phone = $data['phone'] ?? '';
            $password = $data['password'] ?? '';
            $package_id = $data['package_id'] ?? null;
            $status = $data['status'] ?? 'active';
            
            if (empty($username) || empty($phone) || empty($password)) {
                echo json_encode(['success' => false, 'message' => 'Required fields missing']);
                exit;
            }
            
            // ตรวจสอบ username ซ้ำ
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'Username already exists']);
                exit;
            }
            
            // เพิ่มผู้ใช้
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $expires_at = null;
            
            if ($package_id) {
                $stmt = $pdo->prepare("SELECT duration_days FROM packages WHERE id = ?");
                $stmt->execute([$package_id]);
                $package = $stmt->fetch();
                if ($package) {
                    $expires_at = date('Y-m-d H:i:s', strtotime("+{$package['duration_days']} days"));
                }
            }
            
            $stmt = $pdo->prepare("INSERT INTO users (username, phone, password, package_id, status, expires_at, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$username, $phone, $hashed_password, $package_id, $status, $expires_at]);
            
            echo json_encode(['success' => true, 'message' => 'User created successfully']);
            break;
            
        case 'PUT':
            // แก้ไขผู้ใช้
            $data = json_decode(file_get_contents('php://input'), true);
            
            $user_id = $data['id'] ?? '';
            $username = $data['username'] ?? '';
            $phone = $data['phone'] ?? '';
            $package_id = $data['package_id'] ?? null;
            $status = $data['status'] ?? 'active';
            
            if (empty($user_id) || empty($username) || empty($phone)) {
                echo json_encode(['success' => false, 'message' => 'Required fields missing']);
                exit;
            }
            
            $expires_at = null;
            if ($package_id) {
                $stmt = $pdo->prepare("SELECT duration_days FROM packages WHERE id = ?");
                $stmt->execute([$package_id]);
                $package = $stmt->fetch();
                if ($package) {
                    $expires_at = date('Y-m-d H:i:s', strtotime("+{$package['duration_days']} days"));
                }
            }
            
            $stmt = $pdo->prepare("UPDATE users SET username = ?, phone = ?, package_id = ?, status = ?, expires_at = ? WHERE id = ?");
            $stmt->execute([$username, $phone, $package_id, $status, $expires_at, $user_id]);
            
            echo json_encode(['success' => true, 'message' => 'User updated successfully']);
            break;
            
        case 'DELETE':
            // ลบผู้ใช้
            $user_id = $_GET['id'] ?? '';
            
            if (empty($user_id)) {
                echo json_encode(['success' => false, 'message' => 'User ID required']);
                exit;
            }
            
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Users API Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
