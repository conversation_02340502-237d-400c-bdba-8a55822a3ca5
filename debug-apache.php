<?php
// Debug Apache configuration for Clean URLs
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Apache - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        .info { color: #17a2b8; }
        .code-block {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }
        .command {
            color: #4CAF50;
        }
        .comment {
            color: #888;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="color: #FFD700; text-align: center;">🔧 Debug Apache Configuration</h1>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">📋 Current Server Status</h2>
            <?php
            echo "<p><strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
            echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
            echo "<p><strong>OS:</strong> " . PHP_OS . "</p>";
            echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Request Method:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
            
            // Check if running on Apache
            $server_software = $_SERVER['SERVER_SOFTWARE'] ?? '';
            if (stripos($server_software, 'apache') !== false) {
                echo "<p class='success'>✅ Running on Apache</p>";
            } else {
                echo "<p class='warning'>⚠️ Not running on Apache: $server_software</p>";
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🔍 mod_rewrite Check</h2>
            <?php
            // Check mod_rewrite
            if (function_exists('apache_get_modules')) {
                $modules = apache_get_modules();
                echo "<p class='info'>Available Apache modules:</p>";
                echo "<div class='code-block'>";
                foreach ($modules as $module) {
                    if (stripos($module, 'rewrite') !== false) {
                        echo "<span class='success'>✅ $module</span><br>";
                    } else {
                        echo "<span style='color: #ccc;'>$module</span><br>";
                    }
                }
                echo "</div>";
                
                if (in_array('mod_rewrite', $modules)) {
                    echo "<p class='success'>✅ mod_rewrite is enabled</p>";
                } else {
                    echo "<p class='error'>❌ mod_rewrite is NOT enabled</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ Cannot check Apache modules (function not available)</p>";
                
                // Alternative check
                if (getenv('HTTP_MOD_REWRITE') == 'On') {
                    echo "<p class='success'>✅ mod_rewrite detected via environment</p>";
                } else {
                    echo "<p class='warning'>⚠️ mod_rewrite status unknown</p>";
                }
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">📁 File System Check</h2>
            <?php
            $files_to_check = [
                '.htaccess' => 'Main .htaccess file',
                'index.php' => 'Homepage',
                'login.php' => 'Login page',
                'dashboard.php' => 'Dashboard page',
                'admin/' => 'Admin directory',
                'admin/index.php' => 'Admin homepage',
                'assets/' => 'Assets directory',
                'assets/css/' => 'CSS directory',
                'assets/css/style.css' => 'Main stylesheet'
            ];
            
            foreach ($files_to_check as $file => $description) {
                if (file_exists($file)) {
                    if (is_dir($file)) {
                        echo "<p class='success'>✅ Directory: $description ($file)</p>";
                    } else {
                        $size = filesize($file);
                        $perms = substr(sprintf('%o', fileperms($file)), -4);
                        echo "<p class='success'>✅ File: $description ($file) - Size: " . number_format($size) . " bytes, Permissions: $perms</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Missing: $description ($file)</p>";
                }
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">⚙️ .htaccess Content</h2>
            <?php
            if (file_exists('.htaccess') && is_readable('.htaccess')) {
                $htaccess_content = file_get_contents('.htaccess');
                echo "<p class='success'>✅ .htaccess is readable</p>";
                echo "<div class='code-block'>";
                echo htmlspecialchars($htaccess_content);
                echo "</div>";
            } else {
                echo "<p class='error'>❌ Cannot read .htaccess file</p>";
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🛠️ Fix Commands for Linux</h2>
            
            <h3 style="color: #FFD700;">1. Enable mod_rewrite (Ubuntu/Debian):</h3>
            <div class="code-block">
                <span class="comment"># Enable mod_rewrite module</span><br>
                <span class="command">sudo a2enmod rewrite</span><br><br>
                
                <span class="comment"># Restart Apache</span><br>
                <span class="command">sudo systemctl restart apache2</span><br><br>
                
                <span class="comment"># Check if enabled</span><br>
                <span class="command">apache2ctl -M | grep rewrite</span>
            </div>
            
            <h3 style="color: #FFD700;">2. Configure Virtual Host:</h3>
            <div class="code-block">
                <span class="comment"># Edit your site configuration</span><br>
                <span class="command">sudo nano /etc/apache2/sites-available/000-default.conf</span><br><br>
                
                <span class="comment"># Add this inside &lt;VirtualHost&gt; block:</span><br>
                &lt;Directory /var/www/html&gt;<br>
                &nbsp;&nbsp;&nbsp;&nbsp;Options Indexes FollowSymLinks<br>
                &nbsp;&nbsp;&nbsp;&nbsp;AllowOverride All<br>
                &nbsp;&nbsp;&nbsp;&nbsp;Require all granted<br>
                &lt;/Directory&gt;
            </div>
            
            <h3 style="color: #FFD700;">3. Set Correct Permissions:</h3>
            <div class="code-block">
                <span class="comment"># Set file permissions</span><br>
                <span class="command">sudo chmod 644 .htaccess</span><br>
                <span class="command">sudo chmod 644 *.php</span><br>
                <span class="command">sudo chmod 755 admin/</span><br>
                <span class="command">sudo chmod 644 admin/*.php</span><br><br>
                
                <span class="comment"># Set ownership (replace www-data with your web server user)</span><br>
                <span class="command">sudo chown -R www-data:www-data /var/www/html/</span>
            </div>
            
            <h3 style="color: #FFD700;">4. Check Apache Error Log:</h3>
            <div class="code-block">
                <span class="comment"># View recent errors</span><br>
                <span class="command">sudo tail -f /var/log/apache2/error.log</span><br><br>
                
                <span class="comment"># Check access log</span><br>
                <span class="command">sudo tail -f /var/log/apache2/access.log</span>
            </div>
            
            <h3 style="color: #FFD700;">5. Test Configuration:</h3>
            <div class="code-block">
                <span class="comment"># Test Apache configuration</span><br>
                <span class="command">sudo apache2ctl configtest</span><br><br>
                
                <span class="comment"># Reload configuration</span><br>
                <span class="command">sudo systemctl reload apache2</span>
            </div>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🧪 Quick Test</h2>
            <p style="color: #ccc;">ทดสอบ Clean URLs:</p>
            
            <div style="margin: 20px 0;">
                <a href="/login" style="color: #4CAF50; text-decoration: none; margin-right: 20px;">Test /login</a>
                <a href="/dashboard" style="color: #4CAF50; text-decoration: none; margin-right: 20px;">Test /dashboard</a>
                <a href="/admin" style="color: #4CAF50; text-decoration: none;">Test /admin</a>
            </div>
            
            <p style="color: #ccc; font-size: 0.9rem;">
                หากลิงก์ข้างบนใช้งานได้ แสดงว่า Clean URLs ทำงานแล้ว<br>
                หากได้ 404 Error แสดงว่ายังมีปัญหาอยู่
            </p>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/test-htaccess.php" style="color: #00c300; text-decoration: none; padding: 10px 20px; border: 1px solid #00c300; border-radius: 5px; margin-right: 10px;">
                🧪 Full .htaccess Test
            </a>
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                🏠 หน้าแรก
            </a>
        </div>
    </div>
</body>
</html>
