#!/bin/bash
# Simple Installation script for Jelly<PERSON> by James Production Environment
# For root user on Linux server ************* / embyjames.xyz

set -e

echo "🎬 Jellyfin by James - Production Installation (Root)"
echo "===================================================="
echo "Target: ************* / embyjames.xyz"
echo "User: root"
echo "===================================================="

# Check if we're running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root"
   echo "Please run: sudo ./install_production_root.sh"
   exit 1
fi

# Check if we're on the right server
echo "🔍 Checking server environment..."
if [[ $(hostname -I | grep -c "*************") -eq 0 ]]; then
    echo "⚠️  Warning: Not running on expected IP *************"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Install required packages
echo "📦 Installing required packages..."
apt update
apt install -y python3 python3-pip curl mysql-client

# Install Python packages globally
echo "🐍 Installing Python packages..."
pip3 install requests

# Create directories
echo "📁 Creating directories..."
mkdir -p /opt/jellyfin
mkdir -p /var/log/jellyfin
chown www-data:www-data /var/log/jellyfin
chmod 755 /var/log/jellyfin

# Copy files
echo "📋 Copying files..."
cp production_cron.py /opt/jellyfin/
cp continuous_payment_checker.py /opt/jellyfin/
cp monitor_production.py /opt/jellyfin/
chmod +x /opt/jellyfin/*.py
chown www-data:www-data /opt/jellyfin/*.py

# Install systemd service
echo "⚙️  Installing systemd service..."
cp jellyfin-payment-checker.service /etc/systemd/system/
systemctl daemon-reload

# Install crontab for www-data user
echo "⏰ Installing crontab..."
crontab -u www-data jellyfin_crontab.txt

# Test API endpoints
echo "🧪 Testing API endpoints..."
if curl -s -f "https://embyjames.xyz/api/health.php" > /dev/null; then
    echo "✅ API endpoints accessible"
else
    echo "❌ API endpoints not accessible"
    echo "Please check your web server configuration"
fi

# Test database connection
echo "🗄️  Testing database connection..."
if mysql -h ************* -u root -p'Wxmujwsofu@1234' jellyfin_registration -e "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    echo "Please check database credentials and network access"
fi

# Start and enable service
echo "🚀 Starting services..."
systemctl enable jellyfin-payment-checker
systemctl start jellyfin-payment-checker

# Check service status
sleep 3
if systemctl is-active --quiet jellyfin-payment-checker; then
    echo "✅ Payment checker service is running"
    
    # Show service status
    echo ""
    echo "📊 Service Status:"
    systemctl status jellyfin-payment-checker --no-pager -l
else
    echo "❌ Payment checker service failed to start"
    echo ""
    echo "🔍 Service logs:"
    journalctl -u jellyfin-payment-checker --no-pager -l
fi

echo ""
echo "🎉 Installation completed!"
echo ""
echo "📊 Monitoring commands:"
echo "  Service status: systemctl status jellyfin-payment-checker"
echo "  Service logs:   journalctl -u jellyfin-payment-checker -f"
echo "  Payment logs:   tail -f /var/log/jellyfin/continuous_payment.log"
echo "  Full monitor:   python3 /opt/jellyfin/monitor_production.py"
echo ""
echo "🔧 Management commands:"
echo "  Start service:  systemctl start jellyfin-payment-checker"
echo "  Stop service:   systemctl stop jellyfin-payment-checker"
echo "  Restart:        systemctl restart jellyfin-payment-checker"
echo "  View crontab:   crontab -u www-data -l"
echo ""
echo "🧪 Testing commands:"
echo "  Test health:    python3 /opt/jellyfin/production_cron.py health"
echo "  Test payments:  python3 /opt/jellyfin/production_cron.py payment"
echo "  Full test:      python3 /opt/jellyfin/production_cron.py full"
echo ""
echo "📈 The system will now:"
echo "  ✓ Check payments every 5 seconds (continuous service)"
echo "  ✓ Run full system checks every 5 minutes (cron)"
echo "  ✓ Perform health checks every minute (cron)"
echo "  ✓ Clean up logs weekly"
echo ""
echo "🌐 Access your system at: https://embyjames.xyz"
echo ""
echo "🔍 To monitor the system:"
echo "  python3 /opt/jellyfin/monitor_production.py"
