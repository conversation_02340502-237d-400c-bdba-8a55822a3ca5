<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/JellyfinAPI.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    $response = ['success' => false, 'message' => ''];
    
    try {
        switch ($action) {
            case 'start':
                $result = startJellyfinServer();
                break;
            case 'stop':
                $result = stopJellyfinServer();
                break;
            case 'restart':
                $result = restartJellyfinServer();
                break;
            case 'status':
                $result = getJellyfinServerStatus();
                break;
            default:
                throw new Exception('Invalid action');
        }
        
        $response['success'] = $result['success'];
        $response['message'] = $result['message'];
        
        // Log server action
        $stmt = $pdo->prepare("
            INSERT INTO server_logs (action, status, message, user_id, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $action,
            $result['success'] ? 'success' : 'error',
            $result['message'],
            $_SESSION['user_id'],
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // Log user activity
        log_activity($_SESSION['user_id'], 'server_' . $action, $result['message']);
        
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
        error_log('Server control error: ' . $e->getMessage());
    }
    
    echo json_encode($response);
    exit();
}

// Get current server status
$server_status = getJellyfinServerStatus();

// Get recent server logs
$stmt = $pdo->prepare("
    SELECT sl.*, u.username 
    FROM server_logs sl 
    LEFT JOIN users u ON sl.user_id = u.id 
    ORDER BY sl.created_at DESC 
    LIMIT 20
");
$stmt->execute();
$server_logs = $stmt->fetchAll();

/**
 * Start Jellyfin server
 */
function startJellyfinServer() {
    // Check if server is already running
    $jellyfin = new JellyfinAPI();
    if ($jellyfin->isServerRunning()) {
        return ['success' => false, 'message' => 'Server is already running'];
    }
    
    // Platform-specific start commands
    if (PHP_OS_FAMILY === 'Windows') {
        // Windows - start Jellyfin service
        $command = 'net start JellyfinServer 2>&1';
        $output = shell_exec($command);
        
        if (strpos($output, 'successfully') !== false || strpos($output, 'already') !== false) {
            return ['success' => true, 'message' => 'Jellyfin server started successfully'];
        } else {
            // Try alternative method - start executable
            $command = 'start /B jellyfin.exe 2>&1';
            $output = shell_exec($command);
            return ['success' => true, 'message' => 'Jellyfin server start command executed'];
        }
    } else {
        // Linux/Unix - start systemd service or docker container
        $commands = [
            'systemctl start jellyfin 2>&1',
            'service jellyfin start 2>&1',
            'docker start jellyfin 2>&1'
        ];
        
        foreach ($commands as $command) {
            $output = shell_exec($command);
            if ($output && (strpos($output, 'Started') !== false || strpos($output, 'Active') !== false)) {
                return ['success' => true, 'message' => 'Jellyfin server started successfully'];
            }
        }
        
        return ['success' => false, 'message' => 'Failed to start Jellyfin server: ' . ($output ?? 'Unknown error')];
    }
}

/**
 * Stop Jellyfin server
 */
function stopJellyfinServer() {
    // Platform-specific stop commands
    if (PHP_OS_FAMILY === 'Windows') {
        // Windows - stop Jellyfin service
        $command = 'net stop JellyfinServer 2>&1';
        $output = shell_exec($command);
        
        if (strpos($output, 'successfully') !== false || strpos($output, 'not started') !== false) {
            return ['success' => true, 'message' => 'Jellyfin server stopped successfully'];
        } else {
            // Try to kill process
            $command = 'taskkill /F /IM jellyfin.exe 2>&1';
            $output = shell_exec($command);
            return ['success' => true, 'message' => 'Jellyfin server stop command executed'];
        }
    } else {
        // Linux/Unix - stop systemd service or docker container
        $commands = [
            'systemctl stop jellyfin 2>&1',
            'service jellyfin stop 2>&1',
            'docker stop jellyfin 2>&1'
        ];
        
        foreach ($commands as $command) {
            $output = shell_exec($command);
            if ($output && (strpos($output, 'Stopped') !== false || strpos($output, 'Inactive') !== false)) {
                return ['success' => true, 'message' => 'Jellyfin server stopped successfully'];
            }
        }
        
        return ['success' => false, 'message' => 'Failed to stop Jellyfin server: ' . ($output ?? 'Unknown error')];
    }
}

/**
 * Restart Jellyfin server
 */
function restartJellyfinServer() {
    // Stop first
    $stopResult = stopJellyfinServer();
    if (!$stopResult['success']) {
        return $stopResult;
    }
    
    // Wait a moment
    sleep(2);
    
    // Then start
    return startJellyfinServer();
}

/**
 * Get Jellyfin server status
 */
function getJellyfinServerStatus() {
    try {
        $jellyfin = new JellyfinAPI();
        $status = $jellyfin->getServerStatus();
        
        return [
            'success' => true,
            'status' => $status['status'],
            'version' => $status['version'],
            'name' => $status['name']
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'status' => 'unknown',
            'version' => null,
            'name' => 'Jellyfin Server',
            'message' => $e->getMessage()
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Control - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="../assets/images/favicon.svg">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .server-control-panel {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #333;
            margin: 2rem 0;
        }
        .server-status {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-unknown { background-color: #ffc107; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .control-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        .server-control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .btn-start {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .btn-stop {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: white;
        }
        .btn-restart {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: #000;
        }
        .btn-refresh {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
        }
        .server-control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .server-control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .logs-container {
            background: rgba(0, 0, 0, 0.5);
            padding: 1rem;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .log-entry {
            padding: 0.5rem 0;
            border-bottom: 1px solid #333;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .log-time {
            color: #888;
            font-size: 0.8rem;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">Server Control Panel</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">Manage your Jellyfin server</p>
            </div>

            <div class="server-control-panel fade-in-scroll">
                <h3>Server Status</h3>
                <div class="server-status" id="server-status">
                    <div class="status-indicator status-<?php echo $server_status['status']; ?>"></div>
                    <div>
                        <div><strong>Status:</strong> <span id="status-text"><?php echo ucfirst($server_status['status']); ?></span></div>
                        <div><strong>Server:</strong> <?php echo htmlspecialchars($server_status['name']); ?></div>
                    </div>
                </div>

                <h3>Server Controls</h3>
                <div class="control-buttons">
                    <button class="btn btn-success server-control-btn" data-action="start">
                        <i class="icon-play"></i> เริ่มเซิร์ฟเวอร์
                    </button>
                    <button class="btn btn-danger server-control-btn" data-action="stop">
                        <i class="icon-stop"></i> หยุดเซิร์ฟเวอร์
                    </button>
                    <button class="btn btn-warning server-control-btn" data-action="restart">
                        <i class="icon-refresh"></i> รีสตาร์ทเซิร์ฟเวอร์
                    </button>
                    <button class="btn btn-info btn-refresh" onclick="updateServerStatus()">
                        <i class="icon-refresh"></i> รีเฟรชสถานะ
                    </button>
                </div>

                <div style="margin-top: 2rem;">
                    <p style="color: #ffc107; font-size: 0.9rem;">
                        <strong>Note:</strong> Server control commands may require proper system permissions. 
                        Ensure your web server has the necessary privileges to manage the Jellyfin service.
                    </p>
                </div>
            </div>

            <div class="server-control-panel fade-in-scroll">
                <h3>Server Logs</h3>
                <div class="logs-container" id="server-logs">
                    <?php foreach ($server_logs as $log): ?>
                        <div class="log-entry">
                            <div class="log-<?php echo $log['status']; ?>">
                                <strong><?php echo htmlspecialchars($log['action'] ?? 'Unknown'); ?></strong>
                                <?php if ($log['username']): ?>
                                    by <?php echo htmlspecialchars($log['username']); ?>
                                <?php endif; ?>
                            </div>
                            <div><?php echo htmlspecialchars($log['message'] ?? 'No message'); ?></div>
                            <div class="log-time"><?php echo date('M j, Y g:i:s A', strtotime($log['created_at'])); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
    <script>
        // Auto-refresh server status every 30 seconds
        setInterval(updateServerStatus, 30000);
    </script>
</body>
</html>
