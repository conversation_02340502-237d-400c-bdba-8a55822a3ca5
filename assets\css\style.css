/* Main Stylesheet for Jellyfin Registration System */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    color: #fff;
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.main-header {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.9));
    backdrop-filter: blur(15px);
    border-bottom: 2px solid #444;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    overflow: visible !important;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 20px;
    min-height: 70px;
    overflow: visible !important;
    position: relative;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: translateY(-2px);
    text-shadow: 2px 2px 8px rgba(255, 215, 0, 0.4);
}

.logo img {
    height: 50px;
    width: auto;
    filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.5))
            drop-shadow(0 2px 10px rgba(0, 0, 0, 0.7))
            drop-shadow(0 0 25px rgba(255, 165, 0, 0.3));
    transition: all 0.3s ease;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.logo img:hover {
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8))
            drop-shadow(0 2px 15px rgba(0, 0, 0, 0.8))
            drop-shadow(0 0 35px rgba(255, 165, 0, 0.5));
    transform: scale(1.08) rotate(1deg);
}

/* Logo Text Styling */
.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.1;
}

.cinema-subtitle {
    font-size: 0.6em;
    font-weight: normal;
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    letter-spacing: 0.2em;
    text-transform: uppercase;
    margin-top: -0.2em;
    opacity: 0.9;
    animation: cinemaGlow 2s ease-in-out infinite alternate;
}

/* Cinema Logo Glow Animation */
@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.5))
                drop-shadow(0 2px 10px rgba(0, 0, 0, 0.7))
                drop-shadow(0 0 25px rgba(255, 165, 0, 0.3));
    }
    100% {
        filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.7))
                drop-shadow(0 2px 12px rgba(0, 0, 0, 0.8))
                drop-shadow(0 0 30px rgba(255, 165, 0, 0.4));
    }
}

@keyframes cinemaGlow {
    0% {
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.5),
                     0 0 20px rgba(255, 215, 0, 0.3);
    }
    100% {
        text-shadow: 0 0 15px rgba(255, 215, 0, 0.8),
                     0 0 30px rgba(255, 215, 0, 0.4),
                     0 0 40px rgba(255, 165, 0, 0.2);
    }
}

/* Logo Fallback Styling */
.logo-fallback {
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF8C00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.main-nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: nowrap;
    overflow: visible !important;
    scrollbar-width: none;
    -ms-overflow-style: none;
    position: relative;
    z-index: 50;
}

.main-nav::-webkit-scrollbar {
    display: none;
}

.nav-link {
    color: #ccc;
    text-decoration: none;
    padding: 0.6rem 0.8rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
    border: 1px solid transparent;
}

.nav-link:hover {
    color: #fff;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.nav-link.active {
    color: #fff;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
    border-color: rgba(255, 193, 7, 0.3);
}

/* Profile Dropdown */
.profile-dropdown {
    position: relative !important;
    display: inline-block;
    z-index: 1000 !important;
}

.profile-dropdown .nav-link {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    cursor: pointer;
}

.profile-dropdown .nav-link::after {
    content: '▼';
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.profile-dropdown:hover .nav-link::after,
.profile-dropdown.active .nav-link::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: #222;
    border: 2px solid #555;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
    min-width: 160px;
    z-index: 9999 !important;
    display: none;
    overflow: visible !important;
    white-space: nowrap;
}

/* Admin dropdown menu positioning */
.admin-dropdown .dropdown-menu {
    left: 0 !important;
    right: auto !important;
}

/* Show dropdown ONLY on active class (click only) */
.profile-dropdown.active .dropdown-menu,
.admin-dropdown.active .dropdown-menu {
    display: block !important;
}

.dropdown-item {
    display: block;
    padding: 12px 16px;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid #333;
    font-size: 14px;
    white-space: nowrap;
}

.dropdown-item:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.dropdown-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.dropdown-item:hover {
    background: #333;
    color: #fff;
}

.dropdown-item.logout {
    color: #ff6b6b;
    border-top: 1px solid #333;
}

.dropdown-item.logout:hover {
    background: #2d1b1b;
    color: #ff8a8a;
}

/* Admin Console Dropdown */
.admin-dropdown {
    position: relative !important;
    display: inline-block;
    z-index: 1000 !important;
}

.admin-dropdown .nav-link {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    border-color: rgba(255, 193, 7, 0.2);
    cursor: pointer;
}

.admin-dropdown .nav-link::after {
    content: '▼';
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.admin-dropdown:hover .nav-link::after,
.admin-dropdown.active .nav-link::after {
    transform: rotate(180deg);
}

.admin-dropdown .dropdown-menu {
    min-width: 180px;
    left: 0;
    right: auto;
    z-index: 9999 !important;
    overflow: visible !important;
}

.admin-dropdown .dropdown-item:hover {
    background: #2d2416;
    color: #ffc107;
}

/* Enhanced Dropdown Visibility */
.profile-dropdown,
.admin-dropdown {
    overflow: visible !important;
}

.profile-dropdown .dropdown-menu,
.admin-dropdown .dropdown-menu {
    position: absolute !important;
    z-index: 99999 !important;
    overflow: visible !important;
    clip: unset !important;
    clip-path: none !important;
}

/* Ensure parent containers don't clip dropdowns */
.main-header,
.main-header .container,
.main-nav {
    overflow: visible !important;
    clip: unset !important;
    clip-path: none !important;
}

/* Force dropdown to appear above everything */
.dropdown-menu.show,
.profile-dropdown.active .dropdown-menu,
.admin-dropdown.active .dropdown-menu {
    display: block !important;
    position: absolute !important;
    z-index: 999999 !important;
    overflow: visible !important;
    transform: none !important;
    clip: unset !important;
    clip-path: none !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Additional fixes for dropdown visibility */
html, body {
    overflow-x: visible !important;
}

.main-header,
.main-header *,
.main-nav,
.main-nav * {
    overflow: visible !important;
    contain: none !important;
}

/* Ensure dropdowns work in all contexts */
.dropdown-menu {
    will-change: auto !important;
    contain: none !important;
    isolation: auto !important;
}

/* Mobile specific fixes */
@media (max-width: 768px) {
    .dropdown-menu {
        position: fixed !important;
        z-index: 999999 !important;
        max-width: calc(100vw - 20px);
        box-sizing: border-box;
    }

    .admin-dropdown .dropdown-menu {
        left: 10px !important;
        right: auto !important;
    }

    .profile-dropdown .dropdown-menu {
        right: 10px !important;
        left: auto !important;
    }
}





/* Mobile Navigation Enhancements */
.nav-scroll-indicator {
    display: none;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.8));
    color: #ccc;
    padding: 0.5rem;
    font-size: 0.8rem;
    pointer-events: none;
}

@media (max-width: 768px) {
    .main-nav {
        position: relative;
    }

    .nav-scroll-indicator {
        display: block;
    }

    /* Smooth scrolling for navigation */
    .main-nav {
        scroll-behavior: smooth;
    }

    /* Add subtle gradient fade on edges */
    .main-nav::before,
    .main-nav::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 20px;
        pointer-events: none;
        z-index: 1;
    }

    .main-nav::before {
        left: 0;
        background: linear-gradient(90deg, rgba(0, 0, 0, 0.3), transparent);
    }

    .main-nav::after {
        right: 0;
        background: linear-gradient(270deg, rgba(0, 0, 0, 0.3), transparent);
    }
}

/* Main Content */
.main-content {
    flex: 1;
}

.hero-section {
    padding: 4rem 0;
    text-align: center;
    background: 
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #ccc;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(45deg, #333, #555);
    color: #fff;
    border: 2px solid #666;
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #666;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid #333;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.feature-card p {
    color: #ccc;
    line-height: 1.6;
}

/* Form Styles */
.form-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    border: 1px solid #333;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #ccc;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    border-radius: 4px;
    color: #fff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #777;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.form-control::placeholder {
    color: #888;
}

/* Flash Messages */
.flash-message {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 4px;
    font-weight: bold;
}

.flash-success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid #28a745;
    color: #28a745;
}

.flash-error {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid #dc3545;
    color: #dc3545;
}

.flash-warning {
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid #ffc107;
    color: #ffc107;
}

.flash-info {
    background: rgba(23, 162, 184, 0.2);
    border: 1px solid #17a2b8;
    color: #17a2b8;
}

/* Footer */
.main-footer {
    background: rgba(0, 0, 0, 0.9);
    border-top: 1px solid #333;
    padding: 2rem 0;
    text-align: center;
    color: #666;
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-nav {
        gap: 0.3rem;
    }

    .nav-link {
        padding: 0.5rem 0.6rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    .main-header .container {
        padding: 0.6rem 15px;
        min-height: 60px;
    }

    .logo {
        font-size: 1.4rem;
        gap: 0.6rem;
    }

    .logo img {
        height: 40px;
        animation: none; /* Disable animation on mobile for performance */
    }

    .logo img:hover {
        transform: scale(1.03);
    }

    .cinema-subtitle {
        font-size: 0.5em;
        letter-spacing: 0.1em;
        animation: none; /* Disable animation on mobile */
    }

    .main-nav {
        gap: 0.2rem;
        max-width: 100%;
        overflow-x: auto;
        overflow-y: visible;
        padding: 0.2rem 0;
    }

    .nav-link {
        padding: 0.4rem 0.5rem;
        font-size: 0.8rem;
        min-width: fit-content;
    }

    .profile-dropdown .nav-link {
        font-size: 0.75rem;
    }

    .dropdown-menu {
        right: -10px;
        min-width: 140px;
        z-index: 99999 !important;
        position: fixed !important;
        top: auto !important;
        transform: none !important;
        overflow: visible !important;
    }

    .dropdown-item {
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }

    .admin-dropdown .nav-link {
        font-size: 0.75rem;
    }

    .admin-dropdown .dropdown-menu {
        min-width: 160px;
        z-index: 99999 !important;
        position: fixed !important;
        left: 10px !important;
        right: auto !important;
        top: auto !important;
        transform: none !important;
        overflow: visible !important;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-header .container {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem 10px;
    }

    .main-nav {
        width: 100%;
        justify-content: flex-start;
        overflow-x: auto;
        overflow-y: visible;
        padding: 0.3rem 0;
    }

    .nav-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
    }
}
