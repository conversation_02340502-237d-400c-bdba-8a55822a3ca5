<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!is_admin()) {
    header('Location: /login');
    exit();
}

$user = get_user_by_id($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Console - Je<PERSON>fin by <PERSON></title>
    <link rel="icon" type="image/svg+xml" href="../assets/images/favicon.svg">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by <PERSON> Logo" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                <a href="/packages" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                        <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                        <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                        <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">⚙️ Admin Console</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">ระบบจัดการสำหรับผู้ดูแลระบบ</p>
            </div>

            <div class="admin-dashboard" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 2rem;">
                
                <!-- Server Management -->
                <div class="admin-card fade-in-up" style="background: rgba(255, 255, 255, 0.05); padding: 2rem; border-radius: 10px; border: 1px solid #333;">
                    <h3 style="color: #FFD700; margin-bottom: 1rem;">🖥️ เซิร์ฟเวอร์</h3>
                    <p style="color: #ccc; margin-bottom: 1.5rem;">จัดการเซิร์ฟเวอร์ Jellyfin และระบบ</p>
                    <a href="/admin/server" class="btn btn-primary" style="display: inline-block; padding: 0.8rem 1.5rem; background: #FFD700; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold;">
                        เข้าสู่ระบบเซิร์ฟเวอร์
                    </a>
                </div>

                <!-- User Management -->
                <div class="admin-card fade-in-up delay-1" style="background: rgba(255, 255, 255, 0.05); padding: 2rem; border-radius: 10px; border: 1px solid #333;">
                    <h3 style="color: #FFD700; margin-bottom: 1rem;">👥 ผู้ใช้</h3>
                    <p style="color: #ccc; margin-bottom: 1.5rem;">จัดการบัญชีผู้ใช้และสิทธิ์</p>
                    <a href="/admin/users" class="btn btn-primary" style="display: inline-block; padding: 0.8rem 1.5rem; background: #FFD700; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold;">
                        จัดการผู้ใช้
                    </a>
                </div>

                <!-- Package Management -->
                <div class="admin-card fade-in-up delay-2" style="background: rgba(255, 255, 255, 0.05); padding: 2rem; border-radius: 10px; border: 1px solid #333;">
                    <h3 style="color: #FFD700; margin-bottom: 1rem;">📦 แพ็คเกจ</h3>
                    <p style="color: #ccc; margin-bottom: 1.5rem;">ดูและจัดการแพ็คเกจทั้งหมด</p>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <a href="/admin/packages" class="btn btn-primary" style="display: inline-block; padding: 0.6rem 1rem; background: #FFD700; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 0.9rem;">
                            ดูแพ็คเกจ
                        </a>
                        <a href="/admin/manage-packages" class="btn btn-secondary" style="display: inline-block; padding: 0.6rem 1rem; background: #333; color: #fff; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 0.9rem;">
                            จัดการ
                        </a>
                        <a href="/admin/add-package" class="btn btn-success" style="display: inline-block; padding: 0.6rem 1rem; background: #28a745; color: #fff; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 0.9rem;">
                            เพิ่มใหม่
                        </a>
                    </div>
                </div>

                <!-- Transaction Management -->
                <div class="admin-card fade-in-up delay-3" style="background: rgba(255, 255, 255, 0.05); padding: 2rem; border-radius: 10px; border: 1px solid #333;">
                    <h3 style="color: #FFD700; margin-bottom: 1rem;">💰 ธุรกรรม</h3>
                    <p style="color: #ccc; margin-bottom: 1.5rem;">ตรวจสอบการชำระเงินและ PayNoi</p>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <a href="/admin/transactions" class="btn btn-primary" style="display: inline-block; padding: 0.6rem 1rem; background: #FFD700; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 0.9rem;">
                            ตรวจสอบ
                        </a>
                        <a href="/admin/paynoi" class="btn btn-info" style="display: inline-block; padding: 0.6rem 1rem; background: #17a2b8; color: #fff; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 0.9rem;">
                            PayNoi
                        </a>
                    </div>
                </div>

            </div>

            <!-- Quick Stats -->
            <div class="stats-section" style="margin-top: 3rem;">
                <h3 style="color: #FFD700; margin-bottom: 1.5rem;">📊 สถิติด่วน</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    
                    <?php
                    // Get quick stats
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users");
                        $total_users = $stmt->fetch()['total_users'];
                        
                        $stmt = $pdo->query("SELECT COUNT(*) as active_subscriptions FROM payment_transaction WHERE status = 'verified' AND expires_at > NOW()");
                        $active_subscriptions = $stmt->fetch()['active_subscriptions'];
                        
                        $stmt = $pdo->query("SELECT COUNT(*) as pending_payments FROM payment_transaction WHERE status = 'pending'");
                        $pending_payments = $stmt->fetch()['pending_payments'];
                        
                        $stmt = $pdo->query("SELECT COUNT(*) as total_packages FROM packages");
                        $total_packages = $stmt->fetch()['total_packages'];
                    } catch (Exception $e) {
                        $total_users = $active_subscriptions = $pending_payments = $total_packages = 0;
                    }
                    ?>
                    
                    <div class="stat-card" style="background: rgba(40, 167, 69, 0.1); padding: 1.5rem; border-radius: 8px; border: 1px solid #28a745; text-align: center;">
                        <div style="font-size: 2rem; color: #28a745; margin-bottom: 0.5rem;"><?php echo $total_users; ?></div>
                        <div style="color: #ccc;">ผู้ใช้ทั้งหมด</div>
                    </div>
                    
                    <div class="stat-card" style="background: rgba(255, 215, 0, 0.1); padding: 1.5rem; border-radius: 8px; border: 1px solid #FFD700; text-align: center;">
                        <div style="font-size: 2rem; color: #FFD700; margin-bottom: 0.5rem;"><?php echo $active_subscriptions; ?></div>
                        <div style="color: #ccc;">สมาชิกที่ใช้งาน</div>
                    </div>
                    
                    <div class="stat-card" style="background: rgba(255, 193, 7, 0.1); padding: 1.5rem; border-radius: 8px; border: 1px solid #ffc107; text-align: center;">
                        <div style="font-size: 2rem; color: #ffc107; margin-bottom: 0.5rem;"><?php echo $pending_payments; ?></div>
                        <div style="color: #ccc;">รอตรวจสอบ</div>
                    </div>
                    
                    <div class="stat-card" style="background: rgba(23, 162, 184, 0.1); padding: 1.5rem; border-radius: 8px; border: 1px solid #17a2b8; text-align: center;">
                        <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 0.5rem;"><?php echo $total_packages; ?></div>
                        <div style="color: #ccc;">แพ็คเกจทั้งหมด</div>
                    </div>
                    
                </div>
            </div>

        </div>
    </main>

    <script src="../assets/js/script.js"></script>
</body>
</html>
