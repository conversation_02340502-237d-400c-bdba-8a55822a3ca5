<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

$subscription_id = (int)($_GET['subscription_id'] ?? 0);

echo "<h2>🔍 Debug Payment Search</h2>";
echo "<p>User ID: {$_SESSION['user_id']}</p>";
echo "<p>Subscription ID: {$subscription_id}</p>";

try {
    // Get subscription details
    $stmt = $pdo->prepare("
        SELECT us.*, p.name as package_name, p.duration_days, p.price, p.description,
               pt.transaction_ref, pt.qr_code_url, pt.slip_image, pt.status as payment_status
        FROM user_subscriptions us
        JOIN packages p ON us.package_id = p.id
        LEFT JOIN payment_transactions pt ON us.id = pt.subscription_id
        WHERE us.id = ? AND us.user_id = ?
    ");
    $stmt->execute([$subscription_id, $_SESSION['user_id']]);
    $subscription = $stmt->fetch();

    if (!$subscription) {
        echo "<p>❌ ไม่พบ subscription</p>";
        exit();
    }

    echo "<h3>📋 Subscription Details:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($subscription as $key => $value) {
        echo "<tr><td>{$key}</td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
    }
    echo "</table>";

    echo "<h3>💰 All Payment Transactions for this user:</h3>";
    $stmt = $pdo->prepare("
        SELECT pt.*, us.id as subscription_id, p.name as package_name, p.price as package_price
        FROM payment_transactions pt
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.user_id = ?
        ORDER BY pt.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $all_payments = $stmt->fetchAll();

    if ($all_payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Subscription ID</th><th>Amount</th><th>Package Price</th><th>Status</th><th>Method</th><th>Slip Image</th><th>Created</th></tr>";
        foreach ($all_payments as $payment) {
            $highlight = ($payment['status'] === 'pending') ? 'style="background-color: yellow;"' : '';
            echo "<tr {$highlight}>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . $payment['subscription_id'] . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . ($payment['package_price'] ?? 'N/A') . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . $payment['payment_method'] . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅' : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ ไม่พบ payment transactions</p>";
    }

    echo "<h3>🔍 Search for pending payment (current logic):</h3>";
    echo "<p>Searching for: user_id = {$_SESSION['user_id']}, amount = {$subscription['price']}, status = 'pending'</p>";
    
    $stmt = $pdo->prepare("
        SELECT id, amount, status, created_at FROM payment_transactions
        WHERE user_id = ? AND amount = ? AND status = 'pending'
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute([$_SESSION['user_id'], $subscription['price']]);
    $payment_transaction = $stmt->fetch();

    if ($payment_transaction) {
        echo "<p>✅ Found payment transaction:</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($payment_transaction as $key => $value) {
            echo "<tr><td>{$key}</td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ ไม่พบ payment transaction ที่ตรงกับเงื่อนไข</p>";
    }

    echo "<h3>🔍 Alternative search (by subscription_id):</h3>";
    $stmt = $pdo->prepare("
        SELECT id, amount, status, created_at FROM payment_transactions
        WHERE user_id = ? AND subscription_id = ? AND status = 'pending'
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute([$_SESSION['user_id'], $subscription_id]);
    $alt_payment = $stmt->fetch();

    if ($alt_payment) {
        echo "<p>✅ Found payment transaction by subscription_id:</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($alt_payment as $key => $value) {
            echo "<tr><td>{$key}</td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ ไม่พบ payment transaction โดยใช้ subscription_id</p>";
    }

    echo "<h3>🔍 Search for any pending payment for this user:</h3>";
    $stmt = $pdo->prepare("
        SELECT id, amount, status, subscription_id, created_at FROM payment_transactions
        WHERE user_id = ? AND status = 'pending'
        ORDER BY created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $any_pending = $stmt->fetchAll();

    if ($any_pending) {
        echo "<p>✅ Found pending payments:</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Amount</th><th>Status</th><th>Subscription ID</th><th>Created</th></tr>";
        foreach ($any_pending as $payment) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . $payment['subscription_id'] . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ ไม่พบ pending payments เลย</p>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/payment?subscription_id={$subscription_id}'>← กลับไปหน้า Payment</a></p>";
echo "<p><a href='/packages'>← กลับไปหน้า Packages</a></p>";
?>
