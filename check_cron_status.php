<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🔍 Cron Jobs Status Check</h2>";

try {
    echo "<h3>📊 System Information:</h3>";
    echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>Server: " . ($_SERVER['HTTP_HOST'] ?? 'localhost') . "</p>";
    echo "<p>PHP Version: " . PHP_VERSION . "</p>";
    
    echo "<h3>📁 Cron Files Check:</h3>";
    $cronFiles = [
        'cron/linux_manual_check.php',
        'cron/linux_payment_check.php',
        'cron/linux_expiration_check.php',
        'cron/linux_health_check.php',
        'cron/linux_crontab.txt'
    ];
    
    foreach ($cronFiles as $file) {
        $exists = file_exists($file);
        $size = $exists ? filesize($file) : 0;
        $modified = $exists ? date('Y-m-d H:i:s', filemtime($file)) : 'N/A';
        
        echo "<p>{$file}: " . ($exists ? '✅ EXISTS' : '❌ MISSING') . " (Size: {$size} bytes, Modified: {$modified})</p>";
    }
    
    echo "<h3>📝 Log Files Check:</h3>";
    $logFiles = [
        '/var/log/jellyfin/manual_cron.log',
        '/var/log/jellyfin/payment_cron.log',
        '/var/log/jellyfin/expiration_cron.log',
        '/var/log/jellyfin/health_cron.log'
    ];
    
    foreach ($logFiles as $logFile) {
        // Try to read last few lines if accessible
        echo "<h4>{$logFile}:</h4>";
        
        if (file_exists($logFile) && is_readable($logFile)) {
            $lines = file($logFile);
            $lastLines = array_slice($lines, -10); // Last 10 lines
            echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
            foreach ($lastLines as $line) {
                echo htmlspecialchars($line);
            }
            echo "</pre>";
        } else {
            echo "<p>❌ Log file not accessible or doesn't exist</p>";
        }
    }
    
    echo "<h3>🔄 Recent Cron Activity (Database):</h3>";
    
    // Check for recent payment updates
    $stmt = $pdo->prepare("
        SELECT 'payment_transactions' as table_name, 
               COUNT(*) as count,
               MAX(updated_at) as last_update
        FROM payment_transactions 
        WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        
        UNION ALL
        
        SELECT 'user_subscriptions' as table_name,
               COUNT(*) as count,
               MAX(updated_at) as last_update
        FROM user_subscriptions 
        WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $stmt->execute();
    $activity = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Table</th><th>Updates (Last Hour)</th><th>Last Update</th></tr>";
    foreach ($activity as $row) {
        echo "<tr>";
        echo "<td>" . $row['table_name'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "<td>" . ($row['last_update'] ?? 'None') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Manual Cron Test:</h3>";
    echo "<p>Test if manual check cron logic works:</p>";
    
    // Simulate manual check
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
        ORDER BY pt.created_at ASC
        LIMIT 5
    ");
    $stmt->execute();
    $testPayments = $stmt->fetchAll();
    
    echo "<p>Found " . count($testPayments) . " payments for manual check</p>";
    
    if ($testPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Slip</th><th>Age</th></tr>";
        foreach ($testPayments as $payment) {
            $age = (time() - strtotime($payment['created_at'])) / 3600;
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅' : '❌') . "</td>";
            echo "<td>" . round($age, 1) . "h</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🔧 Cron Commands:</h3>";
    echo "<p>To check if cron is running on Linux server:</p>";
    echo "<pre style='background: #f5f5f5; padding: 10px;'>";
    echo "# Check cron service status\n";
    echo "sudo systemctl status cron\n\n";
    echo "# Check current crontab\n";
    echo "crontab -l\n\n";
    echo "# Check cron logs\n";
    echo "sudo tail -f /var/log/syslog | grep CRON\n\n";
    echo "# Manual test run\n";
    echo "cd /var/www/html\n";
    echo "php cron/linux_manual_check.php\n";
    echo "</pre>";
    
    echo "<h3>🛠️ Actions:</h3>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='test_manual_cron' style='background: blue; color: white; padding: 10px; border: none; cursor: pointer; margin: 5px;'>Test Manual Cron Logic</button>";
    echo "<button type='submit' name='test_payment_cron' style='background: green; color: white; padding: 10px; border: none; cursor: pointer; margin: 5px;'>Test Payment Cron Logic</button>";
    echo "</form>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle manual tests
if ($_POST) {
    if (isset($_POST['test_manual_cron'])) {
        echo "<h3>🧪 Testing Manual Cron Logic:</h3>";
        try {
            // Include the manual check logic
            ob_start();
            include 'cron/linux_manual_check.php';
            $output = ob_get_clean();
            
            echo "<pre style='background: #f0f8ff; padding: 10px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars($output);
            echo "</pre>";
            
        } catch (Exception $e) {
            echo "<p>❌ Manual cron test error: " . $e->getMessage() . "</p>";
        }
    }
    
    if (isset($_POST['test_payment_cron'])) {
        echo "<h3>🧪 Testing Payment Cron Logic:</h3>";
        try {
            // Include the payment check logic
            ob_start();
            include 'cron/linux_payment_check.php';
            $output = ob_get_clean();
            
            echo "<pre style='background: #f0fff0; padding: 10px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars($output);
            echo "</pre>";
            
        } catch (Exception $e) {
            echo "<p>❌ Payment cron test error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<hr>";
echo "<p><a href='/admin'>← กลับไปหน้า Admin</a></p>";
echo "<p><a href='/debug_slip_verification.php'>🔍 Debug Slip Verification</a></p>";
?>
