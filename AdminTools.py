#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jellyfin Admin Tools v2.0
ระบบจัดการ Admin สำหรับ Jellyfin Registration System
เชื่อมต่อกับฐานข้อมูล jellyfin_registration โดยตรง
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import mysql.connector
import requests
import json
import threading
import time
from datetime import datetime, timedelta
import hashlib
import bcrypt

class JellyfinAdminTools:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Jellyfin Admin Tools v2.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # Database configuration
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Wxmujwsofu@1234',
            'database': 'jellyfin_registration',
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci'
        }
        
        # API configuration
        self.api_base_url = "http://localhost"
        
        # State variables
        self.is_logged_in = False
        self.admin_user = None
        self.db_connection = None
        
        # Initialize UI
        self.setup_styles()
        self.create_login_interface()
        self.check_database_connection()
        
    def setup_styles(self):
        """ตั้งค่า styles สำหรับ UI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors
        style.configure('Title.TLabel', 
                       background='#2c3e50', 
                       foreground='#ecf0f1', 
                       font=('Arial', 16, 'bold'))
        
        style.configure('Subtitle.TLabel', 
                       background='#2c3e50', 
                       foreground='#bdc3c7', 
                       font=('Arial', 10))
        
        style.configure('Success.TLabel', 
                       background='#2c3e50', 
                       foreground='#27ae60', 
                       font=('Arial', 10, 'bold'))
        
        style.configure('Error.TLabel', 
                       background='#2c3e50', 
                       foreground='#e74c3c', 
                       font=('Arial', 10, 'bold'))
        
        style.configure('Warning.TLabel', 
                       background='#2c3e50', 
                       foreground='#f39c12', 
                       font=('Arial', 10, 'bold'))
    
    def get_db_connection(self):
        """สร้างการเชื่อมต่อฐานข้อมูล"""
        try:
            if self.db_connection is None or not self.db_connection.is_connected():
                self.db_connection = mysql.connector.connect(**self.db_config)
            return self.db_connection
        except mysql.connector.Error as e:
            print(f"Database connection error: {e}")
            return None
    
    def close_db_connection(self):
        """ปิดการเชื่อมต่อฐานข้อมูล"""
        if self.db_connection and self.db_connection.is_connected():
            self.db_connection.close()
            self.db_connection = None
    
    def check_database_connection(self):
        """ตรวจสอบการเชื่อมต่อฐานข้อมูล"""
        try:
            conn = self.get_db_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                cursor.close()
                
                if hasattr(self, 'connection_status_label'):
                    self.connection_status_label.config(
                        text=f"✅ เชื่อมต่อฐานข้อมูลสำเร็จ ({user_count} users)",
                        style='Success.TLabel'
                    )
                return True
        except Exception as e:
            print(f"Database connection failed: {e}")
            if hasattr(self, 'connection_status_label'):
                self.connection_status_label.config(
                    text="❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้",
                    style='Error.TLabel'
                )
            return False
    
    def verify_password(self, plain_password, stored_password):
        """ตรวจสอบ password รองรับ password_hash และ plain text"""
        if not plain_password or not stored_password:
            return False
        
        # 1. ลอง PHP password_hash (bcrypt) ก่อน
        try:
            if stored_password.startswith('$2y$') or stored_password.startswith('$2b$') or stored_password.startswith('$2a$'):
                # แปลง $2y$ เป็น $2b$ สำหรับ Python bcrypt
                test_hash = stored_password.replace('$2y$', '$2b$').replace('$2a$', '$2b$')
                return bcrypt.checkpw(plain_password.encode('utf-8'), test_hash.encode('utf-8'))
        except Exception as e:
            print(f"bcrypt verification error: {e}")
        
        # 2. ลอง plain text
        if plain_password == stored_password:
            return True
        
        # 3. ลอง MD5
        try:
            if hashlib.md5(plain_password.encode('utf-8')).hexdigest() == stored_password:
                return True
        except:
            pass
        
        # 4. ลอง SHA256
        try:
            if hashlib.sha256(plain_password.encode('utf-8')).hexdigest() == stored_password:
                return True
        except:
            pass
        
        return False
    
    def create_login_interface(self):
        """สร้างหน้า login"""
        # Clear existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔧 Jellyfin Admin Tools v2.0", style='Title.TLabel')
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="ระบบจัดการ Admin สำหรับ Jellyfin Registration", style='Subtitle.TLabel')
        subtitle_label.pack(pady=(0, 20))
        
        # Connection status
        self.connection_status_label = ttk.Label(main_frame, text="🔄 กำลังตรวจสอบการเชื่อมต่อ...", style='Warning.TLabel')
        self.connection_status_label.pack(pady=(0, 20))
        
        # Login form
        login_frame = tk.Frame(main_frame, bg='#34495e', relief='raised', bd=2)
        login_frame.pack(pady=20, padx=100, fill='x')
        
        form_title = ttk.Label(login_frame, text="🔐 เข้าสู่ระบบ", style='Title.TLabel')
        form_title.pack(pady=20)
        
        # Username
        tk.Label(login_frame, text="Username:", bg='#34495e', fg='#ecf0f1', font=('Arial', 12)).pack(pady=(10, 5))
        self.username_entry = tk.Entry(login_frame, font=('Arial', 12), width=30)
        self.username_entry.pack(pady=(0, 10))
        
        # Password
        tk.Label(login_frame, text="Password:", bg='#34495e', fg='#ecf0f1', font=('Arial', 12)).pack(pady=(10, 5))
        self.password_entry = tk.Entry(login_frame, font=('Arial', 12), width=30, show='*')
        self.password_entry.pack(pady=(0, 10))
        
        # Login button
        login_btn = tk.Button(login_frame, text="🚀 เข้าสู่ระบบ", 
                             command=self.login, 
                             bg='#3498db', fg='white', 
                             font=('Arial', 12, 'bold'),
                             padx=20, pady=10)
        login_btn.pack(pady=20)
        
        # Status label
        self.login_status_label = ttk.Label(login_frame, text="", style='Subtitle.TLabel')
        self.login_status_label.pack(pady=(0, 20))
        
        # Bind Enter key
        self.root.bind('<Return>', lambda event: self.login())
        
        # Focus on username
        self.username_entry.focus()
    
    def login(self):
        """ทำการ login"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            self.login_status_label.config(text="❌ กรุณาใส่ username และ password", style='Error.TLabel')
            return
        
        self.login_status_label.config(text="🔄 กำลังเข้าสู่ระบบ...", style='Warning.TLabel')
        self.root.update()
        
        try:
            conn = self.get_db_connection()
            if not conn:
                self.login_status_label.config(text="❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้", style='Error.TLabel')
                return
            
            cursor = conn.cursor()
            cursor.execute("SELECT id, username, password_hash, is_admin FROM users WHERE username = %s", (username,))
            user = cursor.fetchone()
            cursor.close()

            if user:
                user_id, db_username, db_password_hash, is_admin = user
                
                # ตรวจสอบ password
                if self.verify_password(password, db_password_hash):
                    # ตรวจสอบ admin status
                    if is_admin == 1:
                        self.admin_user = {
                            'id': user_id,
                            'username': db_username,
                            'is_admin': is_admin
                        }
                        self.is_logged_in = True
                        self.login_status_label.config(text="✅ เข้าสู่ระบบสำเร็จ!", style='Success.TLabel')
                        self.root.after(1000, self.create_main_interface)
                        return
                    else:
                        self.login_status_label.config(text="❌ ไม่มีสิทธิ์ Admin (is_admin ต้องเป็น 1)", style='Error.TLabel')
                else:
                    self.login_status_label.config(text="❌ Password ไม่ถูกต้อง", style='Error.TLabel')
            else:
                self.login_status_label.config(text="❌ ไม่พบ Username นี้ในระบบ", style='Error.TLabel')
                
        except Exception as e:
            print(f"Login error: {e}")
            self.login_status_label.config(text=f"❌ เกิดข้อผิดพลาด: {str(e)}", style='Error.TLabel')

    def create_main_interface(self):
        """สร้างหน้าจอหลัก"""
        # Clear existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()

        # Main container
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True)

        # Header
        header_frame = tk.Frame(main_frame, bg='#34495e', height=80)
        header_frame.pack(fill='x', padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)

        # Title and user info
        title_label = ttk.Label(header_frame, text="🔧 Jellyfin Admin Tools v2.0", style='Title.TLabel')
        title_label.pack(side='left', padx=20, pady=20)

        user_info = ttk.Label(header_frame, text=f"👤 {self.admin_user['username']} (Admin)", style='Subtitle.TLabel')
        user_info.pack(side='right', padx=20, pady=20)

        # Logout button
        logout_btn = tk.Button(header_frame, text="🚪 ออกจากระบบ",
                              command=self.logout,
                              bg='#e74c3c', fg='white',
                              font=('Arial', 10))
        logout_btn.pack(side='right', padx=(0, 20), pady=20)

        # Content area
        content_frame = tk.Frame(main_frame, bg='#2c3e50')
        content_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill='both', expand=True)

        # Create tabs
        self.create_dashboard_tab()
        self.create_users_tab()
        self.create_payments_tab()
        self.create_packages_tab()
        self.create_tools_tab()

    def create_dashboard_tab(self):
        """สร้างแท็บ Dashboard"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")

        # Statistics
        stats_frame = tk.Frame(dashboard_frame, bg='#ecf0f1', relief='raised', bd=2)
        stats_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(stats_frame, text="📈 สถิติระบบ", bg='#ecf0f1', font=('Arial', 14, 'bold')).pack(pady=10)

        # Get statistics from database
        self.update_dashboard_stats(stats_frame)

        # Refresh button
        refresh_btn = tk.Button(stats_frame, text="🔄 รีเฟรช",
                               command=lambda: self.update_dashboard_stats(stats_frame),
                               bg='#3498db', fg='white', font=('Arial', 10))
        refresh_btn.pack(pady=10)

    def update_dashboard_stats(self, parent_frame):
        """อัปเดตสถิติใน Dashboard"""
        try:
            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor()

            # Get user statistics
            cursor.execute("SELECT COUNT(*) FROM users")
            total_users = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
            admin_users = cursor.fetchone()[0]

            # Get payment statistics
            cursor.execute("SELECT COUNT(*) FROM payments")
            total_payments = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM payments WHERE status = 'verified'")
            verified_payments = cursor.fetchone()[0]

            cursor.execute("SELECT SUM(amount) FROM payments WHERE status = 'verified'")
            total_revenue = cursor.fetchone()[0] or 0

            # Get package statistics
            cursor.execute("SELECT COUNT(*) FROM packages WHERE is_active = 1")
            active_packages = cursor.fetchone()[0]

            cursor.close()

            # Clear existing stats
            for widget in parent_frame.winfo_children():
                if isinstance(widget, tk.Frame):
                    widget.destroy()

            # Create stats display
            stats_container = tk.Frame(parent_frame, bg='#ecf0f1')
            stats_container.pack(fill='x', padx=20, pady=10)

            # Users stats
            user_frame = tk.Frame(stats_container, bg='#3498db', relief='raised', bd=2)
            user_frame.pack(side='left', fill='both', expand=True, padx=5)

            tk.Label(user_frame, text="👥 ผู้ใช้", bg='#3498db', fg='white', font=('Arial', 12, 'bold')).pack(pady=5)
            tk.Label(user_frame, text=f"ทั้งหมด: {total_users}", bg='#3498db', fg='white').pack()
            tk.Label(user_frame, text=f"Admin: {admin_users}", bg='#3498db', fg='white').pack(pady=(0, 20))

            # Payments stats
            payment_frame = tk.Frame(stats_container, bg='#27ae60', relief='raised', bd=2)
            payment_frame.pack(side='left', fill='both', expand=True, padx=5)

            tk.Label(payment_frame, text="💰 การชำระเงิน", bg='#27ae60', fg='white', font=('Arial', 12, 'bold')).pack(pady=5)
            tk.Label(payment_frame, text=f"ทั้งหมด: {total_payments}", bg='#27ae60', fg='white').pack()
            tk.Label(payment_frame, text=f"ยืนยันแล้ว: {verified_payments}", bg='#27ae60', fg='white').pack()
            tk.Label(payment_frame, text=f"รายได้: {total_revenue:,.2f} บาท", bg='#27ae60', fg='white').pack(pady=(0, 10))

            # Packages stats
            package_frame = tk.Frame(stats_container, bg='#e67e22', relief='raised', bd=2)
            package_frame.pack(side='left', fill='both', expand=True, padx=5)

            tk.Label(package_frame, text="📦 แพ็กเกจ", bg='#e67e22', fg='white', font=('Arial', 12, 'bold')).pack(pady=5)
            tk.Label(package_frame, text=f"ใช้งานได้: {active_packages}", bg='#e67e22', fg='white').pack(pady=(0, 30))

        except Exception as e:
            print(f"Dashboard stats error: {e}")

    def create_users_tab(self):
        """สร้างแท็บ Users"""
        users_frame = ttk.Frame(self.notebook)
        self.notebook.add(users_frame, text="👥 Users")

        # Search frame
        search_frame = tk.Frame(users_frame, bg='#ecf0f1')
        search_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(search_frame, text="🔍 ค้นหา:", bg='#ecf0f1', font=('Arial', 10)).pack(side='left', padx=5)
        self.user_search_entry = tk.Entry(search_frame, font=('Arial', 10), width=30)
        self.user_search_entry.pack(side='left', padx=5)

        search_btn = tk.Button(search_frame, text="ค้นหา",
                              command=self.search_users,
                              bg='#3498db', fg='white', font=('Arial', 10))
        search_btn.pack(side='left', padx=5)

        refresh_btn = tk.Button(search_frame, text="🔄 รีเฟรช",
                               command=self.load_users,
                               bg='#27ae60', fg='white', font=('Arial', 10))
        refresh_btn.pack(side='left', padx=5)

        reset_password_btn = tk.Button(search_frame, text="🔑 Reset Password",
                                      command=self.reset_user_password,
                                      bg='#e67e22', fg='white', font=('Arial', 10))
        reset_password_btn.pack(side='left', padx=5)

        bulk_reset_btn = tk.Button(search_frame, text="🔑 Bulk Reset",
                                  command=self.bulk_reset_passwords,
                                  bg='#8e44ad', fg='white', font=('Arial', 10))
        bulk_reset_btn.pack(side='left', padx=5)

        # Help text
        help_text = "💡 คำแนะนำ: Right-click สำหรับเมนู | F3=Reset Password"
        help_label = tk.Label(search_frame, text=help_text, bg='#ecf0f1', fg='#7f8c8d', font=('Arial', 9))
        help_label.pack(side='right', padx=10)

        # Users table
        table_frame = tk.Frame(users_frame)
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create treeview
        columns = ('ID', 'Username', 'Phone', 'Admin', 'Created')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=100)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.users_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.users_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Create context menu for users
        self.users_context_menu = tk.Menu(self.root, tearoff=0)
        self.users_context_menu.add_command(label="🔑 Reset Password", command=self.reset_user_password)
        self.users_context_menu.add_command(label="🔑 Bulk Reset", command=self.bulk_reset_passwords)
        self.users_context_menu.add_separator()
        self.users_context_menu.add_command(label="🔄 รีเฟรช", command=self.load_users)

        # Bind right-click event
        self.users_tree.bind('<Button-3>', self.show_users_context_menu)

        # Bind keyboard shortcuts
        self.users_tree.bind('<F3>', lambda event: self.reset_user_password())

        # Load users data
        self.load_users()

    def load_users(self):
        """โหลดข้อมูล users จากฐานข้อมูล"""
        try:
            # Clear existing data
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor()
            query = """
            SELECT u.id, u.username, u.phone, u.is_admin, u.created_at
            FROM users u
            ORDER BY u.created_at DESC
            """
            cursor.execute(query)
            users = cursor.fetchall()
            cursor.close()

            # Insert data into treeview
            for user in users:
                user_id, username, phone, is_admin, created_at = user

                admin_text = "Yes" if is_admin == 1 else "No"
                created_text = created_at.strftime("%Y-%m-%d") if created_at else "None"

                self.users_tree.insert('', 'end', values=(
                    user_id, username, phone or "None", admin_text, created_text
                ))

        except Exception as e:
            print(f"Load users error: {e}")
            messagebox.showerror("Error", f"ไม่สามารถโหลดข้อมูล users ได้: {str(e)}")

    def search_users(self):
        """ค้นหา users"""
        search_term = self.user_search_entry.get().strip()
        if not search_term:
            self.load_users()
            return

        try:
            # Clear existing data
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor()
            query = """
            SELECT u.id, u.username, u.phone, u.is_admin, u.created_at
            FROM users u
            WHERE u.username LIKE %s OR u.phone LIKE %s
            ORDER BY u.created_at DESC
            """
            cursor.execute(query, (f"%{search_term}%", f"%{search_term}%"))
            users = cursor.fetchall()
            cursor.close()

            # Insert data into treeview
            for user in users:
                user_id, username, phone, is_admin, created_at = user

                admin_text = "Yes" if is_admin == 1 else "No"
                created_text = created_at.strftime("%Y-%m-%d") if created_at else "None"

                self.users_tree.insert('', 'end', values=(
                    user_id, username, phone or "None", admin_text, created_text
                ))

        except Exception as e:
            print(f"Search users error: {e}")
            messagebox.showerror("Error", f"ไม่สามารถค้นหา users ได้: {str(e)}")

    def reset_user_password(self):
        """Reset password ของผู้ใช้"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "กรุณาเลือกผู้ใช้ที่ต้องการ reset password")
            return

        item = self.users_tree.item(selected[0])
        user_data = item['values']
        user_id = user_data[0]
        username = user_data[1]

        self.password_reset_dialog(user_id, username)

    def password_reset_dialog(self, user_id, username):
        """แสดง dialog สำหรับ reset password"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Reset Password - {username}")
        dialog.geometry("450x300")
        dialog.configure(bg='#ecf0f1')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Title
        title_label = tk.Label(dialog, text=f"🔑 Reset Password", bg='#ecf0f1', font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)

        user_label = tk.Label(dialog, text=f"ผู้ใช้: {username}", bg='#ecf0f1', font=('Arial', 12))
        user_label.pack(pady=5)

        # Form frame
        form_frame = tk.Frame(dialog, bg='#ecf0f1')
        form_frame.pack(padx=40, pady=20, fill='both', expand=True)

        # New password
        tk.Label(form_frame, text="Password ใหม่:", bg='#ecf0f1', font=('Arial', 12)).pack(anchor='w', pady=(10, 5))
        password_entry = tk.Entry(form_frame, font=('Arial', 12), width=30, show='*')
        password_entry.pack(fill='x', pady=(0, 10))

        # Confirm password
        tk.Label(form_frame, text="ยืนยัน Password:", bg='#ecf0f1', font=('Arial', 12)).pack(anchor='w', pady=(10, 5))
        confirm_entry = tk.Entry(form_frame, font=('Arial', 12), width=30, show='*')
        confirm_entry.pack(fill='x', pady=(0, 10))

        # Show password checkbox
        show_password_var = tk.BooleanVar()
        show_check = tk.Checkbutton(form_frame, text="แสดง Password", variable=show_password_var,
                                   bg='#ecf0f1', font=('Arial', 10),
                                   command=lambda: self.toggle_password_visibility(password_entry, confirm_entry, show_password_var))
        show_check.pack(anchor='w', pady=10)

        # Generate random password button
        generate_frame = tk.Frame(form_frame, bg='#ecf0f1')
        generate_frame.pack(fill='x', pady=10)

        generate_btn = tk.Button(generate_frame, text="🎲 สร้าง Password อัตโนมัติ",
                                command=lambda: self.generate_random_password(password_entry, confirm_entry),
                                bg='#9b59b6', fg='white', font=('Arial', 10))
        generate_btn.pack(side='left')

        # Buttons frame
        buttons_frame = tk.Frame(dialog, bg='#ecf0f1')
        buttons_frame.pack(pady=20)

        def reset_password():
            new_password = password_entry.get().strip()
            confirm_password = confirm_entry.get().strip()

            # Validation
            if not new_password:
                messagebox.showerror("Error", "กรุณาใส่ password ใหม่")
                return

            if len(new_password) < 6:
                messagebox.showerror("Error", "Password ต้องมีอย่างน้อย 6 ตัวอักษร")
                return

            if new_password != confirm_password:
                messagebox.showerror("Error", "Password ไม่ตรงกัน")
                return

            try:
                conn = self.get_db_connection()
                if not conn:
                    messagebox.showerror("Error", "ไม่สามารถเชื่อมต่อฐานข้อมูลได้")
                    return

                # Hash the password
                import bcrypt
                hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                cursor = conn.cursor()
                cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", (hashed_password, user_id))
                conn.commit()
                cursor.close()

                messagebox.showinfo("Success", f"Reset password สำหรับ '{username}' สำเร็จ!\n\nPassword ใหม่: {new_password}")
                dialog.destroy()

            except Exception as e:
                print(f"Reset password error: {e}")
                messagebox.showerror("Error", f"ไม่สามารถ reset password ได้: {str(e)}")

        save_btn = tk.Button(buttons_frame, text="🔑 Reset Password",
                            command=reset_password,
                            bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                            padx=20, pady=10)
        save_btn.pack(side='left', padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ ยกเลิก",
                              command=dialog.destroy,
                              bg='#95a5a6', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=10)
        cancel_btn.pack(side='left', padx=10)

        # Focus on password entry
        password_entry.focus()

    def toggle_password_visibility(self, password_entry, confirm_entry, show_var):
        """เปิด/ปิดการแสดง password"""
        if show_var.get():
            password_entry.config(show='')
            confirm_entry.config(show='')
        else:
            password_entry.config(show='*')
            confirm_entry.config(show='*')

    def generate_random_password(self, password_entry, confirm_entry):
        """สร้าง password แบบสุ่ม"""
        import random
        import string

        # Generate random password with letters, numbers, and symbols
        characters = string.ascii_letters + string.digits + "!@#$%"
        password = ''.join(random.choice(characters) for _ in range(12))

        # Set password in both entries
        password_entry.delete(0, tk.END)
        password_entry.insert(0, password)
        confirm_entry.delete(0, tk.END)
        confirm_entry.insert(0, password)

        # Show a message with the generated password
        messagebox.showinfo("Password Generated", f"Password ที่สร้างขึ้น: {password}\n\nกรุณาจดบันทึกไว้!")

    def show_users_context_menu(self, event):
        """แสดง context menu สำหรับ users"""
        try:
            # Select the item under cursor
            item = self.users_tree.identify_row(event.y)
            if item:
                self.users_tree.selection_set(item)
                self.users_context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"Users context menu error: {e}")

    def bulk_reset_passwords(self):
        """Reset password หลายผู้ใช้พร้อมกัน"""
        selected_items = self.users_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "กรุณาเลือกผู้ใช้ที่ต้องการ reset password (สามารถเลือกหลายคนได้)")
            return

        if len(selected_items) == 1:
            # If only one user selected, use regular reset
            self.reset_user_password()
            return

        users_data = []
        for item in selected_items:
            user_data = self.users_tree.item(item)['values']
            users_data.append((user_data[0], user_data[1]))  # (user_id, username)

        # Confirm bulk reset
        usernames = [user[1] for user in users_data]
        if not messagebox.askyesno("Confirm Bulk Reset",
                                  f"คุณต้องการ reset password สำหรับผู้ใช้ {len(users_data)} คน?\n\n" +
                                  "\n".join(usernames[:10]) +
                                  (f"\n... และอีก {len(usernames)-10} คน" if len(usernames) > 10 else "")):
            return

        self.bulk_reset_dialog(users_data)

    def bulk_reset_dialog(self, users_data):
        """แสดง dialog สำหรับ bulk reset password"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Bulk Reset Password ({len(users_data)} users)")
        dialog.geometry("500x400")
        dialog.configure(bg='#ecf0f1')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Title
        title_label = tk.Label(dialog, text=f"🔑 Bulk Reset Password", bg='#ecf0f1', font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)

        count_label = tk.Label(dialog, text=f"จำนวนผู้ใช้: {len(users_data)} คน", bg='#ecf0f1', font=('Arial', 12))
        count_label.pack(pady=5)

        # Form frame
        form_frame = tk.Frame(dialog, bg='#ecf0f1')
        form_frame.pack(padx=40, pady=20, fill='both', expand=True)

        # Password options
        password_option = tk.StringVar(value="auto")

        tk.Label(form_frame, text="ตัวเลือก Password:", bg='#ecf0f1', font=('Arial', 12, 'bold')).pack(anchor='w', pady=(10, 5))

        auto_radio = tk.Radiobutton(form_frame, text="สร้าง Password อัตโนมัติ (แนะนำ)",
                                   variable=password_option, value="auto",
                                   bg='#ecf0f1', font=('Arial', 11))
        auto_radio.pack(anchor='w', pady=2)

        manual_radio = tk.Radiobutton(form_frame, text="ใช้ Password เดียวกันทั้งหมด",
                                     variable=password_option, value="manual",
                                     bg='#ecf0f1', font=('Arial', 11))
        manual_radio.pack(anchor='w', pady=2)

        # Manual password entry
        manual_frame = tk.Frame(form_frame, bg='#ecf0f1')
        manual_frame.pack(fill='x', pady=10)

        tk.Label(manual_frame, text="Password (สำหรับทุกคน):", bg='#ecf0f1', font=('Arial', 10)).pack(anchor='w')
        manual_password_entry = tk.Entry(manual_frame, font=('Arial', 11), width=30, show='*')
        manual_password_entry.pack(fill='x', pady=5)

        # Show password checkbox
        show_manual_var = tk.BooleanVar()
        show_manual_check = tk.Checkbutton(manual_frame, text="แสดง Password", variable=show_manual_var,
                                          bg='#ecf0f1', font=('Arial', 9),
                                          command=lambda: manual_password_entry.config(show='' if show_manual_var.get() else '*'))
        show_manual_check.pack(anchor='w')

        # Results display
        results_frame = tk.Frame(form_frame, bg='#ecf0f1')
        results_frame.pack(fill='both', expand=True, pady=10)

        tk.Label(results_frame, text="ผลลัพธ์:", bg='#ecf0f1', font=('Arial', 12, 'bold')).pack(anchor='w')

        results_text = scrolledtext.ScrolledText(results_frame, height=8, width=50, font=('Courier', 9))
        results_text.pack(fill='both', expand=True, pady=5)

        # Buttons frame
        buttons_frame = tk.Frame(dialog, bg='#ecf0f1')
        buttons_frame.pack(pady=20)

        def execute_bulk_reset():
            try:
                conn = self.get_db_connection()
                if not conn:
                    messagebox.showerror("Error", "ไม่สามารถเชื่อมต่อฐานข้อมูลได้")
                    return

                import bcrypt
                import random
                import string

                results_text.delete('1.0', tk.END)
                results_text.insert(tk.END, "กำลังดำเนินการ...\n\n")
                results_text.update()

                cursor = conn.cursor()
                success_count = 0

                for user_id, username in users_data:
                    try:
                        if password_option.get() == "auto":
                            # Generate random password
                            characters = string.ascii_letters + string.digits + "!@#$"
                            new_password = ''.join(random.choice(characters) for _ in range(10))
                        else:
                            # Use manual password
                            new_password = manual_password_entry.get().strip()
                            if not new_password:
                                results_text.insert(tk.END, f"❌ {username}: ไม่ได้ใส่ password\n")
                                continue

                        # Hash password
                        hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                        # Update database
                        cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", (hashed_password, user_id))

                        results_text.insert(tk.END, f"✅ {username}: {new_password}\n")
                        success_count += 1

                    except Exception as e:
                        results_text.insert(tk.END, f"❌ {username}: Error - {str(e)}\n")

                    results_text.see(tk.END)
                    results_text.update()

                conn.commit()
                cursor.close()

                results_text.insert(tk.END, f"\n🎉 สำเร็จ: {success_count}/{len(users_data)} คน\n")
                results_text.see(tk.END)

                messagebox.showinfo("Success", f"Reset password สำเร็จ {success_count}/{len(users_data)} คน\n\nกรุณาบันทึก password ใหม่!")

            except Exception as e:
                print(f"Bulk reset error: {e}")
                messagebox.showerror("Error", f"เกิดข้อผิดพลาด: {str(e)}")

        reset_btn = tk.Button(buttons_frame, text="🔑 Reset All Passwords",
                             command=execute_bulk_reset,
                             bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                             padx=20, pady=10)
        reset_btn.pack(side='left', padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ ยกเลิก",
                              command=dialog.destroy,
                              bg='#95a5a6', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=10)
        cancel_btn.pack(side='left', padx=10)

    def create_payments_tab(self):
        """สร้างแท็บ Payments"""
        payments_frame = ttk.Frame(self.notebook)
        self.notebook.add(payments_frame, text="💰 Payments")

        # Search frame
        search_frame = tk.Frame(payments_frame, bg='#ecf0f1')
        search_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(search_frame, text="🔍 ค้นหา:", bg='#ecf0f1', font=('Arial', 10)).pack(side='left', padx=5)
        self.payment_search_entry = tk.Entry(search_frame, font=('Arial', 10), width=30)
        self.payment_search_entry.pack(side='left', padx=5)

        search_btn = tk.Button(search_frame, text="ค้นหา",
                              command=self.search_payments,
                              bg='#3498db', fg='white', font=('Arial', 10))
        search_btn.pack(side='left', padx=5)

        refresh_btn = tk.Button(search_frame, text="🔄 รีเฟรช",
                               command=self.load_payments,
                               bg='#27ae60', fg='white', font=('Arial', 10))
        refresh_btn.pack(side='left', padx=5)

        # Payments table
        table_frame = tk.Frame(payments_frame)
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create treeview
        columns = ('ID', 'User', 'Amount', 'Status', 'Method', 'Created', 'Verified')
        self.payments_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.payments_tree.heading(col, text=col)
            self.payments_tree.column(col, width=100)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.payments_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.payments_tree.xview)
        self.payments_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.payments_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Load payments data
        self.load_payments()

    def load_payments(self):
        """โหลดข้อมูล payments จากฐานข้อมูล"""
        try:
            # Clear existing data
            for item in self.payments_tree.get_children():
                self.payments_tree.delete(item)

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor()
            query = """
            SELECT p.id, u.username, p.amount, p.status, p.payment_method, p.created_at, p.verified_at
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.id
            ORDER BY p.created_at DESC
            """
            cursor.execute(query)
            payments = cursor.fetchall()
            cursor.close()

            # Insert data into treeview
            for payment in payments:
                payment_id, username, amount, status, method, created_at, verified_at = payment

                username_text = username or "Unknown"
                method_text = method or "None"
                created_text = created_at.strftime("%Y-%m-%d %H:%M") if created_at else "None"
                verified_text = verified_at.strftime("%Y-%m-%d %H:%M") if verified_at else "None"

                self.payments_tree.insert('', 'end', values=(
                    payment_id, username_text, f"{amount:,.2f}",
                    status, method_text, created_text, verified_text
                ))

        except Exception as e:
            print(f"Load payments error: {e}")
            messagebox.showerror("Error", f"ไม่สามารถโหลดข้อมูล payments ได้: {str(e)}")

    def search_payments(self):
        """ค้นหา payments"""
        search_term = self.payment_search_entry.get().strip()
        if not search_term:
            self.load_payments()
            return

        try:
            # Clear existing data
            for item in self.payments_tree.get_children():
                self.payments_tree.delete(item)

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor()
            query = """
            SELECT p.id, u.username, p.amount, p.status, p.payment_method, p.created_at, p.verified_at
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.id
            WHERE u.username LIKE %s OR p.status LIKE %s
            ORDER BY p.created_at DESC
            """
            cursor.execute(query, (f"%{search_term}%", f"%{search_term}%"))
            payments = cursor.fetchall()
            cursor.close()

            # Insert data into treeview
            for payment in payments:
                payment_id, username, amount, status, method, created_at, verified_at = payment

                username_text = username or "Unknown"
                method_text = method or "None"
                created_text = created_at.strftime("%Y-%m-%d %H:%M") if created_at else "None"
                verified_text = verified_at.strftime("%Y-%m-%d %H:%M") if verified_at else "None"

                self.payments_tree.insert('', 'end', values=(
                    payment_id, username_text, f"{amount:,.2f}",
                    status, method_text, created_text, verified_text
                ))

        except Exception as e:
            print(f"Search payments error: {e}")
            messagebox.showerror("Error", f"ไม่สามารถค้นหา payments ได้: {str(e)}")

    def create_packages_tab(self):
        """สร้างแท็บ Packages"""
        packages_frame = ttk.Frame(self.notebook)
        self.notebook.add(packages_frame, text="📦 Packages")

        # Control frame
        control_frame = tk.Frame(packages_frame, bg='#ecf0f1')
        control_frame.pack(fill='x', padx=10, pady=10)

        # Left side buttons
        left_frame = tk.Frame(control_frame, bg='#ecf0f1')
        left_frame.pack(side='left')

        refresh_btn = tk.Button(left_frame, text="🔄 รีเฟรช",
                               command=self.load_packages,
                               bg='#27ae60', fg='white', font=('Arial', 10))
        refresh_btn.pack(side='left', padx=5)

        add_btn = tk.Button(left_frame, text="➕ เพิ่มแพ็กเกจ",
                           command=self.add_package,
                           bg='#3498db', fg='white', font=('Arial', 10))
        add_btn.pack(side='left', padx=5)

        edit_btn = tk.Button(left_frame, text="✏️ แก้ไข",
                            command=self.edit_package,
                            bg='#f39c12', fg='white', font=('Arial', 10))
        edit_btn.pack(side='left', padx=5)

        delete_btn = tk.Button(left_frame, text="🗑️ ลบ",
                              command=self.delete_package,
                              bg='#e74c3c', fg='white', font=('Arial', 10))
        delete_btn.pack(side='left', padx=5)

        # Help text
        help_frame = tk.Frame(control_frame, bg='#ecf0f1')
        help_frame.pack(side='right')

        help_text = "💡 คำแนะนำ: Double-click เพื่อแก้ไข | Right-click สำหรับเมนู | Del=ลบ | F2=แก้ไข | Insert=เพิ่ม"
        help_label = tk.Label(help_frame, text=help_text, bg='#ecf0f1', fg='#7f8c8d', font=('Arial', 9))
        help_label.pack()

        # Packages table
        table_frame = tk.Frame(packages_frame)
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create treeview
        columns = ('ID', 'Name', 'Price', 'Duration', 'Description', 'Active', 'Created')
        self.packages_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.packages_tree.heading(col, text=col)
            self.packages_tree.column(col, width=120)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.packages_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.packages_tree.xview)
        self.packages_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.packages_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Bind double-click event
        self.packages_tree.bind('<Double-1>', lambda event: self.edit_package())

        # Create context menu
        self.packages_context_menu = tk.Menu(self.root, tearoff=0)
        self.packages_context_menu.add_command(label="✏️ แก้ไข", command=self.edit_package)
        self.packages_context_menu.add_command(label="🗑️ ลบ", command=self.delete_package)
        self.packages_context_menu.add_separator()
        self.packages_context_menu.add_command(label="➕ เพิ่มแพ็กเกจใหม่", command=self.add_package)

        # Bind right-click event
        self.packages_tree.bind('<Button-3>', self.show_packages_context_menu)

        # Bind keyboard shortcuts
        self.packages_tree.bind('<Delete>', lambda event: self.delete_package())
        self.packages_tree.bind('<F2>', lambda event: self.edit_package())
        self.packages_tree.bind('<Insert>', lambda event: self.add_package())

        # Load packages data
        self.load_packages()

    def load_packages(self):
        """โหลดข้อมูล packages จากฐานข้อมูล"""
        try:
            # Clear existing data
            for item in self.packages_tree.get_children():
                self.packages_tree.delete(item)

            conn = self.get_db_connection()
            if not conn:
                return

            cursor = conn.cursor()
            query = """
            SELECT id, name, price, duration_days, description, is_active, created_at
            FROM packages
            ORDER BY created_at DESC
            """
            cursor.execute(query)
            packages = cursor.fetchall()
            cursor.close()

            # Insert data into treeview
            for package in packages:
                pkg_id, name, price, duration, description, is_active, created_at = package

                active_text = "Yes" if is_active == 1 else "No"
                description_text = (description[:50] + "...") if description and len(description) > 50 else (description or "None")
                created_text = created_at.strftime("%Y-%m-%d") if created_at else "None"

                self.packages_tree.insert('', 'end', values=(
                    pkg_id, name, f"{price:,.2f}", f"{duration} days",
                    description_text, active_text, created_text
                ))

        except Exception as e:
            print(f"Load packages error: {e}")
            messagebox.showerror("Error", f"ไม่สามารถโหลดข้อมูล packages ได้: {str(e)}")

    def add_package(self):
        """เพิ่มแพ็กเกจใหม่"""
        self.package_dialog(mode='add')

    def edit_package(self):
        """แก้ไขแพ็กเกจ"""
        selected = self.packages_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "กรุณาเลือกแพ็กเกจที่ต้องการแก้ไข")
            return

        item = self.packages_tree.item(selected[0])
        package_data = item['values']
        self.package_dialog(mode='edit', package_data=package_data)

    def delete_package(self):
        """ลบแพ็กเกจ"""
        selected = self.packages_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "กรุณาเลือกแพ็กเกจที่ต้องการลบ")
            return

        item = self.packages_tree.item(selected[0])
        package_data = item['values']
        package_id = package_data[0]
        package_name = package_data[1]

        if messagebox.askyesno("Confirm", f"คุณต้องการลบแพ็กเกจ '{package_name}' หรือไม่?"):
            try:
                conn = self.get_db_connection()
                if not conn:
                    messagebox.showerror("Error", "ไม่สามารถเชื่อมต่อฐานข้อมูลได้")
                    return

                cursor = conn.cursor()
                cursor.execute("DELETE FROM packages WHERE id = %s", (package_id,))
                conn.commit()
                cursor.close()

                messagebox.showinfo("Success", f"ลบแพ็กเกจ '{package_name}' สำเร็จ")
                self.load_packages()

            except Exception as e:
                print(f"Delete package error: {e}")
                messagebox.showerror("Error", f"ไม่สามารถลบแพ็กเกจได้: {str(e)}")

    def package_dialog(self, mode='add', package_data=None):
        """แสดง dialog สำหรับเพิ่ม/แก้ไขแพ็กเกจ"""
        dialog = tk.Toplevel(self.root)
        dialog.title("เพิ่มแพ็กเกจ" if mode == 'add' else "แก้ไขแพ็กเกจ")
        dialog.geometry("500x400")
        dialog.configure(bg='#ecf0f1')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Title
        title_text = "เพิ่มแพ็กเกจใหม่" if mode == 'add' else "แก้ไขแพ็กเกจ"
        title_label = tk.Label(dialog, text=title_text, bg='#ecf0f1', font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)

        # Form frame
        form_frame = tk.Frame(dialog, bg='#ecf0f1')
        form_frame.pack(padx=40, pady=20, fill='both', expand=True)

        # Package name
        tk.Label(form_frame, text="ชื่อแพ็กเกจ:", bg='#ecf0f1', font=('Arial', 12)).pack(anchor='w', pady=(10, 5))
        name_entry = tk.Entry(form_frame, font=('Arial', 12), width=40)
        name_entry.pack(fill='x', pady=(0, 10))

        # Price
        tk.Label(form_frame, text="ราคา (บาท):", bg='#ecf0f1', font=('Arial', 12)).pack(anchor='w', pady=(10, 5))
        price_entry = tk.Entry(form_frame, font=('Arial', 12), width=40)
        price_entry.pack(fill='x', pady=(0, 10))

        # Duration
        tk.Label(form_frame, text="ระยะเวลา (วัน):", bg='#ecf0f1', font=('Arial', 12)).pack(anchor='w', pady=(10, 5))
        duration_entry = tk.Entry(form_frame, font=('Arial', 12), width=40)
        duration_entry.pack(fill='x', pady=(0, 10))

        # Description
        tk.Label(form_frame, text="คำอธิบาย:", bg='#ecf0f1', font=('Arial', 12)).pack(anchor='w', pady=(10, 5))
        description_text = scrolledtext.ScrolledText(form_frame, height=5, width=40, font=('Arial', 10))
        description_text.pack(fill='both', expand=True, pady=(0, 10))

        # Active checkbox
        is_active_var = tk.BooleanVar(value=True)
        active_check = tk.Checkbutton(form_frame, text="เปิดใช้งาน", variable=is_active_var,
                                     bg='#ecf0f1', font=('Arial', 12))
        active_check.pack(anchor='w', pady=10)

        # Fill data if editing
        if mode == 'edit' and package_data:
            name_entry.insert(0, package_data[1])  # name
            price_entry.insert(0, str(package_data[2]).replace(',', ''))  # price
            duration_entry.insert(0, str(package_data[3]).replace(' days', ''))  # duration
            description_text.insert('1.0', package_data[4] if package_data[4] != 'None' else '')  # description
            is_active_var.set(package_data[5] == 'Yes')  # is_active

        # Buttons frame
        buttons_frame = tk.Frame(dialog, bg='#ecf0f1')
        buttons_frame.pack(pady=20)

        def save_package():
            name = name_entry.get().strip()
            price_str = price_entry.get().strip()
            duration_str = duration_entry.get().strip()
            description = description_text.get('1.0', tk.END).strip()
            is_active = 1 if is_active_var.get() else 0

            # Validation
            if not name:
                messagebox.showerror("Error", "กรุณาใส่ชื่อแพ็กเกจ")
                return

            try:
                price = float(price_str)
                duration = int(duration_str)
            except ValueError:
                messagebox.showerror("Error", "กรุณาใส่ราคาและระยะเวลาที่ถูกต้อง")
                return

            if price <= 0 or duration <= 0:
                messagebox.showerror("Error", "ราคาและระยะเวลาต้องมากกว่า 0")
                return

            try:
                conn = self.get_db_connection()
                if not conn:
                    messagebox.showerror("Error", "ไม่สามารถเชื่อมต่อฐานข้อมูลได้")
                    return

                cursor = conn.cursor()

                if mode == 'add':
                    query = """
                    INSERT INTO packages (name, price, duration_days, description, is_active, created_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                    """
                    cursor.execute(query, (name, price, duration, description, is_active))
                    action = "เพิ่ม"
                else:
                    package_id = package_data[0]
                    query = """
                    UPDATE packages
                    SET name = %s, price = %s, duration_days = %s, description = %s, is_active = %s
                    WHERE id = %s
                    """
                    cursor.execute(query, (name, price, duration, description, is_active, package_id))
                    action = "แก้ไข"

                conn.commit()
                cursor.close()

                messagebox.showinfo("Success", f"{action}แพ็กเกจสำเร็จ")
                dialog.destroy()
                self.load_packages()

            except Exception as e:
                print(f"Save package error: {e}")
                messagebox.showerror("Error", f"ไม่สามารถ{action}แพ็กเกจได้: {str(e)}")

        save_btn = tk.Button(buttons_frame, text="💾 บันทึก",
                            command=save_package,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            padx=20, pady=10)
        save_btn.pack(side='left', padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ ยกเลิก",
                              command=dialog.destroy,
                              bg='#95a5a6', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=10)
        cancel_btn.pack(side='left', padx=10)

        # Focus on name entry
        name_entry.focus()

    def show_packages_context_menu(self, event):
        """แสดง context menu สำหรับ packages"""
        try:
            # Select the item under cursor
            item = self.packages_tree.identify_row(event.y)
            if item:
                self.packages_tree.selection_set(item)
                self.packages_context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"Context menu error: {e}")

    def create_tools_tab(self):
        """สร้างแท็บ Tools"""
        tools_frame = ttk.Frame(self.notebook)
        self.notebook.add(tools_frame, text="🔧 Tools")

        # Tools content
        tools_content = tk.Frame(tools_frame, bg='#ecf0f1')
        tools_content.pack(fill='both', expand=True, padx=20, pady=20)

        tk.Label(tools_content, text="🔧 เครื่องมือจัดการระบบ", bg='#ecf0f1', font=('Arial', 16, 'bold')).pack(pady=20)

        # Database tools
        db_frame = tk.Frame(tools_content, bg='#3498db', relief='raised', bd=2)
        db_frame.pack(fill='x', pady=10)

        tk.Label(db_frame, text="🗄️ เครื่องมือฐานข้อมูล", bg='#3498db', fg='white', font=('Arial', 12, 'bold')).pack(pady=10)

        test_db_btn = tk.Button(db_frame, text="🧪 ทดสอบการเชื่อมต่อฐานข้อมูล",
                               command=self.test_database_connection,
                               bg='#2980b9', fg='white', font=('Arial', 10))
        test_db_btn.pack(pady=5)

        # System info
        info_frame = tk.Frame(tools_content, bg='#27ae60', relief='raised', bd=2)
        info_frame.pack(fill='x', pady=10)

        tk.Label(info_frame, text="ℹ️ ข้อมูลระบบ", bg='#27ae60', fg='white', font=('Arial', 12, 'bold')).pack(pady=10)

        self.system_info_text = scrolledtext.ScrolledText(info_frame, height=10, width=80)
        self.system_info_text.pack(padx=10, pady=10)

        update_info_btn = tk.Button(info_frame, text="🔄 อัปเดตข้อมูลระบบ",
                                   command=self.update_system_info,
                                   bg='#229954', fg='white', font=('Arial', 10))
        update_info_btn.pack(pady=5)

        # Initialize system info
        self.update_system_info()

    def test_database_connection(self):
        """ทดสอบการเชื่อมต่อฐานข้อมูล"""
        try:
            conn = self.get_db_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                cursor.close()

                messagebox.showinfo("Success", f"✅ เชื่อมต่อฐานข้อมูลสำเร็จ!\nMySQL Version: {version}")
            else:
                messagebox.showerror("Error", "❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้")
        except Exception as e:
            messagebox.showerror("Error", f"❌ เกิดข้อผิดพลาด: {str(e)}")

    def update_system_info(self):
        """อัปเดตข้อมูลระบบ"""
        try:
            info_text = f"🔧 Jellyfin Admin Tools v2.0\n"
            info_text += f"📅 เวลาปัจจุบัน: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            info_text += f"👤 ผู้ใช้: {self.admin_user['username']}\n\n"

            # Database info
            conn = self.get_db_connection()
            if conn:
                cursor = conn.cursor()

                # MySQL version
                cursor.execute("SELECT VERSION()")
                mysql_version = cursor.fetchone()[0]
                info_text += f"🗄️ MySQL Version: {mysql_version}\n"

                # Database size
                cursor.execute("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size (MB)' FROM information_schema.tables WHERE table_schema = %s", (self.db_config['database'],))
                db_size = cursor.fetchone()[0] or 0
                info_text += f"📊 Database Size: {db_size} MB\n\n"

                # Table info
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                info_text += f"📋 Tables ({len(tables)}):\n"

                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    info_text += f"  - {table_name}: {count} records\n"

                cursor.close()
            else:
                info_text += "❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้\n"

            # Update text widget
            self.system_info_text.delete(1.0, tk.END)
            self.system_info_text.insert(1.0, info_text)

        except Exception as e:
            error_text = f"❌ เกิดข้อผิดพลาดในการอัปเดตข้อมูลระบบ: {str(e)}"
            self.system_info_text.delete(1.0, tk.END)
            self.system_info_text.insert(1.0, error_text)

    def logout(self):
        """ออกจากระบบ"""
        self.is_logged_in = False
        self.admin_user = None
        self.create_login_interface()

    def run(self):
        """เริ่มต้นแอปพลิเคชัน"""
        self.root.mainloop()
        self.close_db_connection()

if __name__ == "__main__":
    app = JellyfinAdminTools()
    app.run()
