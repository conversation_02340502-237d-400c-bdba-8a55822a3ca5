<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test Fixed Cron Job</h2>";

try {
    echo "<h3>🔍 Step 1: Check PID Lock System</h3>";
    
    $pidFile = '/tmp/manual_check.pid';
    if (file_exists($pidFile)) {
        $pid = trim(file_get_contents($pidFile));
        echo "<p>PID file exists: {$pidFile}</p>";
        echo "<p>PID: {$pid}</p>";
        
        // Check if process is running
        if (function_exists('posix_kill') && posix_kill($pid, 0)) {
            echo "<p>❌ Process is still running</p>";
        } else {
            echo "<p>✅ Process is not running (stale PID file)</p>";
        }
    } else {
        echo "<p>✅ No PID file exists</p>";
    }
    
    echo "<h3>🔍 Step 2: Check Manual Payments</h3>";
    
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at ASC
        LIMIT 3
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    echo "<p>Found " . count($payments) . " pending manual payments</p>";
    
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Age</th><th>Auto-Verify</th></tr>";
        
        foreach ($payments as $payment) {
            $age = time() - strtotime($payment['created_at']);
            $ageHours = round($age / 3600, 1);
            $isSmallAmount = $payment['amount'] <= 100;
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>{$ageHours}h</td>";
            echo "<td>" . ($isSmallAmount ? '✅ Yes' : '❌ No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🔧 Step 3: Test Single Cron Run</h3>";
    
    if (isset($_POST['run_single_cron'])) {
        echo "<h4>🔄 Running single cron job...</h4>";
        
        // Kill any existing processes first
        shell_exec('pkill -f manual_check 2>/dev/null');
        
        // Remove PID file if exists
        if (file_exists($pidFile)) {
            unlink($pidFile);
            echo "<p>✅ Removed existing PID file</p>";
        }
        
        // Run cron job
        $output = shell_exec('cd /var/www/html && timeout 30 php cron/linux_manual_check.php 2>&1');
        
        echo "<h4>Output:</h4>";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
        
        // Show recent log entries
        $logFile = '/var/log/jellyfin/manual_cron.log';
        if (file_exists($logFile)) {
            $lines = file($logFile);
            $recentLines = array_slice($lines, -15);
            
            echo "<h4>Recent Log Entries:</h4>";
            echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
            echo htmlspecialchars(implode('', $recentLines));
            echo "</pre>";
        }
        
        // Check if PID file was cleaned up
        if (file_exists($pidFile)) {
            echo "<p>⚠️ PID file still exists after execution</p>";
        } else {
            echo "<p>✅ PID file cleaned up properly</p>";
        }
        
    } else {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='run_single_cron' value='1'>";
        echo "<button type='submit' style='background: blue; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px;'>🔄 Run Single Cron Job</button>";
        echo "</form>";
    }
    
    echo "<h3>🔧 Step 4: Test Concurrent Prevention</h3>";
    
    if (isset($_POST['test_concurrent'])) {
        echo "<h4>🔄 Testing concurrent execution prevention...</h4>";
        
        // Start first process in background
        $cmd1 = 'cd /var/www/html && php cron/linux_manual_check.php > /tmp/cron_test1.log 2>&1 &';
        shell_exec($cmd1);
        echo "<p>✅ Started first process</p>";
        
        // Wait a moment
        sleep(1);
        
        // Try to start second process
        $cmd2 = 'cd /var/www/html && timeout 5 php cron/linux_manual_check.php 2>&1';
        $output2 = shell_exec($cmd2);
        
        echo "<h4>Second Process Output:</h4>";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
        echo htmlspecialchars($output2 ?: 'No output (process exited silently)');
        echo "</pre>";
        
        // Check first process log
        if (file_exists('/tmp/cron_test1.log')) {
            $log1 = file_get_contents('/tmp/cron_test1.log');
            echo "<h4>First Process Log:</h4>";
            echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
            echo htmlspecialchars($log1);
            echo "</pre>";
        }
        
        // Clean up
        shell_exec('pkill -f manual_check 2>/dev/null');
        if (file_exists($pidFile)) unlink($pidFile);
        if (file_exists('/tmp/cron_test1.log')) unlink('/tmp/cron_test1.log');
        
    } else {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='test_concurrent' value='1'>";
        echo "<button type='submit' style='background: orange; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px;'>🔄 Test Concurrent Prevention</button>";
        echo "</form>";
    }
    
    echo "<h3>📊 Step 5: Current Statistics</h3>";
    
    $stats = [];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'pending' AND payment_method = 'manual'");
    $stmt->execute();
    $stats['pending_manual'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'completed' AND paid_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $stmt->execute();
    $stats['completed_hour'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'completed' AND paid_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $stats['completed_day'] = $stmt->fetchColumn();
    
    // Check running processes
    $processes = shell_exec('ps aux | grep manual_check | grep -v grep | wc -l');
    $stats['running_processes'] = trim($processes);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Pending Manual</td><td style='background: yellow;'>{$stats['pending_manual']}</td></tr>";
    echo "<tr><td>Completed (Last Hour)</td><td style='background: lightgreen;'>{$stats['completed_hour']}</td></tr>";
    echo "<tr><td>Completed (Last 24h)</td><td style='background: lightblue;'>{$stats['completed_day']}</td></tr>";
    echo "<tr><td>Running Processes</td><td style='background: " . ($stats['running_processes'] > 1 ? 'red' : 'lightgreen') . ";'>{$stats['running_processes']}</td></tr>";
    echo "</table>";
    
    echo "<h3>🛠️ Quick Actions</h3>";
    
    echo "<form method='POST' style='display: inline-block; margin: 5px;'>";
    echo "<input type='hidden' name='kill_all' value='1'>";
    echo "<button type='submit' style='background: red; color: white; padding: 10px; border: none; cursor: pointer;'>🔪 Kill All Processes</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline-block; margin: 5px;'>";
    echo "<input type='hidden' name='clean_pid' value='1'>";
    echo "<button type='submit' style='background: orange; color: white; padding: 10px; border: none; cursor: pointer;'>🧹 Clean PID File</button>";
    echo "</form>";
    
    if (isset($_POST['kill_all'])) {
        shell_exec('pkill -f manual_check 2>/dev/null');
        echo "<p>✅ Killed all manual_check processes</p>";
    }
    
    if (isset($_POST['clean_pid'])) {
        if (file_exists($pidFile)) {
            unlink($pidFile);
            echo "<p>✅ Cleaned PID file</p>";
        } else {
            echo "<p>ℹ️ No PID file to clean</p>";
        }
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/check_cron_duplicate.php'>🔍 Check Cron Duplicates</a></p>";
?>
