<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test with Existing Schema</h2>";

try {
    echo "<h3>🔍 Step 1: Check Current Schema</h3>";
    
    $stmt = $pdo->query("DESCRIBE payment_transactions");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Default</th></tr>";
    
    $availableColumns = [];
    foreach ($columns as $column) {
        $availableColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Available columns for verification:</h4>";
    echo "<ul>";
    echo "<li>status: " . (in_array('status', $availableColumns) ? '✅' : '❌') . "</li>";
    echo "<li>paid_at: " . (in_array('paid_at', $availableColumns) ? '✅' : '❌') . "</li>";
    echo "<li>updated_at: " . (in_array('updated_at', $availableColumns) ? '✅' : '❌') . "</li>";
    echo "</ul>";
    
    echo "<h3>🔍 Step 2: Check Manual Payments</h3>";
    
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at DESC
        LIMIT 3
    ");
    $stmt->execute();
    $manualPayments = $stmt->fetchAll();
    
    echo "<p>Manual payments found: " . count($manualPayments) . "</p>";
    
    if ($manualPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Slip</th><th>File Exists</th><th>Auto-Verify</th></tr>";
        
        foreach ($manualPayments as $payment) {
            // Check file path
            $slipImage = $payment['slip_image'];
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $slipImage = 'uploads/slips/' . $slipImage;
            }
            
            $fileExists = file_exists($slipImage);
            $isSmallAmount = $payment['amount'] <= 100;
            $autoVerify = $isSmallAmount && $fileExists ? '✅' : '❌';
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . basename($payment['slip_image']) . "</td>";
            echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
            echo "<td>" . $autoVerify . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='POST'>";
        echo "<input type='hidden' name='test_verification_existing' value='1'>";
        echo "<button type='submit' style='background: green; color: white; padding: 10px; border: none; cursor: pointer;'>Test Verification (Existing Schema)</button>";
        echo "</form>";
    }
    
    echo "<h3>📊 Step 3: Payment Statistics</h3>";
    
    $stats = [];
    
    // Pending manual payments
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'pending' AND payment_method = 'manual'");
    $stmt->execute();
    $stats['pending_manual'] = $stmt->fetchColumn();
    
    // Completed payments
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'completed'");
    $stmt->execute();
    $stats['completed_total'] = $stmt->fetchColumn();
    
    // Recent completed payments
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'completed' AND paid_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $stats['completed_today'] = $stmt->fetchColumn();
    
    // All statuses
    $stmt = $pdo->prepare("SELECT status, COUNT(*) as count FROM payment_transactions GROUP BY status");
    $stmt->execute();
    $statusCounts = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Pending Manual</td><td style='background: yellow;'>{$stats['pending_manual']}</td></tr>";
    echo "<tr><td>Completed Total</td><td style='background: lightgreen;'>{$stats['completed_total']}</td></tr>";
    echo "<tr><td>Completed Today</td><td style='background: lightblue;'>{$stats['completed_today']}</td></tr>";
    echo "</table>";
    
    echo "<h4>Status Breakdown:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Status</th><th>Count</th></tr>";
    foreach ($statusCounts as $status) {
        echo "<tr><td>{$status['status']}</td><td>{$status['count']}</td></tr>";
    }
    echo "</table>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle form submissions
if ($_POST && isset($_POST['test_verification_existing'])) {
    echo "<h3>🧪 Testing Verification with Existing Schema:</h3>";
    try {
        // Get one manual payment to test
        $stmt = $pdo->prepare("
            SELECT pt.*, u.username
            FROM payment_transactions pt
            JOIN users u ON pt.user_id = u.id
            WHERE pt.status = 'pending'
            AND pt.payment_method = 'manual'
            AND pt.slip_image IS NOT NULL
            AND pt.slip_image != ''
            ORDER BY pt.created_at ASC
            LIMIT 1
        ");
        $stmt->execute();
        $payment = $stmt->fetch();
        
        if ($payment) {
            echo "<p>Testing payment ID: {$payment['id']} for user: {$payment['username']}</p>";
            
            // Check slip file with path fix
            $slipImage = $payment['slip_image'];
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $slipImage = 'uploads/slips/' . $slipImage;
            }
            
            if (!file_exists($slipImage)) {
                echo "<p>❌ Slip file not found: {$slipImage}</p>";
            } else {
                $fileSize = filesize($slipImage);
                echo "<p>✅ Slip file found: {$slipImage} ({$fileSize} bytes)</p>";
                
                // Check auto-verification criteria
                $isValidSize = $fileSize > 10000 && $fileSize < 10000000;
                $isSmallAmount = $payment['amount'] <= 100;
                
                echo "<p>Valid size: " . ($isValidSize ? '✅' : '❌') . "</p>";
                echo "<p>Small amount (≤100 THB): " . ($isSmallAmount ? '✅' : '❌') . "</p>";
                
                if ($isSmallAmount && $isValidSize) {
                    echo "<p>✅ <strong>Auto-verification criteria met!</strong></p>";
                    
                    // Test the verification update using existing columns
                    $pdo->beginTransaction();
                    
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'completed',
                            paid_at = NOW()
                        WHERE id = ?
                    ");
                    $result = $updateStmt->execute([$payment['id']]);
                    
                    if ($result) {
                        echo "<p>✅ Payment verified successfully using existing schema!</p>";
                        echo "<p>Status changed to: completed</p>";
                        echo "<p>paid_at set to: NOW()</p>";
                        echo "<p>Affected rows: " . $updateStmt->rowCount() . "</p>";
                        
                        // Show updated record
                        $checkStmt = $pdo->prepare("SELECT status, paid_at FROM payment_transactions WHERE id = ?");
                        $checkStmt->execute([$payment['id']]);
                        $updated = $checkStmt->fetch();
                        
                        echo "<p>Updated record: status = {$updated['status']}, paid_at = {$updated['paid_at']}</p>";
                        
                    } else {
                        echo "<p>❌ Failed to verify payment</p>";
                    }
                    
                    $pdo->commit();
                    
                } else {
                    echo "<p>❌ Auto-verification criteria not met</p>";
                    echo "<p>This payment would need manual admin review</p>";
                }
            }
        } else {
            echo "<p>❌ No manual payments found for testing</p>";
        }
        
    } catch (Exception $e) {
        if (isset($pdo)) $pdo->rollback();
        echo "<p>❌ Test error: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/debug_slip_path.php'>🔍 Debug Slip Path</a></p>";
?>
