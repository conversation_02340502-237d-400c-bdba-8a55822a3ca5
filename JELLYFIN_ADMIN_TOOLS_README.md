# 🔧 Jellyfin Admin Tools v2.0

ระบบจัดการ Admin สำหรับ Jellyfin Registration System ที่เชื่อมต่อกับฐานข้อมูล jellyfin_registration โดยตรง

## ✨ Features

### 🔐 Authentication System
- **Database Direct Login** - เชื่อมต่อฐานข้อมูล MySQL โดยตรง
- **Password Hash Support** - รองรับ PHP password_hash() และ bcrypt
- **Admin Verification** - ตรวจสอบ is_admin = 1 และ status = 'active'
- **Secure Login** - รองรับ password หลายรูปแบบ (bcrypt, MD5, SHA256, plain text)

### 📊 Dashboard
- **Real-time Statistics** - สถิติผู้ใช้, การชำระเงิน, แพ็กเกจ
- **User Analytics** - จำนวนผู้ใช้ทั้งหมด, Admin, Active users
- **Payment Analytics** - รายได้รวม, การชำระเงินที่ยืนยันแล้ว
- **Package Analytics** - แพ็กเกจที่ใช้งานได้

### 👥 User Management
- **User List** - แสดงรายการผู้ใช้ทั้งหมด
- **Search Users** - ค้นหาผู้ใช้ด้วย username หรือ phone
- **Reset Password** - รีเซ็ต password ผู้ใช้ (เดี่ยวหรือหลายคน)
- **Bulk Reset** - รีเซ็ต password หลายผู้ใช้พร้อมกัน
- **Password Generator** - สร้าง password แบบสุ่มอัตโนมัติ
- **Interactive Controls** - Right-click menu, Keyboard shortcuts
- **Real-time Data** - ข้อมูลจากฐานข้อมูลแบบ real-time

### 💰 Payment Management
- **Payment List** - แสดงรายการการชำระเงินทั้งหมด
- **Search Payments** - ค้นหาการชำระเงินด้วย username, status, package
- **Payment Details** - ข้อมูลการชำระเงิน, สถานะ, วันที่ยืนยัน
- **Payment Status** - pending, verified, failed

### 📦 Package Management
- **Package List** - แสดงรายการแพ็กเกจทั้งหมด
- **Add Package** - เพิ่มแพ็กเกจใหม่ (ชื่อ, ราคา, ระยะเวลา, คำอธิบาย)
- **Edit Package** - แก้ไขแพ็กเกจที่มีอยู่
- **Delete Package** - ลบแพ็กเกจ (มีการยืนยัน)
- **Package Status** - เปิด/ปิดใช้งานแพ็กเกจ
- **Interactive Controls** - Double-click แก้ไข, Right-click menu, Keyboard shortcuts

### 🔧 System Tools
- **Database Test** - ทดสอบการเชื่อมต่อฐานข้อมูล
- **System Information** - ข้อมูลระบบ, MySQL version, ขนาดฐานข้อมูล
- **Table Statistics** - จำนวน records ในแต่ละตาราง

## 🚀 Installation & Setup

### 1. ข้อกำหนดระบบ
```
- Windows 10/11
- MySQL Server (Laragon/XAMPP)
- ฐานข้อมูล jellyfin_registration
- Admin user ที่มี is_admin = 1
```

### 2. Database Configuration
```sql
Database: jellyfin_registration
Host: localhost
User: root
Password: Wxmujwsofu@1234
Charset: utf8mb4
```

### 3. Required Tables
```sql
- users (id, username, password_hash, phone, is_admin, created_at)
- payments (id, user_id, amount, status, payment_method, created_at, verified_at)
- packages (id, name, price, duration_days, description, is_active, created_at)
```

**หมายเหตุ:**
- ใช้ field `password_hash` แทน `password`
- ไม่ใช้ field `status` และ `expires_at` ในตาราง users
- ไม่ใช้ field `package_id` ในตาราง users และ payments

## 🔑 Login Credentials

### Database Users
ใช้ admin user ที่มีอยู่ในฐานข้อมูล jellyfin_registration:
```
Username: [admin username from database]
Password: [admin password from database]
Requirements: is_admin = 1
```

### สร้าง Admin User
หากยังไม่มี admin user:
1. เปิด `http://localhost/create_admin_user.php`
2. สร้าง admin user ใหม่
3. ใช้ username และ password ที่สร้างเพื่อ login

### Quick Fix User james
หากต้องการแก้ไข user james:
1. เปิด `http://localhost/quick_fix_james.php`
2. คลิก "Auto Fix User james"
3. ใช้ username: james, password: james123

## 🎯 Usage

### 1. เปิดแอปพลิเคชัน
```
รัน: JellyfinAdminTools.exe
```

### 2. Login
```
1. ใส่ username และ password
2. คลิก "เข้าสู่ระบบ"
3. รอการตรวจสอบ authentication
```

### 3. ใช้งาน Features
```
📊 Dashboard - ดูสถิติระบบ
👥 Users - จัดการผู้ใช้
💰 Payments - จัดการการชำระเงิน
📦 Packages - จัดการแพ็กเกจ (เพิ่ม/แก้ไข/ลบ)
🔧 Tools - เครื่องมือระบบ
```

### 4. Package Management
```
➕ เพิ่มแพ็กเกจ: คลิกปุ่ม "เพิ่มแพ็กเกจ" หรือกด Insert
✏️ แก้ไขแพ็กเกจ: Double-click หรือเลือกแล้วกด F2
🗑️ ลบแพ็กเกจ: เลือกแล้วกด Delete หรือคลิกปุ่ม "ลบ"
📋 Context Menu: Right-click บนแพ็กเกจเพื่อดูตัวเลือก
🔄 รีเฟรช: คลิกปุ่ม "รีเฟรช" เพื่ออัปเดตข้อมูล
```

### 5. Password Reset Management
```
🔑 Reset Password: เลือกผู้ใช้แล้วคลิกปุ่ม "Reset Password" หรือกด F3
🔑 Bulk Reset: เลือกหลายผู้ใช้แล้วคลิก "Bulk Reset"
🎲 Auto Generate: ใช้ปุ่ม "สร้าง Password อัตโนมัติ"
👁️ Show Password: เปิด/ปิดการแสดง password
📋 Context Menu: Right-click บนผู้ใช้เพื่อดูตัวเลือก
```

## 🔧 Technical Details

### Database Connection
```python
{
    'host': 'localhost',
    'user': 'root',
    'password': 'Wxmujwsofu@1234',
    'database': 'jellyfin_registration',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}
```

### Password Verification
```python
1. PHP password_hash (bcrypt) - Primary
2. Plain text - Legacy support
3. MD5 hash - Legacy support
4. SHA256 hash - Legacy support
```

### UI Framework
```
- Tkinter (Python GUI)
- ttk Styles (Modern appearance)
- Treeview (Data tables)
- ScrolledText (System info)
```

## 🐛 Troubleshooting

### การเชื่อมต่อฐานข้อมูลไม่ได้
```
1. ตรวจสอบ MySQL Server ทำงานหรือไม่
2. ตรวจสอบ database password
3. ตรวจสอบฐานข้อมูล jellyfin_registration มีอยู่หรือไม่
4. ใช้เครื่องมือ "ทดสอบการเชื่อมต่อฐานข้อมูล" ในแท็บ Tools
```

### Login ไม่ได้
```
1. ตรวจสอบ username มีอยู่ในฐานข้อมูลหรือไม่
2. ตรวจสอบ is_admin = 1
3. ตรวจสอบ password_hash มีค่าและถูกต้องหรือไม่
4. ใช้ quick_fix_james.php เพื่อแก้ไข
```

### ข้อมูลไม่แสดง
```
1. คลิกปุ่ม "รีเฟรช" ในแต่ละแท็บ
2. ตรวจสอบการเชื่อมต่อฐานข้อมูล
3. ตรวจสอบตารางมีข้อมูลหรือไม่
```

## 📝 Version History

### v2.0 (Current)
- ✅ สร้างใหม่ทั้งหมดโดยอิงจากฐานข้อมูล jellyfin_registration
- ✅ เชื่อมต่อฐานข้อมูล MySQL โดยตรง
- ✅ รองรับ password_hash และ bcrypt
- ✅ UI สวยงามแบบ modern
- ✅ Dashboard แสดงสถิติ real-time
- ✅ User management with search
- ✅ **Password Reset Features** - รีเซ็ต password เดี่ยว/หลายคน, Auto generate
- ✅ **Package Management ครบครัน** - เพิ่ม/แก้ไข/ลบแพ็กเกจ
- ✅ **Interactive Controls** - Double-click, Right-click menu, Keyboard shortcuts
- ✅ Payment management และ System tools

### v1.x (Legacy)
- API-based connection
- Demo data fallback
- Basic user interface

## 🔗 Related Files

### Web Tools
```
- quick_fix_james.php - แก้ไข user james
- test_password_hash.php - ทดสอบ password hash
- debug_login.php - debug login issues
- create_admin_user.php - สร้าง admin user
- setup_database.php - ตั้งค่าฐานข้อมูล
```

### API Endpoints
```
- api/login.php - API login
- api/admin/users.php - Users API
- api/admin/payments.php - Payments API
- api/admin/packages.php - Packages API
```

## 📞 Support

หากพบปัญหาการใช้งาน:
1. ตรวจสอบ Troubleshooting section
2. ใช้เครื่องมือ debug ที่มีให้
3. ตรวจสอบ console output สำหรับ error messages

---

**Jellyfin Admin Tools v2.0** - Built with ❤️ for Jellyfin Registration System
