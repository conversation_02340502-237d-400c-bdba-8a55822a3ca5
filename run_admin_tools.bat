@echo off
title Jellyfin Admin Tools
echo.
echo ========================================
echo    Jellyfin Admin Tools Launcher
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check if required packages are installed
echo Checking required packages...
python -c "import tkinter, requests" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install requests
    if errorlevel 1 (
        echo Error: Failed to install required packages
        pause
        exit /b 1
    )
)

REM Run the Admin Tools
echo Starting Jellyfin Admin Tools...
echo.
python AdminTools.py

if errorlevel 1 (
    echo.
    echo Error: Failed to start Admin Tools
    echo Please check the error messages above
    pause
)

echo.
echo Admin Tools closed.
pause
