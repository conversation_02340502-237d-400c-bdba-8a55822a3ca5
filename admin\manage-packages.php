<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require admin access
require_login();
require_admin();

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

$message = '';
$message_type = 'info';

// Handle package actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    $package_id = (int)($_POST['package_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'toggle_status':
                $stmt = $pdo->prepare("UPDATE packages SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$package_id]);
                $message = 'อัปเดตสถานะแพ็คเกจเรียบร้อยแล้ว';
                $message_type = 'success';
                break;
                
            case 'delete_package':
                // Check if package has any subscriptions
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_subscriptions WHERE package_id = ?");
                $stmt->execute([$package_id]);
                $subscription_count = $stmt->fetchColumn();
                
                if ($subscription_count > 0) {
                    $message = 'ไม่สามารถลบแพ็คเกจที่มีการสมัครสมาชิกแล้ว';
                    $message_type = 'error';
                } else {
                    $stmt = $pdo->prepare("DELETE FROM packages WHERE id = ?");
                    $stmt->execute([$package_id]);
                    $message = 'ลบแพ็คเกจเรียบร้อยแล้ว';
                    $message_type = 'success';
                }
                break;
        }
        
        // Log activity
        log_activity($_SESSION['user_id'], 'package_' . $action, "Package ID: {$package_id}");
        
    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get all packages
$stmt = $pdo->query("
    SELECT p.*, 
           COUNT(us.id) as subscription_count,
           SUM(CASE WHEN us.status = 'active' THEN 1 ELSE 0 END) as active_subscriptions
    FROM packages p
    LEFT JOIN user_subscriptions us ON p.id = us.package_id
    GROUP BY p.id
    ORDER BY p.price ASC
");
$packages = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการแพ็คเกจ - Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .packages-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 2rem 0;
        }
        .packages-table th,
        .packages-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .packages-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .packages-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-active { background: #28a745; color: #fff; }
        .status-inactive { background: #dc3545; color: #fff; }
        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0 0.25rem;
            font-size: 0.8rem;
        }
        .btn-edit { background: #ffc107; color: #000; }
        .btn-toggle { background: #17a2b8; color: #fff; }
        .btn-delete { background: #dc3545; color: #fff; }
        .btn-add { background: #28a745; color: #fff; padding: 0.75rem 1.5rem; margin-bottom: 1rem; }
        .max-sessions-highlight {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-weight: bold;
        }
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="add-package.php" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="auto-verification.php" class="dropdown-item">เช็คสลิปอัตโนมัติ</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

        <main class="main-content">
            <div class="hero-section" style="padding: 2rem 0;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h2 class="fade-in-up">จัดการแพ็คเกจ</h2>
                        <p class="fade-in-up delay-1" style="color: #ccc;">เพิ่ม แก้ไข และจัดการแพ็คเกจทั้งหมด</p>
                    </div>
                    <a href="add-package.php" class="btn btn-primary">+ เพิ่มแพ็คเกจใหม่</a>
                </div>
            </div>

            <div class="fade-in-scroll">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'error'; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <a href="add-package.php" class="action-btn btn-add">➕ เพิ่มแพ็คเกจใหม่</a>

                <div style="overflow-x: auto;">
                    <table class="packages-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>ชื่อแพ็คเกจ</th>
                                <th>ราคา</th>
                                <th>ระยะเวลา</th>
                                <th>Max Sessions</th>
                                <th>สถานะ</th>
                                <th>การสมัคร</th>
                                <th>วันที่สร้าง</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($packages as $package): ?>
                                <tr>
                                    <td><?php echo $package['id']; ?></td>
                                    <td><strong><?php echo htmlspecialchars($package['name']); ?></strong></td>
                                    <td><strong><?php echo number_format($package['price'], 2); ?> บาท</strong></td>
                                    <td><?php echo $package['duration_days']; ?> วัน</td>
                                    <td>
                                        <span class="max-sessions-highlight">
                                            <?php echo $package['max_sessions']; ?> อุปกรณ์
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $package['is_active'] ? 'active' : 'inactive'; ?>">
                                            <?php echo $package['is_active'] ? 'ใช้งานได้' : 'ปิดใช้งาน'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div>ทั้งหมด: <?php echo $package['subscription_count']; ?></div>
                                        <div style="font-size: 0.8rem; color: #28a745;">ใช้งานอยู่: <?php echo $package['active_subscriptions']; ?></div>
                                    </td>
                                    <td><?php echo date('d/m/Y', strtotime($package['created_at'])); ?></td>
                                    <td>
                                        <a href="edit-package.php?id=<?php echo $package['id']; ?>" class="action-btn btn-edit">✏️ แก้ไข</a>
                                        
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="package_id" value="<?php echo $package['id']; ?>">
                                            <button type="submit" class="action-btn btn-toggle"
                                                    onclick="return confirm('เปลี่ยนสถานะแพ็คเกจนี้ใช่หรือไม่?')">
                                                <?php echo $package['is_active'] ? '🔒 ปิด' : '🔓 เปิด'; ?>
                                            </button>
                                        </form>
                                        
                                        <?php if ($package['subscription_count'] == 0): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete_package">
                                                <input type="hidden" name="package_id" value="<?php echo $package['id']; ?>">
                                                <button type="submit" class="action-btn btn-delete"
                                                        onclick="return confirm('ลบแพ็คเกจนี้ใช่หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้!')">
                                                    🗑️ ลบ
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <span class="action-btn" style="background: #6c757d; color: #fff; cursor: not-allowed;">
                                                🗑️ ไม่สามารถลบ
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 2rem; padding: 1rem; background: rgba(255, 193, 7, 0.1); border-radius: 8px; border: 1px solid rgba(255, 193, 7, 0.3);">
                    <h4 style="color: #ffc107; margin-bottom: 1rem;">📋 คำแนะนำ:</h4>
                    <ul style="color: #ccc; margin: 0; padding-left: 1.5rem;">
                        <li>แพ็คเกจที่มีการสมัครสมาชิกแล้วจะไม่สามารถลบได้</li>
                        <li>การปิดใช้งานแพ็คเกจจะทำให้ผู้ใช้ไม่เห็นแพ็คเกจในหน้าเลือกแพ็คเกจ</li>
                        <li>Max Sessions คือจำนวนอุปกรณ์ที่สามารถดูพร้อมกันได้</li>
                        <li>ราคาของแต่ละแพ็คเกจต้องไม่ซ้ำกัน</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
