# Backup of current .htaccess
# Jellyfin by James - Clean URL Configuration
RewriteEngine On

# Main Pages Clean URLs
RewriteRule ^home/?$ index.php [L]
RewriteRule ^login/?$ login.php [L]
RewriteRule ^register/?$ register.php [L]
RewriteRule ^dashboard/?$ dashboard.php [L]
RewriteRule ^packages/?$ packages.php [L]
RewriteRule ^profile/?$ profile.php [L]
RewriteRule ^guide/?$ guide.php [L]
RewriteRule ^payment/?$ payment.php [L]
RewriteRule ^referral/?$ referral.php [L]
RewriteRule ^logout/?$ logout.php [L]

# Admin Pages Clean URLs
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/server/?$ admin/server-control.php [L]
RewriteRule ^admin/server-control/?$ admin/server-control.php [L]
RewriteRule ^admin/users/?$ admin/users.php [L]
RewriteRule ^admin/packages/?$ admin/packages.php [L]
RewriteRule ^admin/manage-packages/?$ admin/manage-packages.php [L]
RewriteRule ^admin/add-package/?$ admin/add-package.php [L]
RewriteRule ^admin/transactions/?$ admin/transaction-check.php [L]
RewriteRule ^admin/transaction-check/?$ admin/transaction-check.php [L]
RewriteRule ^admin/paynoi/?$ admin/paynoi-transactions.php [L]
RewriteRule ^admin/paynoi-transactions/?$ admin/paynoi-transactions.php [L]

# API Clean URLs
RewriteRule ^api/login/?$ api/login.php [L]

# Test rewrite
RewriteRule ^test-rewrite/?$ test-rewrite.php [L]
RewriteRule ^test-simple/?$ test-simple.php [L]
