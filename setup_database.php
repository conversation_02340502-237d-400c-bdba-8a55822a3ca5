<?php
/**
 * สคริปต์สำหรับสร้างตารางฐานข้อมูล jellyfin_registration
 */

require_once 'includes/config.php';

echo "<h1>🔧 ตั้งค่าฐานข้อมูล jellyfin_registration</h1>";

if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        if ($action === 'create_tables') {
            // สร้างตาราง users
            $sql_users = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                is_admin TINYINT(1) DEFAULT 0,
                status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
                package_id INT,
                expires_at DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_is_admin (is_admin),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $pdo->exec($sql_users);
            echo "<p style='color: green;'>✅ สร้างตาราง users สำเร็จ</p>";
            
            // สร้างตาราง packages
            $sql_packages = "
            CREATE TABLE IF NOT EXISTS packages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                duration_days INT NOT NULL,
                description TEXT,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $pdo->exec($sql_packages);
            echo "<p style='color: green;'>✅ สร้างตาราง packages สำเร็จ</p>";
            
            // สร้างตาราง payments
            $sql_payments = "
            CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                package_id INT,
                amount DECIMAL(10,2) NOT NULL,
                status ENUM('pending', 'verified', 'failed') DEFAULT 'pending',
                payment_method VARCHAR(50),
                transaction_id VARCHAR(100),
                slip_image VARCHAR(255),
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                verified_at TIMESTAMP NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
                INDEX idx_user_id (user_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $pdo->exec($sql_payments);
            echo "<p style='color: green;'>✅ สร้างตาราง payments สำเร็จ</p>";
            
            echo "<div style='background: #d4edda; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>";
            echo "<h3 style='color: #155724;'>🎉 สร้างตารางทั้งหมดสำเร็จ!</h3>";
            echo "<p>ตอนนี้สามารถสร้าง Admin User และใช้งาน Admin Tools ได้แล้ว</p>";
            echo "</div>";
            
        } elseif ($action === 'create_sample_data') {
            // สร้างข้อมูลตัวอย่าง
            
            // สร้างแพ็กเกจตัวอย่าง
            $packages = [
                ['Package 30 Days', 30.00, 30, 'แพ็กเกจพื้นฐาน 1 เดือน'],
                ['Package 60 Days', 60.00, 60, 'แพ็กเกจมาตรฐาน 2 เดือน'],
                ['Package 90 Days', 90.00, 90, 'แพ็กเกจพรีเมียม 3 เดือน'],
                ['Package 150 Days', 150.00, 150, 'แพ็กเกจ VIP 5 เดือน (2 อุปกรณ์)']
            ];
            
            foreach ($packages as $package) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO packages (name, price, duration_days, description) VALUES (?, ?, ?, ?)");
                $stmt->execute($package);
            }
            
            echo "<p style='color: green;'>✅ สร้างแพ็กเกจตัวอย่างสำเร็จ</p>";
            
            // สร้าง admin user ตัวอย่าง
            $admin_username = 'admin';
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, phone, is_admin, status) VALUES (?, ?, ?, 1, 'active')");
            $stmt->execute([$admin_username, $admin_password, '0812345678']);
            
            echo "<p style='color: green;'>✅ สร้าง Admin User ตัวอย่างสำเร็จ</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            
            echo "<div style='background: #d4edda; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>";
            echo "<h3 style='color: #155724;'>🎉 สร้างข้อมูลตัวอย่างสำเร็จ!</h3>";
            echo "<p>ตอนนี้สามารถใช้งาน Admin Tools ได้ทันที</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
    }
}

// ตรวจสอบสถานะปัจจุบัน
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $has_users = in_array('users', $tables);
    $has_packages = in_array('packages', $tables);
    $has_payments = in_array('payments', $tables);
    
    echo "<h2>📊 สถานะฐานข้อมูลปัจจุบัน</h2>";
    echo "<ul>";
    echo "<li>ตาราง users: " . ($has_users ? "✅ มีอยู่แล้ว" : "❌ ยังไม่มี") . "</li>";
    echo "<li>ตาราง packages: " . ($has_packages ? "✅ มีอยู่แล้ว" : "❌ ยังไม่มี") . "</li>";
    echo "<li>ตาราง payments: " . ($has_payments ? "✅ มีอยู่แล้ว" : "❌ ยังไม่มี") . "</li>";
    echo "</ul>";
    
    if ($has_users) {
        $stmt = $pdo->query("SELECT COUNT(*) as admin_count FROM users WHERE is_admin = 1");
        $admin_count = $stmt->fetch()['admin_count'];
        echo "<p><strong>จำนวน Admin Users:</strong> {$admin_count}</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบสถานะได้: " . $e->getMessage() . "</p>";
}
?>

<h2>🔧 การตั้งค่า</h2>

<?php if (!$has_users || !$has_packages || !$has_payments): ?>
<div style="background: #fff3cd; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
    <h3>⚠️ ต้องสร้างตารางฐานข้อมูล</h3>
    <p>ฐานข้อมูลยังไม่พร้อมใช้งาน กรุณาสร้างตารางก่อน</p>
    
    <form method="POST" style="margin: 1rem 0;">
        <input type="hidden" name="action" value="create_tables">
        <button type="submit" style="background: #007bff; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer;">
            🔧 สร้างตารางฐานข้อมูล
        </button>
    </form>
</div>
<?php endif; ?>

<?php if ($has_users && $has_packages && $has_payments): ?>
<div style="background: #d4edda; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
    <h3 style="color: #155724;">✅ ฐานข้อมูลพร้อมใช้งาน</h3>
    <p>ตารางทั้งหมดถูกสร้างแล้ว</p>
    
    <form method="POST" style="margin: 1rem 0;">
        <input type="hidden" name="action" value="create_sample_data">
        <button type="submit" style="background: #28a745; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer;">
            📦 สร้างข้อมูลตัวอย่าง
        </button>
    </form>
</div>
<?php endif; ?>

<div style="background: #e7f3ff; padding: 1rem; border-radius: 8px; margin: 2rem 0;">
    <h3>📋 ขั้นตอนการตั้งค่า:</h3>
    <ol>
        <li><strong>สร้างตารางฐานข้อมูล:</strong> คลิกปุ่ม "สร้างตารางฐานข้อมูล"</li>
        <li><strong>สร้างข้อมูลตัวอย่าง:</strong> คลิกปุ่ม "สร้างข้อมูลตัวอย่าง" (ไม่บังคับ)</li>
        <li><strong>สร้าง Admin User:</strong> ไปที่หน้า <a href="create_admin_user.php">สร้าง Admin User</a></li>
        <li><strong>ใช้งาน Admin Tools:</strong> เปิด JellyfinAdminTools.exe</li>
    </ol>
</div>

<p style="text-align: center; margin-top: 2rem;">
    <a href="check_database.php" style="background: #6c757d; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">ตรวจสอบฐานข้อมูล</a>
    <a href="create_admin_user.php" style="background: #007bff; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px; margin-left: 10px;">สร้าง Admin User</a>
    <a href="index.php" style="background: #28a745; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px; margin-left: 10px;">กลับไปหน้าแรก</a>
</p>
