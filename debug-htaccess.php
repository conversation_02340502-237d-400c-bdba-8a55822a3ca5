<?php
// Debug .htaccess and Apache configuration
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug .htaccess - Jellyfin by <PERSON></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .test-link { display: inline-block; margin: 5px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .test-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔧 Debug .htaccess Configuration</h1>
    
    <h2>1. Apache Module Check</h2>
    <?php
    if (function_exists('apache_get_modules')) {
        $modules = apache_get_modules();
        echo "<p class='success'>✓ apache_get_modules() function is available</p>";
        
        $required_modules = ['mod_rewrite', 'mod_headers', 'mod_expires', 'mod_deflate'];
        foreach ($required_modules as $module) {
            if (in_array($module, $modules)) {
                echo "<p class='success'>✓ $module is loaded</p>";
            } else {
                echo "<p class='error'>✗ $module is NOT loaded</p>";
            }
        }
    } else {
        echo "<p class='warning'>? Cannot check Apache modules (function not available)</p>";
    }
    ?>
    
    <h2>2. Server Information</h2>
    <pre><?php
    echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
    echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
    echo "Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "\n";
    echo "Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "\n";
    echo "HTTP Host: " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "\n";
    ?></pre>
    
    <h2>3. .htaccess File Check</h2>
    <?php
    $htaccess_path = __DIR__ . '/.htaccess';
    if (file_exists($htaccess_path)) {
        echo "<p class='success'>✓ .htaccess file exists</p>";
        echo "<p class='info'>File size: " . filesize($htaccess_path) . " bytes</p>";
        echo "<p class='info'>Last modified: " . date('Y-m-d H:i:s', filemtime($htaccess_path)) . "</p>";
    } else {
        echo "<p class='error'>✗ .htaccess file not found</p>";
    }
    ?>
    
    <h2>4. Test Links</h2>
    <p>Click these links to test if Clean URLs are working:</p>
    
    <a href="/test-rewrite" class="test-link" target="_blank">Test Rewrite (/test-rewrite)</a>
    <a href="/home" class="test-link" target="_blank">Home (/home)</a>
    <a href="/login" class="test-link" target="_blank">Login (/login)</a>
    <a href="/dashboard" class="test-link" target="_blank">Dashboard (/dashboard)</a>
    <a href="/admin" class="test-link" target="_blank">Admin (/admin)</a>
    
    <h2>5. Direct File Access Test</h2>
    <p>These should work regardless of .htaccess:</p>
    
    <a href="/index.php" class="test-link" target="_blank">index.php</a>
    <a href="/login.php" class="test-link" target="_blank">login.php</a>
    <a href="/dashboard.php" class="test-link" target="_blank">dashboard.php</a>
    
    <h2>6. Environment Variables</h2>
    <details>
        <summary>Click to show all $_SERVER variables</summary>
        <pre><?php print_r($_SERVER); ?></pre>
    </details>
    
    <hr>
    <p><strong>Instructions:</strong></p>
    <ol>
        <li>If mod_rewrite is not loaded, enable it in Apache configuration</li>
        <li>Make sure AllowOverride is set to "All" in your Apache virtual host</li>
        <li>Check Apache error logs for any .htaccess syntax errors</li>
        <li>Test the links above to see which ones work</li>
    </ol>
</body>
</html>
