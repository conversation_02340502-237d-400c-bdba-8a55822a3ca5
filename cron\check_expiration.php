<?php
/**
 * Cron Job: Check subscription expiration and disable Jellyfin accounts
 * Run this script every hour to check for expired subscriptions
 * 
 * Usage: php cron/check_expiration.php
 * Or set up as cron job: 0 * * * * /usr/bin/php /path/to/your/site/cron/check_expiration.php
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory
$rootDir = dirname(__DIR__);
chdir($rootDir);

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Log function
function writeLog($message) {
    $logFile = 'logs/expiration_check.log';
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

writeLog("=== Expiration Check Started ===");

try {
    // Get all active subscriptions that have expired
    $stmt = $pdo->prepare("
        SELECT us.*, u.username, ju.jellyfin_user_id, ju.jellyfin_username
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
        WHERE us.status = 'active'
        AND us.end_date < NOW()
        ORDER BY us.end_date ASC
    ");
    $stmt->execute();
    $expiredSubscriptions = $stmt->fetchAll();
    
    writeLog("Found " . count($expiredSubscriptions) . " expired subscriptions");
    
    $disabledCount = 0;
    $errorCount = 0;
    
    foreach ($expiredSubscriptions as $subscription) {
        $username = $subscription['username'];
        $endDate = $subscription['end_date'];
        $jellyfinUserId = $subscription['jellyfin_user_id'];
        
        writeLog("Processing expired subscription for user: {$username} (expired: {$endDate})");
        
        try {
            $pdo->beginTransaction();
            
            // Update subscription status to expired
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET status = 'expired', updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$subscription['id']]);
            
            // Disable Jellyfin user if exists
            if ($jellyfinUserId) {
                try {
                    $jellyfin = new JellyfinAPI();
                    
                    // Check if user exists in Jellyfin
                    $jellyfinUser = $jellyfin->getUser($jellyfinUserId);
                    if ($jellyfinUser) {
                        // Check if already disabled
                        if (!$jellyfinUser['Policy']['IsDisabled']) {
                            // Disable the user
                            $result = $jellyfin->setUserEnabled($jellyfinUserId, false);
                            if ($result) {
                                writeLog("✅ Disabled Jellyfin user: {$username} (ID: {$jellyfinUserId})");
                                $disabledCount++;
                            } else {
                                writeLog("❌ Failed to disable Jellyfin user: {$username}");
                                $errorCount++;
                            }
                        } else {
                            writeLog("ℹ️  Jellyfin user already disabled: {$username}");
                        }
                    } else {
                        writeLog("⚠️  Jellyfin user not found: {$username} (ID: {$jellyfinUserId})");
                    }
                } catch (Exception $e) {
                    writeLog("❌ Error disabling Jellyfin user {$username}: " . $e->getMessage());
                    $errorCount++;
                }
            } else {
                writeLog("ℹ️  No Jellyfin user ID for: {$username}");
            }
            
            // Log activity
            $stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, details, created_at) 
                VALUES (?, 'subscription_expired', ?, NOW())
            ");
            $stmt->execute([
                $subscription['user_id'], 
                "Subscription expired and Jellyfin account disabled - End date: {$endDate}"
            ]);
            
            $pdo->commit();
            writeLog("✅ Successfully processed expiration for user: {$username}");
            
        } catch (Exception $e) {
            $pdo->rollback();
            writeLog("❌ Error processing expiration for user {$username}: " . $e->getMessage());
            $errorCount++;
        }
    }
    
    // Check for users with expired subscriptions but still enabled in Jellyfin
    writeLog("--- Checking for inconsistencies ---");
    
    $stmt = $pdo->prepare("
        SELECT u.username, ju.jellyfin_user_id
        FROM users u
        JOIN jellyfin_users ju ON u.id = ju.user_id
        WHERE u.id NOT IN (
            SELECT user_id FROM user_subscriptions 
            WHERE status = 'active' AND end_date > NOW()
        )
    ");
    $stmt->execute();
    $usersWithoutActiveSubscription = $stmt->fetchAll();
    
    foreach ($usersWithoutActiveSubscription as $user) {
        try {
            $jellyfin = new JellyfinAPI();
            $jellyfinUser = $jellyfin->getUser($user['jellyfin_user_id']);
            
            if ($jellyfinUser && !$jellyfinUser['Policy']['IsDisabled']) {
                writeLog("⚠️  Found enabled Jellyfin user without active subscription: {$user['username']}");
                
                // Disable the user
                $result = $jellyfin->setUserEnabled($user['jellyfin_user_id'], false);
                if ($result) {
                    writeLog("✅ Disabled orphaned Jellyfin user: {$user['username']}");
                    $disabledCount++;
                } else {
                    writeLog("❌ Failed to disable orphaned Jellyfin user: {$user['username']}");
                    $errorCount++;
                }
            }
        } catch (Exception $e) {
            writeLog("❌ Error checking orphaned user {$user['username']}: " . $e->getMessage());
            $errorCount++;
        }
    }
    
    writeLog("=== Expiration Check Completed ===");
    writeLog("Processed: " . count($expiredSubscriptions) . " expired subscriptions");
    writeLog("Disabled: {$disabledCount} Jellyfin accounts");
    writeLog("Errors: {$errorCount}");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error during expiration check: " . $e->getMessage());
    exit(1);
}

writeLog("=== End of Expiration Check ===");
?>
