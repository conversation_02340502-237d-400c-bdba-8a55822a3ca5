<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/JellyfinAPI.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';

// Handle subscription actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    $subscription_id = (int)($_POST['subscription_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'approve_payment':
                $pdo->beginTransaction();

                // Get subscription details including package max_sessions
                $stmt = $pdo->prepare("
                    SELECT us.*, u.username, ju.jellyfin_user_id, p.duration_days, p.max_simultaneous_sessions
                    FROM user_subscriptions us
                    JOIN users u ON us.user_id = u.id
                    LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
                    JOIN packages p ON us.package_id = p.id
                    WHERE us.id = ?
                ");
                $stmt->execute([$subscription_id]);
                $subscription = $stmt->fetch();

                if ($subscription && $subscription['status'] === 'pending') {
                    // Update payment transaction
                    $stmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'completed', paid_at = NOW(), updated_at = NOW()
                        WHERE subscription_id = ?
                    ");
                    $stmt->execute([$subscription_id]);

                    // Update subscription
                    $start_date = date('Y-m-d H:i:s');
                    $end_date = date('Y-m-d H:i:s', strtotime("+{$subscription['duration_days']} days"));

                    $stmt = $pdo->prepare("
                        UPDATE user_subscriptions
                        SET status = 'active', start_date = ?, end_date = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$start_date, $end_date, $subscription_id]);

                    // Enable user in Jellyfin and grant access to all libraries with package max sessions
                    if ($subscription['jellyfin_user_id']) {
                        try {
                            $jellyfin = new JellyfinAPI();
                            $maxSessions = $subscription['max_simultaneous_sessions'] ?? 1;
                            $jellyfin->enableAllLibraries($subscription['jellyfin_user_id'], $maxSessions);

                            // Update database record
                            $stmt = $pdo->prepare("UPDATE jellyfin_users SET max_simultaneous_sessions = ? WHERE jellyfin_user_id = ?");
                            $stmt->execute([$maxSessions, $subscription['jellyfin_user_id']]);

                        } catch (Exception $e) {
                            error_log('Failed to enable Jellyfin user: ' . $e->getMessage());
                        }
                    }

                    // Award referral points if user was referred
                    require_once '../includes/ReferralSystem.php';
                    $referralSystem = new ReferralSystem($pdo);
                    $referralSystem->awardReferralPointsInTransaction($subscription['user_id'], $subscription['payment_amount'] ?? null, $subscription_id);

                    $pdo->commit();

                    $message = "อนุมัติการชำระเงินสำหรับ {$subscription['username']} เรียบร้อยแล้ว";
                    $message_type = 'success';

                    log_activity($_SESSION['user_id'], 'payment_approved', "Approved payment for user: {$subscription['username']}");
                } else {
                    $pdo->rollBack();
                    $message = 'ไม่พบการสมัครสมาชิกหรือสถานะไม่ถูกต้อง';
                    $message_type = 'error';
                }
                break;
                
            case 'reject_payment':
                $pdo->beginTransaction();

                $stmt = $pdo->prepare("
                    UPDATE user_subscriptions
                    SET status = 'cancelled', updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$subscription_id]);

                $stmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'cancelled', updated_at = NOW()
                    WHERE subscription_id = ?
                ");
                $stmt->execute([$subscription_id]);

                $pdo->commit();

                $message = 'ปฏิเสธการชำระเงินเรียบร้อยแล้ว';
                $message_type = 'warning';

                log_activity($_SESSION['user_id'], 'payment_rejected', "Rejected payment for subscription ID: {$subscription_id}");
                break;
        }
    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $message_type = 'error';
        error_log('Package management error: ' . $e->getMessage());
    }
}

// Get all subscriptions
$stmt = $pdo->prepare("
    SELECT us.*, u.username, COALESCE(u.email, CONCAT(u.username, '@local')) as email,
           p.name as package_name, p.duration_days, p.price,
           pt.transaction_ref, pt.qr_code_url, pt.slip_image, pt.status as payment_status, pt.paid_at
    FROM user_subscriptions us
    JOIN users u ON us.user_id = u.id
    JOIN packages p ON us.package_id = p.id
    LEFT JOIN payment_transactions pt ON us.id = pt.subscription_id
    ORDER BY us.created_at DESC
");
$stmt->execute();
$subscriptions = $stmt->fetchAll();

// Get subscription statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_subscriptions,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_subscriptions,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_subscriptions,
        SUM(CASE WHEN status = 'active' THEN payment_amount ELSE 0 END) as total_revenue
    FROM user_subscriptions
");
$stmt->execute();
$stats = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการแพ็คเกจ - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="../assets/images/favicon.svg">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #333;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }
        .subscriptions-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
        }
        .subscriptions-table th,
        .subscriptions-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .subscriptions-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .subscriptions-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-pending { background: #ffc107; color: #000; }
        .status-active { background: #28a745; color: white; }
        .status-expired { background: #dc3545; color: white; }
        .status-cancelled { background: #6c757d; color: white; }
        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            text-decoration: none;
            display: inline-block;
            margin: 0.1rem;
            transition: all 0.3s ease;
        }
        .btn-approve { background: #28a745; color: white; }
        .btn-reject { background: #dc3545; color: white; }
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="add-package.php" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="auto-verification.php" class="dropdown-item">เช็คสลิปอัตโนมัติ</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">จัดการแพ็คเกจและการชำระเงิน</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">อนุมัติการชำระเงินและจัดการสมาชิกภาพ</p>
            </div>

            <?php if ($message): ?>
                <div class="flash-message flash-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid fade-in-scroll">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_subscriptions']; ?></div>
                    <div class="stat-label">การสมัครทั้งหมด</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['pending_subscriptions']; ?></div>
                    <div class="stat-label">รอการอนุมัติ</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_subscriptions']; ?></div>
                    <div class="stat-label">ใช้งานอยู่</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_revenue'] ?? 0, 0); ?></div>
                    <div class="stat-label">รายได้ (บาท)</div>
                </div>
            </div>

            <!-- Subscriptions Table -->
            <div class="fade-in-scroll" style="overflow-x: auto;">
                <table class="subscriptions-table">
                    <thead>
                        <tr>
                            <th>ผู้ใช้</th>
                            <th>แพ็คเกจ</th>
                            <th>จำนวนเงิน</th>
                            <th>สลิป</th>
                            <th>สถานะ</th>
                            <th>วันที่สมัคร</th>
                            <th>วันหมดอายุ</th>
                            <th>การดำเนินการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($subscriptions as $subscription): ?>
                            <tr>
                                <td>
                                    <div><strong><?php echo htmlspecialchars($subscription['username'] ?? ''); ?></strong></div>
                                    <div style="font-size: 0.8rem; color: #ccc;">
                                        <?php echo htmlspecialchars($subscription['email'] ?? 'ไม่มีอีเมล'); ?>
                                    </div>
                                </td>
                                <td>
                                    <div><strong><?php echo htmlspecialchars($subscription['package_name'] ?? ''); ?></strong></div>
                                    <div style="font-size: 0.8rem; color: #ccc;">
                                        <?php echo $subscription['duration_days'] ?? 0; ?> วัน
                                    </div>
                                </td>
                                <td>
                                    <strong><?php echo number_format($subscription['payment_amount'], 2); ?> บาท</strong>
                                    <?php if ($subscription['transaction_ref']): ?>
                                        <div style="font-size: 0.8rem; color: #ccc;">
                                            Ref: <?php echo htmlspecialchars($subscription['transaction_ref'] ?? ''); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($subscription['slip_image']): ?>
                                        <a href="<?php echo htmlspecialchars($subscription['slip_image'] ?? ''); ?>" target="_blank">
                                            <img src="<?php echo htmlspecialchars($subscription['slip_image'] ?? ''); ?>"
                                                 alt="Payment Slip"
                                                 style="max-width: 60px; max-height: 80px; border: 1px solid #333; border-radius: 3px; cursor: pointer;">
                                        </a>
                                        <div style="font-size: 0.8rem; color: #28a745; margin-top: 0.25rem;">
                                            ✓ มีสลิป
                                        </div>
                                    <?php else: ?>
                                        <span style="color: #ffc107; font-size: 0.8rem;">ยังไม่มีสลิป</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $subscription['status']; ?>">
                                        <?php 
                                        switch($subscription['status']) {
                                            case 'pending': echo 'รอการอนุมัติ'; break;
                                            case 'active': echo 'ใช้งานอยู่'; break;
                                            case 'expired': echo 'หมดอายุ'; break;
                                            case 'cancelled': echo 'ยกเลิก'; break;
                                            default: echo $subscription['status']; break;
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo date('d/m/Y H:i', strtotime($subscription['created_at'])); ?>
                                </td>
                                <td>
                                    <?php if ($subscription['end_date']): ?>
                                        <?php echo date('d/m/Y H:i', strtotime($subscription['end_date'])); ?>
                                        <?php 
                                        $days_left = ceil((strtotime($subscription['end_date']) - time()) / (60 * 60 * 24));
                                        if ($subscription['status'] === 'active' && $days_left > 0): 
                                        ?>
                                            <div style="font-size: 0.8rem; color: #28a745;">
                                                เหลือ <?php echo $days_left; ?> วัน
                                            </div>
                                        <?php elseif ($subscription['status'] === 'active' && $days_left <= 0): ?>
                                            <div style="font-size: 0.8rem; color: #dc3545;">
                                                หมดอายุแล้ว
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span style="color: #888;">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($subscription['status'] === 'pending'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="approve_payment">
                                            <input type="hidden" name="subscription_id" value="<?php echo $subscription['id']; ?>">
                                            <button type="submit" class="action-btn btn-approve"
                                                    onclick="return confirm('อนุมัติการชำระเงินนี้ใช่หรือไม่?')">
                                                อนุมัติ
                                            </button>
                                        </form>
                                        
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="reject_payment">
                                            <input type="hidden" name="subscription_id" value="<?php echo $subscription['id']; ?>">
                                            <button type="submit" class="action-btn btn-reject"
                                                    onclick="return confirm('ปฏิเสธการชำระเงินนี้ใช่หรือไม่?')">
                                                ปฏิเสธ
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <span style="color: #888;">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
