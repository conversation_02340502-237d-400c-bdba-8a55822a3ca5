-- Add slip_hash column to payment_transactions table
-- This prevents duplicate slip usage

USE jellyfin_registration;

-- Add slip_hash column to payment_transactions table if it doesn't exist
ALTER TABLE payment_transactions 
ADD COLUMN IF NOT EXISTS slip_hash VARCHAR(64) NULL AFTER slip_image;

-- Add index for better performance when checking duplicates
ALTER TABLE payment_transactions 
ADD INDEX IF NOT EXISTS idx_slip_hash (slip_hash);

-- Show updated table structure
DESCRIBE payment_transactions;

-- Show current payment transactions
SELECT id, user_id, amount, slip_image, slip_hash, status, created_at 
FROM payment_transactions 
ORDER BY created_at DESC 
LIMIT 5;
