/* Film Noir Detective Movie Website CSS Animations */

/* Enhanced Cinema Logo Animations */
@keyframes cinemaSpotlight {
    0% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3),
                    0 0 40px rgba(255, 215, 0, 0.2),
                    0 0 60px rgba(255, 215, 0, 0.1);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.5),
                    0 0 60px rgba(255, 215, 0, 0.3),
                    0 0 90px rgba(255, 215, 0, 0.2);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3),
                    0 0 40px rgba(255, 215, 0, 0.2),
                    0 0 60px rgba(255, 215, 0, 0.1);
    }
}

@keyframes cinemaFlicker {
    0%, 100% { opacity: 1; }
    2% { opacity: 0.95; }
    4% { opacity: 1; }
    6% { opacity: 0.98; }
    8% { opacity: 1; }
    10% { opacity: 0.97; }
    12% { opacity: 1; }
}

/* Enhanced Cinema Header */
.main-header {
    position: relative;
    overflow: hidden;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 215, 0, 0.1),
        transparent);
    animation: cinemaSpotlight 4s ease-in-out infinite;
    z-index: 1;
}

.main-header .container {
    position: relative;
    z-index: 2;
}

/* Cinema Logo Enhancement */
.logo {
    position: relative;
    z-index: 3;
}

.logo::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle,
        rgba(255, 215, 0, 0.1) 0%,
        transparent 70%);
    border-radius: 50%;
    animation: cinemaSpotlight 3s ease-in-out infinite;
    z-index: -1;
}

.logo img {
    animation: cinemaFlicker 8s ease-in-out infinite;
}

/* Film Grain Overlay Animation */
@keyframes filmGrain {
    0% { transform: translate(0, 0) rotate(0deg); opacity: 0.8; }
    10% { transform: translate(-5%, -10%) rotate(1deg); opacity: 0.7; }
    20% { transform: translate(-10%, 5%) rotate(-1deg); opacity: 0.9; }
    30% { transform: translate(5%, -15%) rotate(2deg); opacity: 0.6; }
    40% { transform: translate(-15%, -5%) rotate(-2deg); opacity: 0.8; }
    50% { transform: translate(10%, 10%) rotate(1deg); opacity: 0.7; }
    60% { transform: translate(-10%, 15%) rotate(-1deg); opacity: 0.9; }
    70% { transform: translate(15%, -10%) rotate(2deg); opacity: 0.6; }
    80% { transform: translate(-5%, 5%) rotate(-2deg); opacity: 0.8; }
    90% { transform: translate(5%, -5%) rotate(1deg); opacity: 0.7; }
    100% { transform: translate(0, 0) rotate(0deg); opacity: 0.8; }
}

.film-grain-overlay {
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle, transparent 1px, rgba(255,255,255,0.15) 1px),
        radial-gradient(circle, transparent 1px, rgba(0,0,0,0.3) 1px);
    background-size: 3px 3px, 7px 7px;
    background-position: 0 0, 3px 3px;
    animation: filmGrain 0.2s steps(10) infinite;
    pointer-events: none;
    z-index: 1000;
    mix-blend-mode: overlay;
}

/* Fade-in animations for text and images */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shadowPulse {
    0% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.1); }
    50% { box-shadow: 0 0 40px rgba(255, 255, 255, 0.3), 0 0 60px rgba(255, 255, 255, 0.1); }
    100% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.1); }
}

/* Fade-in classes */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
    opacity: 0;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
    opacity: 0;
}

.delay-1 { animation-delay: 0.2s; }
.delay-2 { animation-delay: 0.4s; }
.delay-3 { animation-delay: 0.6s; }

/* Scroll-triggered fade-in */
.fade-in-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.fade-in-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Film Noir Button Hover Effects */
.noir-button {
    position: relative;
    background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
    border: 2px solid #666;
    color: #fff;
    padding: 12px 30px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: bold;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.noir-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.noir-button:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 30px rgba(255, 255, 255, 0.1);
    border-color: #999;
}

.noir-button:hover::before {
    left: 100%;
}

.noir-button:active {
    transform: translateY(0);
    box-shadow: 
        0 2px 10px rgba(0, 0, 0, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Play Trailer Button Special Effect */
.play-trailer-btn {
    position: relative;
    background: radial-gradient(circle, #2d2d2d, #1a1a1a);
    border: 3px solid #fff;
    color: #fff;
    padding: 15px 40px;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 3px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.4s ease;
}

.play-trailer-btn::after {
    content: '▶';
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    transition: all 0.3s ease;
}

.play-trailer-btn:hover {
    background: radial-gradient(circle, #fff, #e0e0e0);
    color: #000;
    transform: scale(1.05);
    box-shadow: 
        0 0 50px rgba(255, 255, 255, 0.5),
        inset 0 0 20px rgba(0, 0, 0, 0.1);
    animation: shadowPulse 2s infinite;
}

.play-trailer-btn:hover::after {
    left: 25px;
    color: #000;
}

/* Dramatic text effects */
.dramatic-text {
    position: relative;
    color: #fff;
    text-shadow: 
        2px 2px 4px rgba(0, 0, 0, 0.8),
        0 0 10px rgba(255, 255, 255, 0.1);
}

.dramatic-text::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    color: rgba(255, 255, 255, 0.1);
    z-index: -1;
    transform: translate(2px, 2px);
}

/* Venetian blind effect */
@keyframes venetianBlind {
    0% { clip-path: inset(0 0 100% 0); }
    100% { clip-path: inset(0 0 0 0); }
}

.venetian-blind {
    animation: venetianBlind 1s ease-out forwards;
}

/* Typewriter effect */
@keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
}

.typewriter {
    overflow: hidden;
    border-right: 2px solid #fff;
    white-space: nowrap;
    animation: typewriter 3s steps(40) forwards, blink 0.75s step-end infinite;
}

@keyframes blink {
    from, to { border-color: transparent; }
    50% { border-color: #fff; }
}

/* Spotlight effect */
.spotlight {
    position: relative;
    overflow: hidden;
}

.spotlight::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

.spotlight:hover::before {
    transform: scale(1);
}
