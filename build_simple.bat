@echo off
title Build Jellyfin Admin Tools (Simple)
echo.
echo ========================================
echo    Building Admin Tools (Simple)
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Install required packages
echo Installing required packages...
pip install requests pyinstaller mysql-connector-python bcrypt

REM Build executable (simple version)
echo Building executable...
pyinstaller --onefile --windowed --name "JellyfinAdminTools" AdminTools.py

if errorlevel 1 (
    echo.
    echo Error: Failed to build executable
    echo Trying alternative build method...
    
    REM Try without windowed mode
    pyinstaller --onefile --name "JellyfinAdminTools" AdminTools.py
    
    if errorlevel 1 (
        echo Error: Build failed completely
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo    Build completed successfully!
echo ========================================
echo.

REM Check if executable was created
if exist "dist\JellyfinAdminTools.exe" (
    echo Executable location: dist\JellyfinAdminTools.exe
    echo.
    echo Moving executable to current directory...
    copy "dist\JellyfinAdminTools.exe" "JellyfinAdminTools.exe"
    echo.
    echo You can now run JellyfinAdminTools.exe
) else (
    echo Warning: Executable not found in expected location
    echo Please check the dist folder
)

echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
echo OR
echo Username: jellyfin_admin  
echo Password: jellyfin123
echo.
pause
