<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create LINE QR Code - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        .qr-generator {
            background: rgba(0, 195, 0, 0.1);
            border: 1px solid #00c300;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .form-group {
            margin: 20px 0;
            text-align: left;
        }
        .form-group label {
            display: block;
            color: #00c300;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #00c300;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 16px;
        }
        .btn {
            background: #00c300;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
        }
        .btn:hover {
            background: #00a300;
        }
        .qr-preview {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }
        .qr-preview img {
            max-width: 100%;
            height: auto;
        }
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #FFD700;">🎬 Jellyfin by James</h1>
        <h2 style="color: #00c300;">📱 สร้าง LINE QR Code</h2>
        
        <div class="qr-generator">
            <h3 style="color: #00c300; margin-top: 0;">🔧 QR Code Generator</h3>
            
            <div class="form-group">
                <label for="lineUrl">LINE Group URL:</label>
                <input type="text" id="lineUrl" placeholder="https://line.me/ti/g/your-group-id" value="https://line.me/ti/g/jellyfin-support">
            </div>
            
            <div class="form-group">
                <label for="qrSize">ขนาด QR Code:</label>
                <select id="qrSize" style="width: 100%; padding: 10px; border: 1px solid #00c300; border-radius: 5px; background: rgba(255, 255, 255, 0.1); color: #fff;">
                    <option value="200">200x200 pixels</option>
                    <option value="300" selected>300x300 pixels</option>
                    <option value="400">400x400 pixels</option>
                    <option value="500">500x500 pixels</option>
                </select>
            </div>
            
            <button onclick="generateQR()" class="btn">🎯 สร้าง QR Code</button>
            
            <div id="qrPreview" class="qr-preview">
                <h4 style="color: #000; margin-top: 0;">QR Code Preview</h4>
                <img id="qrImage" src="" alt="Generated QR Code">
                <div style="margin-top: 15px;">
                    <button onclick="downloadQR()" class="btn">💾 ดาวน์โหลด</button>
                    <button onclick="copyQRUrl()" class="btn">📋 คัดลอก URL</button>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3 style="color: #FFD700;">📋 วิธีใช้งาน</h3>
            <ol style="color: #ccc; line-height: 1.8;">
                <li><strong>ใส่ LINE Group URL:</strong> ใส่ลิงก์กลุ่ม LINE ที่ต้องการสร้าง QR Code</li>
                <li><strong>เลือกขนาด:</strong> เลือกขนาด QR Code ที่เหมาะสม (แนะนำ 300x300)</li>
                <li><strong>สร้าง QR Code:</strong> คลิก "สร้าง QR Code" เพื่อสร้าง</li>
                <li><strong>ดาวน์โหลด:</strong> คลิก "ดาวน์โหลด" เพื่อบันทึกไฟล์</li>
                <li><strong>อัปโหลด:</strong> นำไฟล์ที่ดาวน์โหลดไปใส่ในโฟลเดอร์ <code>line/</code></li>
            </ol>
            
            <h3 style="color: #FFD700;">🎯 ขั้นตอนการติดตั้ง</h3>
            <ol style="color: #ccc; line-height: 1.8;">
                <li>สร้าง QR Code ด้วยเครื่องมือด้านบน</li>
                <li>ดาวน์โหลดไฟล์ QR Code</li>
                <li>เปลี่ยนชื่อไฟล์เป็น <code>line.jpg</code></li>
                <li>อัปโหลดไฟล์ไปยังโฟลเดอร์ <code>line/</code> ในเว็บไซต์</li>
                <li>ตรวจสอบว่า QR Code แสดงในหน้าคู่มือ</li>
            </ol>
            
            <h3 style="color: #FFD700;">⚠️ หมายเหตุ</h3>
            <ul style="color: #ccc; line-height: 1.6;">
                <li>QR Code ที่สร้างจะใช้ API ภายนอก (qrserver.com)</li>
                <li>ตรวจสอบให้แน่ใจว่า LINE Group URL ถูกต้อง</li>
                <li>ไฟล์ควรมีขนาดไม่เกิน 2MB</li>
                <li>รองรับรูปแบบ JPG และ PNG</li>
            </ul>
        </div>
        
        <div style="margin-top: 40px;">
            <a href="/test-line-qr.php" style="color: #00c300; text-decoration: none; padding: 10px 20px; border: 1px solid #00c300; border-radius: 5px; margin-right: 10px;">
                🧪 ทดสอบ QR Code
            </a>
            <a href="/guide" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                ← กลับไปหน้าคู่มือ
            </a>
        </div>
    </div>
    
    <script>
        let currentQRUrl = '';
        
        function generateQR() {
            const lineUrl = document.getElementById('lineUrl').value.trim();
            const qrSize = document.getElementById('qrSize').value;
            
            if (!lineUrl) {
                alert('❌ กรุณาใส่ LINE Group URL');
                return;
            }
            
            // Validate LINE URL
            if (!lineUrl.includes('line.me')) {
                if (!confirm('⚠️ URL ไม่ใช่ลิงก์ LINE\nต้องการสร้าง QR Code ต่อไปหรือไม่?')) {
                    return;
                }
            }
            
            // Generate QR Code URL
            const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}x${qrSize}&data=${encodeURIComponent(lineUrl)}`;
            
            // Show preview
            const qrImage = document.getElementById('qrImage');
            const qrPreview = document.getElementById('qrPreview');
            
            qrImage.src = qrApiUrl;
            currentQRUrl = qrApiUrl;
            qrPreview.style.display = 'block';
            
            console.log('Generated QR Code:', qrApiUrl);
        }
        
        function downloadQR() {
            if (!currentQRUrl) {
                alert('❌ กรุณาสร้าง QR Code ก่อน');
                return;
            }
            
            // Create download link
            const a = document.createElement('a');
            a.href = currentQRUrl;
            a.download = 'line-qr-code.png';
            a.target = '_blank';
            
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            // Show instructions
            setTimeout(() => {
                alert('📥 ไฟล์ถูกดาวน์โหลดแล้ว!\n\n📋 ขั้นตอนต่อไป:\n1. เปลี่ยนชื่อไฟล์เป็น "line.jpg"\n2. อัปโหลดไปยังโฟลเดอร์ "line/" ในเว็บไซต์\n3. ตรวจสอบในหน้าคู่มือ');
            }, 1000);
        }
        
        function copyQRUrl() {
            if (!currentQRUrl) {
                alert('❌ กรุณาสร้าง QR Code ก่อน');
                return;
            }
            
            navigator.clipboard.writeText(currentQRUrl).then(() => {
                alert('📋 คัดลอก URL แล้ว!\nสามารถนำไปใช้ในโค้ดได้');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = currentQRUrl;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('📋 คัดลอก URL แล้ว!');
            });
        }
        
        // Auto-generate sample QR on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('LINE QR Code generator loaded');
        });
    </script>
</body>
</html>
