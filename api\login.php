<?php
/**
 * API Login endpoint สำหรับ Admin Tools
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

require_once '../includes/config.php';

try {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Username and password required']);
        exit;
    }
    
    // ตรวจสอบ admin credentials
    $stmt = $pdo->prepare("SELECT id, username, password, is_admin FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    if ($user) {
        // ตรวจสอบ password (ใช้ password_hash เป็นหลัก)
        $password_valid = false;
        $password_method = '';

        // ตรวจสอบว่ามี password หรือไม่
        if (empty($user['password'])) {
            echo json_encode(['success' => false, 'message' => 'ผู้ใช้นี้ไม่มี password กรุณาติดต่อ admin']);
            exit;
        }

        // 1. ลอง password_verify ก่อน (PHP password_hash)
        if (password_verify($password, $user['password'])) {
            $password_valid = true;
            $password_method = 'password_hash';
        }
        // 2. ลอง plain text (สำหรับ password เก่า)
        elseif ($password === $user['password']) {
            $password_valid = true;
            $password_method = 'plain_text';

            // อัปเดต password เป็น hash ใหม่
            $new_hash = password_hash($password, PASSWORD_DEFAULT);
            $update_stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $update_stmt->execute([$new_hash, $user['id']]);
        }
        // 3. ลอง MD5 (สำหรับ password เก่า)
        elseif (md5($password) === $user['password']) {
            $password_valid = true;
            $password_method = 'md5';

            // อัปเดต password เป็น hash ใหม่
            $new_hash = password_hash($password, PASSWORD_DEFAULT);
            $update_stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $update_stmt->execute([$new_hash, $user['id']]);
        }
        // 4. ลอง SHA256 (สำหรับ password เก่า)
        elseif (hash('sha256', $password) === $user['password']) {
            $password_valid = true;
            $password_method = 'sha256';

            // อัปเดต password เป็น hash ใหม่
            $new_hash = password_hash($password, PASSWORD_DEFAULT);
            $update_stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $update_stmt->execute([$new_hash, $user['id']]);
        }

        if ($password_valid) {
            // ตรวจสอบสิทธิ์ admin
            if ($user['is_admin'] == 1) {
                // ตรวจสอบสถานะ account
                if ($user['status'] !== 'active') {
                    echo json_encode(['success' => false, 'message' => 'บัญชีผู้ใช้ถูกระงับ']);
                    exit;
                }

                // สร้าง session
                session_start();
                $_SESSION['admin_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['is_admin'] = true;

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful',
                    'is_admin' => true,
                    'username' => $user['username'],
                    'user_id' => $user['id'],
                    'password_method' => $password_method
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'ไม่มีสิทธิ์ Admin (is_admin = ' . $user['is_admin'] . ')']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'รหัสผ่านไม่ถูกต้อง']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'ไม่พบผู้ใช้นี้ในระบบ']);
    }
    
} catch (Exception $e) {
    error_log("Login API Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
