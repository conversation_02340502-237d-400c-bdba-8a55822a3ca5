<?php
/*
 * Linux Cron Job: System Health Check
 * Optimized for production Linux environment (*************)
 *
 * Usage: php /var/www/html/cron/linux_health_check.php
 * Cron: every 15 minutes - /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/health_cron.log 2>&1
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory to web root
$webRoot = '/var/www/html';
chdir($webRoot);

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Setup logging
$logFile = '/var/log/jellyfin/health_cron.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

function checkDiskSpace() {
    $freeBytes = disk_free_space('/var/www/html');
    $totalBytes = disk_total_space('/var/www/html');
    $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
    
    return [
        'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
        'total_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
        'used_percent' => round($usedPercent, 1)
    ];
}

function checkLogFiles() {
    $logFiles = [
        '/var/log/jellyfin/payment_cron.log',
        '/var/log/jellyfin/expiration_cron.log',
        '/var/log/jellyfin/manual_cron.log',
        '/var/log/jellyfin/health_cron.log'
    ];
    
    $status = [];
    foreach ($logFiles as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            $modified = filemtime($file);
            $age = time() - $modified;
            
            $status[basename($file)] = [
                'exists' => true,
                'size_mb' => round($size / 1024 / 1024, 2),
                'age_minutes' => round($age / 60, 1),
                'writable' => is_writable($file)
            ];
        } else {
            $status[basename($file)] = ['exists' => false];
        }
    }
    
    return $status;
}

writeLog("=== System Health Check Started ===");

try {
    // 1. Database Health Check
    writeLog("--- Database Health Check ---");
    
    $dbStart = microtime(true);
    $pdo->query("SELECT 1");
    $dbTime = round((microtime(true) - $dbStart) * 1000, 2);
    
    writeLog("✅ Database connection: {$dbTime}ms");
    
    // Check critical tables
    $tables = ['users', 'payment_transactions', 'user_subscriptions', 'packages'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $stmt->fetch()['count'];
        writeLog("✅ Table {$table}: {$count} records");
    }
    
    // Check recent activity
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM payment_transactions
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $recentPayments = $stmt->fetch()['count'];
    writeLog("📊 Recent payments (24h): {$recentPayments}");
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as count FROM user_subscriptions 
        WHERE status = 'active' AND end_date > NOW()
    ");
    $activeSubscriptions = $stmt->fetch()['count'];
    writeLog("📊 Active subscriptions: {$activeSubscriptions}");
    
    // 2. Jellyfin API Health Check
    writeLog("--- Jellyfin API Health Check ---");
    
    try {
        $jellyfinStart = microtime(true);
        $jellyfin = new JellyfinAPI();
        $users = $jellyfin->getUsers();
        $jellyfinTime = round((microtime(true) - $jellyfinStart) * 1000, 2);
        
        if ($users && is_array($users)) {
            $enabledUsers = 0;
            $disabledUsers = 0;
            
            foreach ($users as $user) {
                if (isset($user['Policy']['IsDisabled'])) {
                    if ($user['Policy']['IsDisabled']) {
                        $disabledUsers++;
                    } else {
                        $enabledUsers++;
                    }
                }
            }
            
            writeLog("✅ Jellyfin API: {$jellyfinTime}ms");
            writeLog("📊 Jellyfin users - Enabled: {$enabledUsers}, Disabled: {$disabledUsers}");
        } else {
            writeLog("❌ Jellyfin API: Invalid response");
        }
    } catch (Exception $e) {
        writeLog("❌ Jellyfin API error: " . $e->getMessage());
    }
    
    // 3. File System Health Check
    writeLog("--- File System Health Check ---");
    
    $diskInfo = checkDiskSpace();
    writeLog("💾 Disk space: {$diskInfo['free_gb']}GB free / {$diskInfo['total_gb']}GB total ({$diskInfo['used_percent']}% used)");
    
    if ($diskInfo['used_percent'] > 90) {
        writeLog("⚠️  WARNING: Disk space usage is high ({$diskInfo['used_percent']}%)");
    }
    
    // Check uploads directory
    $uploadsDir = '/var/www/html/uploads/slips';
    if (is_dir($uploadsDir)) {
        $slipCount = count(glob($uploadsDir . '/*'));
        $uploadsSize = 0;
        
        foreach (glob($uploadsDir . '/*') as $file) {
            if (is_file($file)) {
                $uploadsSize += filesize($file);
            }
        }
        
        $uploadsSizeMB = round($uploadsSize / 1024 / 1024, 2);
        writeLog("📁 Slip uploads: {$slipCount} files, {$uploadsSizeMB}MB");
        
        if (!is_writable($uploadsDir)) {
            writeLog("❌ Uploads directory not writable: {$uploadsDir}");
        }
    } else {
        writeLog("❌ Uploads directory not found: {$uploadsDir}");
    }
    
    // 4. Log Files Health Check
    writeLog("--- Log Files Health Check ---");
    
    $logStatus = checkLogFiles();
    foreach ($logStatus as $filename => $info) {
        if ($info['exists']) {
            $status = $info['writable'] ? '✅' : '❌';
            writeLog("{$status} {$filename}: {$info['size_mb']}MB, updated {$info['age_minutes']}min ago");
            
            // Clean up large log files (>100MB)
            if ($info['size_mb'] > 100) {
                $logPath = "/var/log/jellyfin/{$filename}";
                $backupPath = "/var/log/jellyfin/{$filename}.backup";
                
                // Keep last 1000 lines
                exec("tail -n 1000 '{$logPath}' > '{$backupPath}' && mv '{$backupPath}' '{$logPath}'");
                writeLog("🧹 Cleaned up large log file: {$filename}");
            }
        } else {
            writeLog("❌ {$filename}: Not found");
        }
    }
    
    // 5. Process Health Check
    writeLog("--- Process Health Check ---");
    
    // Check if payment checker service is running
    $serviceStatus = shell_exec('systemctl is-active jellyfin-payment-checker 2>/dev/null');
    $serviceStatus = trim($serviceStatus);
    
    if ($serviceStatus === 'active') {
        writeLog("✅ jellyfin-payment-checker service: RUNNING");
    } else {
        writeLog("❌ jellyfin-payment-checker service: {$serviceStatus}");
        
        // Try to restart the service
        writeLog("🔄 Attempting to restart jellyfin-payment-checker service...");
        exec('sudo systemctl restart jellyfin-payment-checker 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            writeLog("✅ Service restarted successfully");
        } else {
            writeLog("❌ Failed to restart service: " . implode(' ', $output));
        }
    }
    
    // 6. Performance Metrics
    writeLog("--- Performance Metrics ---");
    
    // Check system load
    $loadAvg = sys_getloadavg();
    writeLog("📊 System load: " . implode(', ', array_map(function($load) { return round($load, 2); }, $loadAvg)));
    
    // Check memory usage
    $memInfo = file_get_contents('/proc/meminfo');
    preg_match('/MemTotal:\s+(\d+)/', $memInfo, $memTotal);
    preg_match('/MemAvailable:\s+(\d+)/', $memInfo, $memAvailable);
    
    if ($memTotal && $memAvailable) {
        $totalMB = round($memTotal[1] / 1024, 0);
        $availableMB = round($memAvailable[1] / 1024, 0);
        $usedPercent = round((($totalMB - $availableMB) / $totalMB) * 100, 1);
        
        writeLog("💾 Memory: {$availableMB}MB available / {$totalMB}MB total ({$usedPercent}% used)");
        
        if ($usedPercent > 90) {
            writeLog("⚠️  WARNING: Memory usage is high ({$usedPercent}%)");
        }
    }
    
    writeLog("=== System Health Check Completed ===");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
