<?php
// Production Database configuration for embyjames.xyz
define('DB_HOST', '*************');
define('DB_NAME', 'jellyfin_registration');
define('DB_USER', 'root');
define('DB_PASS', 'Wxmujwsofu@1234');

// Jellyfin API configuration
define('JELLYFIN_SERVER_URL', 'https://jellyfin.embyjames.xyz');
define('JELLYFIN_API_KEY', '1ffb3f05e955493fb2e8edfe42bfd52a');

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>
