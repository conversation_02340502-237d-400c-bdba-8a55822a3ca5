<?php
/**
 * Check PHP syntax by including files
 */

echo "<h1>🔍 Checking Cron Files Syntax</h1>";

$cron_files = [
    'cron/linux_payment_check.php',
    'cron/linux_manual_check.php', 
    'cron/linux_health_check.php',
    'cron/linux_expiration_check.php'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>File</th><th>Status</th><th>Details</th></tr>";

foreach ($cron_files as $file) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($file) . "</td>";
    
    if (!file_exists($file)) {
        echo "<td style='color: red;'>❌ Not Found</td>";
        echo "<td>File does not exist</td>";
    } else {
        // Check syntax by reading and parsing
        $content = file_get_contents($file);
        
        // Look for common syntax issues
        $issues = [];
        
        // Check for */ in comments that might break
        if (preg_match('/\/\*.*?\*\/.*?\*/', $content)) {
            $issues[] = "Potential comment syntax issue with */";
        }
        
        // Check for unclosed strings
        $lines = explode("\n", $content);
        foreach ($lines as $line_num => $line) {
            if (strpos($line, '*/') !== false && strpos($line, '/*') === false) {
                $issues[] = "Line " . ($line_num + 1) . ": Potential comment closure issue";
            }
        }
        
        // Try to check basic PHP syntax
        $temp_file = tempnam(sys_get_temp_dir(), 'syntax_check');
        file_put_contents($temp_file, $content);
        
        // Disable error reporting temporarily
        $old_error_reporting = error_reporting(0);
        
        try {
            // Try to include the file
            ob_start();
            
            // Backup $_SERVER to avoid "command line only" errors
            $server_backup = $_SERVER;
            unset($_SERVER['HTTP_HOST']);
            
            include $temp_file;
            
            $_SERVER = $server_backup;
            
            $output = ob_get_clean();
            
            if (empty($issues)) {
                echo "<td style='color: green;'>✅ OK</td>";
                echo "<td>Syntax appears valid</td>";
            } else {
                echo "<td style='color: orange;'>⚠️ Warning</td>";
                echo "<td>" . implode('<br>', $issues) . "</td>";
            }
            
        } catch (ParseError $e) {
            echo "<td style='color: red;'>❌ Parse Error</td>";
            echo "<td>" . htmlspecialchars($e->getMessage()) . "</td>";
        } catch (Exception $e) {
            echo "<td style='color: orange;'>⚠️ Runtime Issue</td>";
            echo "<td>" . htmlspecialchars($e->getMessage()) . "</td>";
        }
        
        error_reporting($old_error_reporting);
        unlink($temp_file);
    }
    
    echo "</tr>";
}

echo "</table>";

// Show first few lines of each file to check for obvious issues
echo "<h2>📄 File Contents Preview</h2>";

foreach ($cron_files as $file) {
    if (file_exists($file)) {
        echo "<h3>" . htmlspecialchars($file) . "</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        
        $lines = file($file);
        for ($i = 0; $i < min(15, count($lines)); $i++) {
            $line_num = $i + 1;
            echo sprintf("%2d: %s", $line_num, htmlspecialchars($lines[$i]));
        }
        
        echo "</pre>";
    }
}

echo "<h2>🔧 Recommended Fix</h2>";
echo "<p>The syntax errors are likely caused by <code>*/</code> in cron schedule comments.</p>";
echo "<p>These have been fixed by changing:</p>";
echo "<ul>";
echo "<li><code>*/5 * * * *</code> → <code>every 5 minutes</code></li>";
echo "<li><code>*/10 * * * *</code> → <code>every 10 minutes</code></li>";
echo "<li><code>*/15 * * * *</code> → <code>every 15 minutes</code></li>";
echo "</ul>";
?>
