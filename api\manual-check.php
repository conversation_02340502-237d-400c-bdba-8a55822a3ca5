<?php
/**
 * API Endpoint for Manual Payment Slip Check
 * สำหรับ AutoPaymentChecker Production Mode
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Include required files - use production config
    if (file_exists('../config/database_production.php')) {
        require_once '../config/database_production.php';
    } else {
        require_once '../includes/config.php';
    }
    
    // Log the check attempt
    error_log("[" . date('Y-m-d H:i:s') . "] Manual slip check started via API endpoint");
    
    // Get pending manual payments (slip uploads)
    $stmt = $pdo->prepare("
        SELECT id, user_id, amount, slip_image, created_at 
        FROM payment_transaction 
        WHERE status = 'pending' 
        AND payment_method = 'slip'
        AND slip_image IS NOT NULL
        AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $pending_payments = $stmt->fetchAll();
    
    $processed_count = 0;
    $processed_payments = [];
    
    if (empty($pending_payments)) {
        echo json_encode([
            'success' => true,
            'message' => 'No pending manual slip payments found',
            'processed_count' => 0,
            'processed_payments' => []
        ]);
        exit;
    }
    
    // Process each pending payment
    foreach ($pending_payments as $payment) {
        // Check if slip image exists
        $slip_path = '../uploads/slips/' . $payment['slip_image'];
        
        if (file_exists($slip_path)) {
            // For now, we'll just log that the slip exists
            // In a real implementation, you might want to add OCR or manual verification
            
            $processed_payments[] = [
                'payment_id' => $payment['id'],
                'amount' => $payment['amount'],
                'slip_image' => $payment['slip_image'],
                'status' => 'slip_found',
                'message' => 'Slip image found, awaiting manual verification'
            ];
            
            $processed_count++;
            
            error_log("[" . date('Y-m-d H:i:s') . "] Manual slip found: ID {$payment['id']}, Amount {$payment['amount']}, Slip: {$payment['slip_image']}");
        } else {
            $processed_payments[] = [
                'payment_id' => $payment['id'],
                'amount' => $payment['amount'],
                'slip_image' => $payment['slip_image'],
                'status' => 'slip_missing',
                'message' => 'Slip image file not found'
            ];
            
            error_log("[" . date('Y-m-d H:i:s') . "] Manual slip missing: ID {$payment['id']}, Slip: {$payment['slip_image']}");
        }
    }
    
    $message = "Manual slip check completed: {$processed_count} slips processed out of " . count($pending_payments) . " pending";
    error_log("[" . date('Y-m-d H:i:s') . "] " . $message);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'processed_count' => $processed_count,
        'total_pending' => count($pending_payments),
        'processed_payments' => $processed_payments
    ]);
    
} catch (Exception $e) {
    error_log("[" . date('Y-m-d H:i:s') . "] Manual slip check error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Manual slip check failed: ' . $e->getMessage(),
        'processed_count' => 0
    ]);
}
?>
