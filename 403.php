<?php
http_response_code(403);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ไม่มีสิทธิ์เข้าถึง - Jellyfin by <PERSON></title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        .error-content {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.05);
            padding: 3rem;
            border-radius: 12px;
            border: 1px solid #333;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #dc3545;
            text-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
            margin-bottom: 1rem;
            line-height: 1;
        }
        .error-title {
            font-size: 2rem;
            color: #fff;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.1rem;
            color: #ccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn-home {
            background: linear-gradient(45deg, #007cba, #0056b3);
            color: #fff;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.3);
        }
        .btn-login {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: #fff;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            .error-title {
                font-size: 1.5rem;
            }
            .error-content {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <div class="error-container">
        <div class="error-content fade-in-up">
            <div class="error-code">403</div>
            <h1 class="error-title">ไม่มีสิทธิ์เข้าถึง</h1>
            <p class="error-message">
                ขออภัย คุณไม่มีสิทธิ์ในการเข้าถึงหน้านี้<br>
                กรุณาเข้าสู่ระบบหรือติดต่อผู้ดูแลระบบ
            </p>
            <div class="error-actions">
                <a href="/login" class="btn-login">เข้าสู่ระบบ</a>
                <a href="/" class="btn-home">กลับหน้าหลัก</a>
            </div>
        </div>
    </div>

    <script>
        // Add lock icon animation
        document.addEventListener('DOMContentLoaded', function() {
            const errorCode = document.querySelector('.error-code');
            
            // Add shake effect on hover
            errorCode.addEventListener('mouseenter', function() {
                this.style.animation = 'shake 0.5s ease-in-out';
            });
            
            errorCode.addEventListener('animationend', function() {
                this.style.animation = '';
            });
        });
        
        // Add shake animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
