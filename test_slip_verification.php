<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test Slip Verification System</h2>";

try {
    echo "<h3>🔍 Step 1: Check Current Manual Payments</h3>";
    
    // Check payments that should be processed by manual cron
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
        ORDER BY pt.created_at ASC
    ");
    $stmt->execute();
    $manualPayments = $stmt->fetchAll();
    
    echo "<p>Found " . count($manualPayments) . " manual payments for processing</p>";
    
    if ($manualPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Package</th><th>Method</th><th>Slip</th><th>Created</th></tr>";
        foreach ($manualPayments as $payment) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['package_name'] ?? 'N/A') . "</td>";
            echo "<td>" . $payment['payment_method'] . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅' : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>ℹ️ No manual payments found. This could mean:</p>";
        echo "<ul>";
        echo "<li>No users have uploaded slips recently</li>";
        echo "<li>All slips have been processed already</li>";
        echo "<li>Payment method is not set to 'manual'</li>";
        echo "</ul>";
    }
    
    echo "<h3>🔍 Step 2: Check All Pending Payments with Slips</h3>";
    
    // Check all pending payments with slips (regardless of payment_method)
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $allPendingSlips = $stmt->fetchAll();
    
    echo "<p>Found " . count($allPendingSlips) . " pending payments with slips (any method)</p>";
    
    if ($allPendingSlips) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Method</th><th>Slip File</th><th>File Exists</th><th>Created</th></tr>";
        foreach ($allPendingSlips as $payment) {
            $slipPath = "uploads/slips/" . $payment['slip_image'];
            $fileExists = file_exists($slipPath);
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . ($payment['payment_method'] ?? 'NULL') . "</td>";
            echo "<td>" . basename($payment['slip_image']) . "</td>";
            echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🛠️ Step 3: Fix Payment Methods</h3>";
    
    // Find payments with slips but no payment_method set
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM payment_transactions
        WHERE slip_image IS NOT NULL
        AND slip_image != ''
        AND (payment_method IS NULL OR payment_method = '')
        AND status = 'pending'
    ");
    $stmt->execute();
    $needsFixCount = $stmt->fetchColumn();
    
    echo "<p>Payments needing payment_method fix: {$needsFixCount}</p>";
    
    if ($needsFixCount > 0) {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='fix_payment_methods' value='1'>";
        echo "<button type='submit' style='background: orange; color: white; padding: 10px; border: none; cursor: pointer;'>Fix Payment Methods</button>";
        echo "</form>";
    }
    
    echo "<h3>🧪 Step 4: Run Manual Verification Test</h3>";
    
    if ($manualPayments) {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='run_manual_verification' value='1'>";
        echo "<button type='submit' style='background: green; color: white; padding: 10px; border: none; cursor: pointer;'>Run Manual Verification Test</button>";
        echo "</form>";
    } else {
        echo "<p>⚠️ No manual payments to test</p>";
    }
    
    echo "<h3>📊 Step 5: Verification Statistics</h3>";
    
    // Get verification statistics
    $stats = [];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'pending' AND slip_image IS NOT NULL");
    $stmt->execute();
    $stats['pending_slips'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'verified' AND slip_image IS NOT NULL");
    $stmt->execute();
    $stats['verified_slips'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'verified' AND verified_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $stats['verified_today'] = $stmt->fetchColumn();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Pending Slips</td><td style='background: yellow;'>{$stats['pending_slips']}</td></tr>";
    echo "<tr><td>Verified Slips (Total)</td><td style='background: lightgreen;'>{$stats['verified_slips']}</td></tr>";
    echo "<tr><td>Verified Today</td><td style='background: lightblue;'>{$stats['verified_today']}</td></tr>";
    echo "</table>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['fix_payment_methods'])) {
        echo "<h3>🔧 Fixing Payment Methods:</h3>";
        try {
            $stmt = $pdo->prepare("
                UPDATE payment_transactions
                SET payment_method = 'manual'
                WHERE slip_image IS NOT NULL
                AND slip_image != ''
                AND (payment_method IS NULL OR payment_method = '')
                AND status = 'pending'
            ");
            $result = $stmt->execute();
            $affected = $stmt->rowCount();
            
            echo "<p>✅ Fixed {$affected} payment records</p>";
            echo "<p><a href='?'>Refresh Page</a></p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Error fixing payment methods: " . $e->getMessage() . "</p>";
        }
    }
    
    if (isset($_POST['run_manual_verification'])) {
        echo "<h3>🧪 Running Manual Verification Test:</h3>";
        try {
            // Include JellyfinAPI
            require_once 'includes/JellyfinAPI.php';
            $jellyfin = new JellyfinAPI();
            
            // Get one manual payment to test
            $stmt = $pdo->prepare("
                SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
                FROM payment_transactions pt
                JOIN users u ON pt.user_id = u.id
                LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
                LEFT JOIN packages p ON us.package_id = p.id
                WHERE pt.status = 'pending'
                AND pt.payment_method = 'manual'
                AND pt.slip_image IS NOT NULL
                AND pt.slip_image != ''
                ORDER BY pt.created_at ASC
                LIMIT 1
            ");
            $stmt->execute();
            $payment = $stmt->fetch();
            
            if ($payment) {
                echo "<p>Testing payment ID: {$payment['id']} for user: {$payment['username']}</p>";
                
                // Check slip file
                $slipPath = "uploads/slips/" . $payment['slip_image'];
                if (!file_exists($slipPath)) {
                    echo "<p>❌ Slip file not found: {$slipPath}</p>";
                } else {
                    $fileSize = filesize($slipPath);
                    $fileTime = filemtime($slipPath);
                    $paymentTime = strtotime($payment['created_at']);
                    
                    echo "<p>✅ Slip file found: " . basename($slipPath) . " ({$fileSize} bytes)</p>";
                    
                    // Check auto-verification criteria
                    $isValidSize = $fileSize > 10000 && $fileSize < 10000000;
                    $isRecent = abs($fileTime - $paymentTime) < 3600;
                    $isSmallAmount = $payment['amount'] <= 100;
                    
                    // Check user history
                    $historyStmt = $pdo->prepare("
                        SELECT COUNT(*) as verified_count FROM payment_transactions
                        WHERE user_id = ? AND status IN ('verified', 'completed')
                    ");
                    $historyStmt->execute([$payment['user_id']]);
                    $verifiedCount = $historyStmt->fetch()['verified_count'];
                    $isTrustedUser = $verifiedCount >= 2;
                    
                    echo "<p>Verification criteria:</p>";
                    echo "<ul>";
                    echo "<li>Valid size (10KB-10MB): " . ($isValidSize ? '✅' : '❌') . "</li>";
                    echo "<li>Recent file (within 1h): " . ($isRecent ? '✅' : '❌') . "</li>";
                    echo "<li>Small amount (≤100 THB): " . ($isSmallAmount ? '✅' : '❌') . "</li>";
                    echo "<li>Trusted user (≥2 payments): " . ($isTrustedUser ? '✅' : '❌') . " ({$verifiedCount} previous)</li>";
                    echo "</ul>";
                    
                    $shouldAutoVerify = ($isSmallAmount || $isTrustedUser) && $isRecent && $isValidSize;
                    
                    if ($shouldAutoVerify) {
                        echo "<p>✅ Auto-verification criteria met. Verifying...</p>";
                        
                        $pdo->beginTransaction();
                        
                        // Update payment status
                        $updateStmt = $pdo->prepare("
                            UPDATE payment_transactions
                            SET status = 'verified',
                                verified_at = NOW(),
                                admin_notes = 'Test verification - auto-verified'
                            WHERE id = ?
                        ");
                        $updateStmt->execute([$payment['id']]);
                        
                        echo "<p>✅ Payment verified successfully!</p>";
                        
                        $pdo->commit();
                        
                    } else {
                        echo "<p>❌ Auto-verification criteria not met</p>";
                        echo "<p>This payment would need manual admin review</p>";
                    }
                }
            } else {
                echo "<p>❌ No manual payments found for testing</p>";
            }
            
        } catch (Exception $e) {
            if (isset($pdo)) $pdo->rollback();
            echo "<p>❌ Test error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<hr>";
echo "<p><a href='/admin'>← กลับไปหน้า Admin</a></p>";
echo "<p><a href='/check_cron_status.php'>🔍 Check Cron Status</a></p>";
echo "<p><a href='/debug_slip_verification.php'>🔍 Debug Slip Verification</a></p>";
?>
