-- Add packages and payment system tables
-- Run this SQL to add the new tables to existing database

-- Packages table
CREATE TABLE IF NOT EXISTS packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    duration_days INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    max_simultaneous_sessions INT DEFAULT 1,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_is_active (is_active)
);

-- User subscriptions
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    status ENUM('pending', 'active', 'expired', 'cancelled') DEFAULT 'pending',
    payment_amount DECIMAL(10,2) NOT NULL,
    payment_reference VARCHAR(255),
    qr_code_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_package_id (package_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date)
);

-- Payment transactions
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subscription_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'promptpay',
    transaction_ref VARCHAR(255),
    qr_code_url VARCHAR(500),
    slip_image VARCHAR(500),
    slip_hash VARCHAR(64) NULL,
    status ENUM('pending', 'pending_verification', 'completed', 'failed', 'cancelled', 'manual_review') DEFAULT 'pending',
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_transaction_ref (transaction_ref),
    INDEX idx_slip_hash (slip_hash)
);

-- Insert default packages
INSERT IGNORE INTO packages (name, duration_days, price, max_simultaneous_sessions, description, is_active) VALUES
('15 วัน', 15, 60.00, 1, 'แพ็คเกจ 15 วัน', TRUE),
('30 วัน', 30, 100.00, 1, 'แพ็คเกจ 30 วัน', TRUE);

-- Add PromptPay phone number to server config
INSERT IGNORE INTO server_config (config_key, config_value, description) VALUES
('promptpay_phone', '0820722972', 'PromptPay phone number for payments');
