<?php
// Test file upload functionality
session_start();

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Test user ID
}

$message = '';
$message_type = 'info';

if ($_POST && isset($_FILES['test_file'])) {
    try {
        echo "<h3>🔍 การทดสอบการอัปโหลดไฟล์</h3>";
        
        // Check upload directory
        $upload_dir = 'uploads/slips/';
        echo "<p><strong>📁 ตรวจสอบโฟลเดอร์:</strong> {$upload_dir}</p>";
        
        if (!is_dir($upload_dir)) {
            echo "<p>❌ โฟลเดอร์ไม่มีอยู่ - กำลังสร้าง...</p>";
            if (mkdir($upload_dir, 0755, true)) {
                echo "<p>✅ สร้างโฟลเดอร์สำเร็จ</p>";
            } else {
                throw new Exception('ไม่สามารถสร้างโฟลเดอร์ได้');
            }
        } else {
            echo "<p>✅ โฟลเดอร์มีอยู่แล้ว</p>";
        }
        
        // Check permissions
        if (is_writable($upload_dir)) {
            echo "<p>✅ โฟลเดอร์สามารถเขียนไฟล์ได้</p>";
        } else {
            echo "<p>❌ โฟลเดอร์ไม่สามารถเขียนไฟล์ได้</p>";
        }
        
        // Check file upload
        $file = $_FILES['test_file'];
        echo "<p><strong>📄 ข้อมูลไฟล์:</strong></p>";
        echo "<ul>";
        echo "<li>ชื่อไฟล์: " . htmlspecialchars($file['name']) . "</li>";
        echo "<li>ขนาด: " . number_format($file['size']) . " bytes</li>";
        echo "<li>ประเภท: " . htmlspecialchars($file['type']) . "</li>";
        echo "<li>Error Code: " . $file['error'] . "</li>";
        echo "<li>Temp File: " . htmlspecialchars($file['tmp_name']) . "</li>";
        echo "</ul>";
        
        // Check upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในระบบ',
                UPLOAD_ERR_FORM_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในฟอร์ม',
                UPLOAD_ERR_PARTIAL => 'ไฟล์อัปโหลดไม่สมบูรณ์',
                UPLOAD_ERR_NO_FILE => 'ไม่มีไฟล์ที่อัปโหลด',
                UPLOAD_ERR_NO_TMP_DIR => 'ไม่พบโฟลเดอร์ชั่วคราว',
                UPLOAD_ERR_CANT_WRITE => 'ไม่สามารถเขียนไฟล์ได้',
                UPLOAD_ERR_EXTENSION => 'การอัปโหลดถูกหยุดโดย extension'
            ];
            throw new Exception($upload_errors[$file['error']] ?? 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์');
        }
        
        echo "<p>✅ ไม่มี upload errors</p>";
        
        // Check if temp file exists
        if (!file_exists($file['tmp_name'])) {
            throw new Exception('ไฟล์ชั่วคราวไม่พบ');
        }
        
        echo "<p>✅ ไฟล์ชั่วคราวพบแล้ว</p>";
        
        // Validate file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('ประเภทไฟล์ไม่ถูกต้อง');
        }
        
        echo "<p>✅ ประเภทไฟล์ถูกต้อง</p>";
        
        // Check file size
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('ไฟล์มีขนาดใหญ่เกินไป');
        }
        
        echo "<p>✅ ขนาดไฟล์เหมาะสม</p>";
        
        // Try to get image info
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            throw new Exception('ไฟล์ไม่ใช่รูปภาพที่ถูกต้อง');
        }
        
        echo "<p>✅ ไฟล์เป็นรูปภาพที่ถูกต้อง ({$image_info[0]}x{$image_info[1]})</p>";
        
        // Generate filename
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $new_filename = 'test_' . time() . '_' . uniqid() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;
        
        echo "<p><strong>📝 ชื่อไฟล์ใหม่:</strong> {$new_filename}</p>";
        echo "<p><strong>📍 เส้นทางเต็ม:</strong> {$upload_path}</p>";
        
        // Try to move file
        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
            echo "<p>✅ อัปโหลดไฟล์สำเร็จ!</p>";
            
            // Verify file exists
            if (file_exists($upload_path)) {
                $file_size = filesize($upload_path);
                echo "<p>✅ ไฟล์ถูกบันทึกแล้ว (ขนาด: " . number_format($file_size) . " bytes)</p>";
                
                // Calculate hash
                $file_hash = hash_file('sha256', $upload_path);
                echo "<p><strong>🔐 Hash:</strong> {$file_hash}</p>";
                
                $message = 'ทดสอบการอัปโหลดสำเร็จ!';
                $message_type = 'success';
            } else {
                throw new Exception('ไฟล์ไม่ได้ถูกบันทึก');
            }
        } else {
            throw new Exception('ไม่สามารถย้ายไฟล์ได้');
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        $message = $e->getMessage();
        $message_type = 'error';
    }
}

// Check PHP configuration
echo "<h3>⚙️ การตั้งค่า PHP</h3>";
echo "<ul>";
echo "<li><strong>upload_max_filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>post_max_size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>max_file_uploads:</strong> " . ini_get('max_file_uploads') . "</li>";
echo "<li><strong>file_uploads:</strong> " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>upload_tmp_dir:</strong> " . (ini_get('upload_tmp_dir') ?: 'Default') . "</li>";
echo "</ul>";

// Check directory permissions
echo "<h3>📁 สิทธิ์โฟลเดอร์</h3>";
$upload_dir = 'uploads/slips/';
if (is_dir($upload_dir)) {
    echo "<ul>";
    echo "<li><strong>โฟลเดอร์มีอยู่:</strong> ✅</li>";
    echo "<li><strong>สามารถอ่านได้:</strong> " . (is_readable($upload_dir) ? '✅' : '❌') . "</li>";
    echo "<li><strong>สามารถเขียนได้:</strong> " . (is_writable($upload_dir) ? '✅' : '❌') . "</li>";
    echo "<li><strong>เส้นทางเต็ม:</strong> " . realpath($upload_dir) . "</li>";
    echo "</ul>";
} else {
    echo "<p>❌ โฟลเดอร์ไม่มีอยู่</p>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการอัปโหลดไฟล์</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .flash-message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        ul {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        h3 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ทดสอบการอัปโหลดไฟล์</h1>
        
        <?php if ($message): ?>
            <div class="flash-message flash-<?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="test_file">เลือกไฟล์รูปภาพเพื่อทดสอบ:</label>
                <input type="file" id="test_file" name="test_file" accept="image/*" required>
            </div>
            <button type="submit">ทดสอบการอัปโหลด</button>
        </form>
        
        <hr style="margin: 30px 0;">
        
        <?php
        // Show existing files
        echo "<h3>📋 ไฟล์ที่มีอยู่ในโฟลเดอร์</h3>";
        $upload_dir = 'uploads/slips/';
        if (is_dir($upload_dir)) {
            $files = glob($upload_dir . '*');
            if ($files) {
                echo "<ul>";
                foreach ($files as $file) {
                    if (is_file($file)) {
                        $filename = basename($file);
                        $size = filesize($file);
                        $date = date('Y-m-d H:i:s', filemtime($file));
                        echo "<li><strong>{$filename}</strong> - " . number_format($size) . " bytes - {$date}</li>";
                    }
                }
                echo "</ul>";
            } else {
                echo "<p>ไม่มีไฟล์ในโฟลเดอร์</p>";
            }
        } else {
            echo "<p>โฟลเดอร์ไม่มีอยู่</p>";
        }
        ?>
    </div>
</body>
</html>
