<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: /dashboard');
    exit();
}

$errors = [];
$success = false;

// Registration is always enabled

// Handle form submission
if ($_POST && empty($errors)) {
    $username = sanitize_input($_POST['username']);
    $phone = sanitize_input($_POST['phone']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $referral_code = sanitize_input($_POST['referral_code'] ?? '');
    
    // Validation
    if (empty($username)) {
        $errors[] = 'กรุณากรอก Username';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username ต้องมีอย่างน้อย 3 ตัวอักษร';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $errors[] = 'Username สามารถใช้ได้เฉพาะตัวอักษร ตัวเลข และขีดล่างเท่านั้น';
    }

    if (empty($phone)) {
        $errors[] = 'เบอร์โทรศัพท์จำเป็นต้องใส่';
    } elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
        $errors[] = 'เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก';
    }

    if (empty($password)) {
        $errors[] = 'กรุณากรอกรหัสผ่าน';
    } elseif (strlen($password) < 6) {
        $errors[] = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
    }

    if ($password !== $confirm_password) {
        $errors[] = 'รหัสผ่านไม่ตรงกัน';
    }
    
    // Check if username or phone already exists
    if (empty($errors)) {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR phone = ?");
        $stmt->execute([$username, $phone]);
        if ($stmt->fetch()) {
            $errors[] = 'Username หรือเบอร์โทรศัพท์นี้มีอยู่ในระบบแล้ว';
        }
    }
    


    // Validate referral code if provided
    $referrer_id = null;
    if (!empty($referral_code)) {
        require_once 'includes/ReferralSystem.php';
        $referralSystem = new ReferralSystem($pdo);
        $referrer = $referralSystem->findUserByReferralCode($referral_code);

        if (!$referrer) {
            $errors[] = 'รหัสแนะนำไม่ถูกต้อง';
        } else {
            $referrer_id = $referrer['id'];
        }
    }
    
    // Create user if no errors
    if (empty($errors)) {
        try {
            $pdo->beginTransaction();
            
            // Create user in local database
            $password_hash = hash_password($password);
            $email_verification_token = generate_random_string();

            // Generate referral code for new user
            require_once 'includes/ReferralSystem.php';
            $referralSystem = new ReferralSystem($pdo);
            $new_referral_code = $referralSystem->generateReferralCode($username);

            $stmt = $pdo->prepare("
                INSERT INTO users (username, phone, password_hash, email_verification_token, referral_code, referred_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$username, $phone, $password_hash, $email_verification_token, $new_referral_code, $referrer_id]);
            $user_id = $pdo->lastInsertId();
            
            // Create user in Jellyfin (disabled by default)
            try {
                $jellyfin = new JellyfinAPI();
                $jellyfin_user = $jellyfin->createUser($username, $password, true); // true = disabled

                if ($jellyfin_user) {
                    // Store Jellyfin user details
                    $stmt = $pdo->prepare("
                        INSERT INTO jellyfin_users (user_id, jellyfin_user_id, jellyfin_username, jellyfin_password, max_simultaneous_sessions, created_at)
                        VALUES (?, ?, ?, ?, 1, NOW())
                    ");
                    $stmt->execute([$user_id, $jellyfin_user['Id'], $username, $password]);
                }
            } catch (Exception $e) {
                // Log the error but don't fail registration
                error_log('Failed to create Jellyfin user: ' . $e->getMessage());
            }
            

            
            // Log activity
            log_activity($user_id, 'user_registered', 'User registered with username: ' . $username);
            
            $pdo->commit();
            
            // Check if email verification is required
            $stmt = $pdo->prepare("SELECT config_value FROM server_config WHERE config_key = 'email_verification_required'");
            $stmt->execute();
            $email_verification_required = $stmt->fetchColumn();
            
            if ($email_verification_required) {
                set_flash_message('Registration successful! Please check your email to verify your account.', 'success');
                // TODO: Send verification email
            } else {
                // Auto-verify and log in
                $stmt = $pdo->prepare("UPDATE users SET email_verified = 1 WHERE id = ?");
                $stmt->execute([$user_id]);
                
                $_SESSION['user_id'] = $user_id;
                $_SESSION['username'] = $username;
                $_SESSION['is_admin'] = false;

                set_flash_message('สมัครสมาชิกสำเร็จ! กรุณาเลือกแพ็คเกจเพื่อเปิดใช้งานบัญชี', 'success');
                header('Location: packages.php');
                exit();
            }
            
            $success = true;
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $errors[] = 'Registration failed. Please try again.';
            error_log('Registration error: ' . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สมัครสมาชิก - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
        <style>
        .referral-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
        }
        .referral-code {
            background: linear-gradient(135deg, #ffc107, #ff8f00);
            color: #000;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 1rem 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffc107;
        }
        .stat-label {
            color: #ccc;
            margin-top: 0.5rem;
        }
        .transactions-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 2rem 0;
        }
        .transactions-table th,
        .transactions-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .transactions-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .copy-button {
            background: #28a745;
            color: #fff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 1rem;
        }
        .copy-button:hover {
            background: #218838;
        }
    </style>
</head>
<body> 
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/" class="nav-link">หน้าแรก</a>
                <a href="/login" class="nav-link">เข้าสู่ระบบ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="form-container fade-in-up">
                <h2 style="text-align: center; margin-bottom: 2rem;">สร้างบัญชีของคุณ</h2>

                <?php display_flash_message(); ?>

                <?php if (!empty($errors)): ?>
                    <div class="flash-message flash-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="flash-message flash-success">
                        สมัครสมาชิกเสร็จสิ้น!
                        <?php if (!empty($email_verification_required)): ?>
                            กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชีของคุณ
                        <?php else: ?>
                            <a href="/login">คลิกที่นี่เพื่อเข้าสู่ระบบ</a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <form method="POST" id="registerForm">
                        <div class="form-group">
                            <label for="username">ชื่อผู้ใช้ *</label>
                            <input type="text" id="username" name="username" class="form-control"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   required placeholder="กรอกชื่อผู้ใช้ของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="phone">เบอร์โทรศัพท์ *</label>
                            <input type="tel" id="phone" name="phone" class="form-control"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                   required placeholder="0812345678" pattern="[0-9]{10}" maxlength="10">
                            <small style="color: #888; font-size: 0.8rem;">กรุณาใส่เบอร์โทรศัพท์ 10 หลัก</small>
                        </div>

                        <div class="form-group">
                            <label for="password">รหัสผ่าน *</label>
                            <input type="password" id="password" name="password" class="form-control"
                                   required placeholder="กรอกรหัสผ่านของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">ยืนยันรหัสผ่าน *</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control"
                                   required placeholder="ยืนยันรหัสผ่านของคุณ">
                        </div>
                        
                        <div class="form-group">
                            <label for="referral_code">รหัสแนะนำ (ไม่บังคับ)</label>
                            <input type="text" id="referral_code" name="referral_code" class="form-control"
                                   value="<?php echo htmlspecialchars($_POST['referral_code'] ?? $_GET['ref'] ?? ''); ?>"
                                   placeholder="รหัสแนะนำจากเพื่อน">
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary noir-button" style="width: 100%;">
                                สร้างบัญชี
                            </button>
                        </div>

                        <div style="text-align: center; margin-top: 1rem;">
                            <p>มีบัญชีอยู่แล้ว? <a href="login.php" style="color: #ccc;">เข้าสู่ระบบที่นี่</a></p>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
