#!/usr/bin/env python3
"""
Auto Payment Checker - GUI Application
ตรวจสอบการชำระเงินอัตโนมัติทั้ง Slip และ PayNoi API ทุก 5 วินาที
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
import re
import json
from datetime import datetime
import requests
import json
from datetime import datetime, timedelta

class AutoPaymentChecker:
    def __init__(self, root):
        self.root = root
        self.root.title("Jellyfin Management System")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        self.root.configure(bg='#2d2d30')

        # Configure minimal style
        self.setup_minimal_styles()
        
        # Load configuration
        self.load_config()

        # Environment detection
        self.is_production = self.detect_environment()

        # Configuration based on environment
        if self.is_production:
            self.setup_production_config()
        else:
            self.setup_local_config()

        # PayNoi API Configuration
        self.paynoi_api_key = "6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446"
        self.paynoi_record_key = "100568"
        self.paynoi_api_url = "https://paynoi.com/api_line"

        # Status variables
        self.cloudflare_started = False
        self.expiration_running = False

        # Variables
        self.is_running = False
        self.check_thread = None
        self.expiration_thread = None
        self.interval = tk.IntVar(value=5)  # Default 5 seconds for payment
        self.expiration_interval = tk.IntVar(value=3600)  # Default 1 hour for expiration
        self.check_count = 0
        self.last_check_time = None
        self.last_expiration_check = None
        
        self.setup_ui()
        self.update_status()

    def load_config(self):
        """Load configuration from JSON file"""
        self.config = {
            "local": {
                "php_path": r"C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe",
                "web_root": r"C:\laragon\www",
                "base_url": "http://localhost"
            },
            "production": {
                "base_url": "https://embyjames.xyz",
                "api_endpoints": {
                    "paynoi_check": "/api/payment-check.php",
                    "manual_check": "/api/manual-check.php",
                    "expiration_check": "/api/expiration-check.php",
                    "health": "/api/health.php"
                }
            }
        }

    def detect_environment(self):
        """Detect if running in production environment"""
        # Check if we're on Linux or if localhost is not accessible
        try:
            if os.name != 'nt':  # Not Windows
                return True

            # Try to access localhost
            response = requests.get("http://localhost", timeout=5)
            return False  # localhost accessible, assume local
        except:
            return True  # localhost not accessible, assume production

    def setup_local_config(self):
        """Setup configuration for local environment"""
        self.php_path = self.config["local"]["php_path"]
        self.web_root = self.config["local"]["web_root"]
        self.base_url = self.config["local"]["base_url"]
        self.paynoi_script = os.path.join(self.web_root, "paynoi-transactions.php")
        self.manual_script = os.path.join(self.web_root, "manual_check_payments.php")
        self.cloudflare_script = os.path.join(self.web_root, "start_cloudflare.bat")
        self.expiration_script = os.path.join(self.web_root, "cron", "check_expiration.php")

    def setup_production_config(self):
        """Setup configuration for production environment"""
        self.base_url = self.config["production"]["base_url"]
        self.api_endpoints = self.config["production"]["api_endpoints"]
        # No local scripts in production mode

    def setup_minimal_styles(self):
        """Setup minimal UI styles for better performance"""
        # Use default system theme for better performance
        pass
        
    def setup_ui(self):
        # Configure root grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # Main frame with simple layout
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky='nsew')
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)  # Log area expands

        # Simple header
        self.create_simple_header(main_frame)

        # Control sections
        self.create_payment_controls(main_frame)
        self.create_expiration_controls(main_frame)

        # Log area
        self.create_simple_log_area(main_frame)

    def create_simple_header(self, parent):
        """Create simple header"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        header_frame.columnconfigure(1, weight=1)

        # Title with environment indicator
        env_text = "Production" if self.is_production else "Local"
        title_text = f"Jellyfin Management System ({env_text})"
        title_label = ttk.Label(header_frame, text=title_text,
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, sticky='w')

        # Status
        self.status_label = ttk.Label(header_frame, text="Status: Stopped",
                                     font=('Arial', 10))
        self.status_label.grid(row=0, column=1, sticky='e')

        # Environment info
        env_info = f"Base URL: {self.base_url}" if hasattr(self, 'base_url') else "Environment: Unknown"
        self.env_label = ttk.Label(header_frame, text=env_info,
                                  font=('Arial', 8), foreground='gray')
        self.env_label.grid(row=1, column=0, columnspan=2, sticky='w')

    def create_payment_controls(self, parent):
        """Create payment control section"""
        payment_frame = ttk.LabelFrame(parent, text="Payment Management", padding="10")
        payment_frame.grid(row=1, column=0, sticky='ew', pady=(0, 5))
        payment_frame.columnconfigure(3, weight=1)

        # Interval
        ttk.Label(payment_frame, text="Interval:").grid(row=0, column=0, sticky='w')
        interval_combo = ttk.Combobox(payment_frame, textvariable=self.interval,
                                     values=[5, 10, 15, 30, 60], state="readonly", width=8)
        interval_combo.grid(row=0, column=1, padx=(5, 5))
        ttk.Label(payment_frame, text="seconds").grid(row=0, column=2, sticky='w')

        # Buttons
        self.start_button = ttk.Button(payment_frame, text="Start Auto Check",
                                      command=self.start_auto_check)
        self.start_button.grid(row=0, column=3, padx=(20, 5), sticky='e')

        self.stop_button = ttk.Button(payment_frame, text="Stop Auto Check",
                                     command=self.stop_auto_check, state='disabled')
        self.stop_button.grid(row=0, column=4, padx=(5, 5))

        self.manual_button = ttk.Button(payment_frame, text="Manual Check",
                                       command=self.manual_check)
        self.manual_button.grid(row=0, column=5, padx=(5, 0))

    def create_expiration_controls(self, parent):
        """Create expiration control section"""
        exp_frame = ttk.LabelFrame(parent, text="Expiration Management", padding="10")
        exp_frame.grid(row=2, column=0, sticky='ew', pady=(0, 5))
        exp_frame.columnconfigure(3, weight=1)

        # Interval
        ttk.Label(exp_frame, text="Interval:").grid(row=0, column=0, sticky='w')
        exp_combo = ttk.Combobox(exp_frame, textvariable=self.expiration_interval,
                                values=[300, 600, 1800, 3600, 7200], state="readonly", width=8)
        exp_combo.grid(row=0, column=1, padx=(5, 5))

        # Interval label
        def update_label(*args):
            val = self.expiration_interval.get()
            if val >= 3600:
                text = f"{val//3600}h"
            elif val >= 60:
                text = f"{val//60}m"
            else:
                text = f"{val}s"
            exp_label.config(text=text)

        exp_label = ttk.Label(exp_frame, text="1h")
        exp_label.grid(row=0, column=2, sticky='w')
        self.expiration_interval.trace('w', update_label)

        # Buttons
        self.expiration_start_button = ttk.Button(exp_frame, text="Start Expiration",
                                                 command=self.start_expiration_check)
        self.expiration_start_button.grid(row=0, column=3, padx=(20, 5), sticky='e')

        self.expiration_stop_button = ttk.Button(exp_frame, text="Stop Expiration",
                                                command=self.stop_expiration_check, state='disabled')
        self.expiration_stop_button.grid(row=0, column=4, padx=(5, 5))

        self.expiration_manual_button = ttk.Button(exp_frame, text="Manual Check",
                                                  command=self.manual_expiration_check)
        self.expiration_manual_button.grid(row=0, column=5, padx=(5, 0))

    def create_simple_log_area(self, parent):
        """Create simple log area"""
        log_frame = ttk.LabelFrame(parent, text="Activity Log", padding="10")
        log_frame.grid(row=3, column=0, sticky='nsew', pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # Log text with scrollbar
        self.log_text = tk.Text(log_frame, height=20, wrap=tk.WORD,
                               font=('Consolas', 9), bg='white', fg='black')
        scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky='nsew')
        scrollbar.grid(row=0, column=1, sticky='ns')

        # Configure text tags for colors
        self.log_text.tag_configure('SUCCESS', foreground='green')
        self.log_text.tag_configure('ERROR', foreground='red')
        self.log_text.tag_configure('WARNING', foreground='orange')
        self.log_text.tag_configure('INFO', foreground='black')

        # Log controls
        control_frame = ttk.Frame(log_frame)
        control_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=(5, 0))

        ttk.Button(control_frame, text="Clear Log", command=self.clear_logs).pack(side='left')
        ttk.Button(control_frame, text="Test APIs", command=self.test_apis).pack(side='left', padx=(5, 0))
        ttk.Button(control_frame, text="Start Cloudflare", command=self.start_cloudflare_tunnel).pack(side='right')




    def clear_logs(self):
        """Clear the log display"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("Log cleared", "INFO")


        # Initialize check types for compatibility
        self.check_paynoi = tk.BooleanVar(value=True)
        self.check_manual = tk.BooleanVar(value=True)

    def manual_check(self):
        """Run manual payment check"""
        self.run_manual_check()

    def open_log_folder(self):
        """Open log folder in file explorer"""
        try:
            import subprocess
            log_folder = os.path.join(self.web_root, "logs")
            if os.path.exists(log_folder):
                subprocess.Popen(f'explorer "{log_folder}"')
                self.log_message("📁 Opened log folder", "INFO")
            else:
                self.log_message("❌ Log folder not found", "ERROR")
        except Exception as e:
            self.log_message(f"❌ Failed to open log folder: {str(e)}", "ERROR")

        
    def log_message(self, message, level="INFO"):
        """Add message to log with simple styling"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Insert with color tag
        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)

        # Keep only last 500 lines for performance
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 500:
            self.log_text.delete("1.0", f"{len(lines)-500}.0")
    
    def clear_log(self):
        """Clear the log text"""
        self.log_text.delete("1.0", tk.END)
        self.log_message("Log cleared", "INFO")
    
    def update_status(self):
        """Update status display"""
        if self.is_running:
            status_text = f"Payment: Running ({self.interval.get()}s)"
            if self.expiration_running:
                status_text += f" | Expiration: Running ({self.expiration_interval.get()//60}m)"
            else:
                status_text += " | Expiration: Stopped"
        else:
            status_text = "Payment: Stopped"
            if self.expiration_running:
                status_text += f" | Expiration: Running ({self.expiration_interval.get()//60}m)"
            else:
                status_text += " | Expiration: Stopped"

        self.status_label.config(text=status_text)

        # Update button states
        self.start_button.config(state="disabled" if self.is_running else "normal")
        self.stop_button.config(state="normal" if self.is_running else "disabled")
    
    def update_statistics(self, paynoi_result=None, manual_result=None):
        """Update statistics display"""
        self.stats_text.config(state="normal")
        self.stats_text.delete("1.0", tk.END)
        
        stats_info = []
        if paynoi_result:
            stats_info.append(f"PayNoi API: {paynoi_result}")
        if manual_result:
            stats_info.append(f"Manual Check: {manual_result}")
            
        if stats_info:
            self.stats_text.insert("1.0", " | ".join(stats_info))
        else:
            self.stats_text.insert("1.0", "No recent check results")
            
        self.stats_text.config(state="disabled")
    
    def check_files_exist(self):
        """Check if required files exist"""
        if not os.path.exists(self.php_path):
            messagebox.showerror("Error", f"PHP not found at:\n{self.php_path}")
            return False

        if not os.path.exists(self.paynoi_script):
            messagebox.showerror("Error", f"PayNoi script not found at:\n{self.paynoi_script}")
            return False

        if not os.path.exists(self.manual_script):
            messagebox.showerror("Error", f"Manual script not found at:\n{self.manual_script}")
            return False

        return True

    def start_cloudflare_tunnel(self):
        """Start Cloudflare tunnel (only once)"""
        if self.is_production:
            self.log_message("🌐 Cloudflare tunnel not needed in production mode", "INFO")
            self.cloudflare_started = True
            return True

        if self.cloudflare_started:
            self.log_message("🌐 Cloudflare tunnel already started", "INFO")
            return True

        if not hasattr(self, 'cloudflare_script') or not os.path.exists(self.cloudflare_script):
            self.log_message(f"⚠️ Cloudflare script not found", "WARNING")
            return False

        try:
            self.log_message("🌐 Starting Cloudflare tunnel via batch script...", "INFO")

            # Run the batch file in background (completely hidden)
            startupinfo = None
            creation_flags = 0

            if sys.platform == "win32":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS

            # Start the cloudflare tunnel via batch script in background
            subprocess.Popen([self.cloudflare_script],
                           cwd=self.web_root,
                           startupinfo=startupinfo,
                           creationflags=creation_flags,
                           stdout=subprocess.DEVNULL,
                           stderr=subprocess.DEVNULL,
                           stdin=subprocess.DEVNULL)

            self.cloudflare_started = True
            self.log_message("✅ Cloudflare tunnel started successfully (hidden)", "SUCCESS")
            return True

        except Exception as e:
            self.log_message(f"❌ Failed to start Cloudflare tunnel: {str(e)}", "ERROR")
            return False

    def check_cloudflare_status(self):
        """Check if Cloudflare tunnel is running"""
        try:
            # Hide console window
            startupinfo = None
            creation_flags = 0

            if sys.platform == "win32":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq cloudflared.exe'],
                                      capture_output=True, text=True, timeout=10,
                                      startupinfo=startupinfo,
                                      creationflags=creation_flags)

                if 'cloudflared.exe' in result.stdout:
                    self.log_message("✅ Cloudflare tunnel is running", "SUCCESS")
                    return True
                else:
                    self.log_message("⚠️ Cloudflare tunnel is not running", "WARNING")
                    return False
            else:
                # For non-Windows systems
                result = subprocess.run(['pgrep', 'cloudflared'],
                                      capture_output=True, text=True, timeout=10)
                return result.returncode == 0

        except Exception as e:
            self.log_message(f"❌ Failed to check Cloudflare status: {str(e)}", "ERROR")
            return False

    def stop_cloudflare_tunnel(self):
        """Stop Cloudflare tunnel"""
        try:
            self.log_message("🛑 Stopping Cloudflare tunnel...", "INFO")

            # Hide console window
            startupinfo = None
            creation_flags = 0

            if sys.platform == "win32":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NO_WINDOW

                # Kill cloudflared process on Windows
                result = subprocess.run(['taskkill', '/F', '/IM', 'cloudflared.exe'],
                                      capture_output=True, text=True, timeout=10,
                                      startupinfo=startupinfo,
                                      creationflags=creation_flags)

                if result.returncode == 0:
                    self.cloudflare_started = False
                    self.log_message("✅ Cloudflare tunnel stopped successfully", "SUCCESS")
                    return True
                else:
                    self.log_message("⚠️ No Cloudflare tunnel process found to stop", "WARNING")
                    self.cloudflare_started = False
                    return True
            else:
                # For non-Windows systems
                result = subprocess.run(['pkill', 'cloudflared'],
                                      capture_output=True, text=True, timeout=10)
                self.cloudflare_started = False
                self.log_message("✅ Cloudflare tunnel stop command sent", "SUCCESS")
                return True

        except Exception as e:
            self.log_message(f"❌ Failed to stop Cloudflare tunnel: {str(e)}", "ERROR")
            return False

    def run_expiration_check(self):
        """Run the expiration check script"""
        try:
            self.log_message("🕒 Running expiration check...", "INFO")

            if self.is_production:
                # Production mode - use API call
                return self.call_production_api("expiration_check")
            else:
                # Local mode - use PHP script
                # Check if expiration script exists
                if not os.path.exists(self.expiration_script):
                    self.log_message(f"❌ Expiration script not found: {self.expiration_script}", "ERROR")
                    return False

                # Check if PHP executable exists
                if not os.path.exists(self.php_path):
                    self.log_message(f"❌ PHP executable not found: {self.php_path}", "ERROR")
                    return False

                # Log the command being executed
                self.log_message(f"🔧 Executing: {self.php_path} {self.expiration_script}", "INFO")
                self.log_message(f"🔧 Working directory: {self.web_root}", "INFO")

                # Hide console window
                startupinfo = None
                creation_flags = 0

                if sys.platform == "win32":
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    creation_flags = subprocess.CREATE_NO_WINDOW

                # Run PHP script
                result = subprocess.run([
                self.php_path,
                self.expiration_script
            ],
            cwd=self.web_root,
            capture_output=True,
            text=True,
            timeout=300,
            startupinfo=startupinfo,
            creationflags=creation_flags
            )

            # Log return code
            self.log_message(f"🔧 Return code: {result.returncode}", "INFO")

            if result.returncode == 0:
                # Parse output for important information
                if result.stdout:
                    output_lines = result.stdout.strip().split('\n')
                    for line in output_lines:
                        if line and line.strip():
                            # Extract just the message part (remove timestamp)
                            if '] ' in line:
                                message = line.split('] ', 1)[1]
                                if '✅' in message:
                                    self.log_message(message, "SUCCESS")
                                elif '❌' in message:
                                    self.log_message(message, "ERROR")
                                elif '⚠️' in message:
                                    self.log_message(message, "WARNING")
                                else:
                                    self.log_message(message, "INFO")
                            else:
                                self.log_message(line, "INFO")
                else:
                    self.log_message("✅ Expiration check completed (no output)", "SUCCESS")

                self.log_message("✅ Expiration check completed successfully", "SUCCESS")
                return True
            else:
                error_msg = result.stderr if result.stderr else "Unknown error"
                self.log_message(f"❌ Expiration check failed: {error_msg}", "ERROR")
                return False

        except subprocess.TimeoutExpired:
            self.log_message("❌ Expiration check timed out", "ERROR")
            return False
        except Exception as e:
            self.log_message(f"❌ Error running expiration check: {str(e)}", "ERROR")
            return False

    def expiration_check_loop(self):
        """Main expiration check loop"""
        while self.expiration_running:
            try:
                self.run_expiration_check()

                # Update last check time
                self.last_expiration_check = datetime.now().strftime("%H:%M:%S")

                # Wait for the specified interval
                for i in range(self.expiration_interval.get()):
                    if not self.expiration_running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ Expiration auto check error: {str(e)}", "ERROR")
                time.sleep(5)  # Wait 5 seconds before retrying

    def start_expiration_check(self):
        """Start the expiration check process"""
        if not os.path.exists(self.expiration_script):
            self.log_message(f"❌ Expiration script not found: {self.expiration_script}", "ERROR")
            return

        if self.expiration_running:
            return

        self.expiration_running = True
        self.update_expiration_status()

        # Start the check thread
        self.expiration_thread = threading.Thread(target=self.expiration_check_loop, daemon=True)
        self.expiration_thread.start()

        interval_minutes = self.expiration_interval.get() // 60
        self.log_message(f"🚀 Auto expiration check started (interval: {interval_minutes} minutes)", "SUCCESS")

    def stop_expiration_check(self):
        """Stop the expiration check process"""
        if not self.expiration_running:
            return

        self.expiration_running = False
        self.update_expiration_status()

        self.log_message("🛑 Auto expiration check stopped", "INFO")

    def manual_expiration_check(self):
        """Run a manual expiration check"""
        if not os.path.exists(self.expiration_script):
            self.log_message(f"❌ Expiration script not found: {self.expiration_script}", "ERROR")
            return

        # Run in separate thread to avoid blocking UI
        threading.Thread(target=self.run_expiration_check, daemon=True).start()

    def update_expiration_status(self):
        """Update expiration status display"""
        if self.expiration_running:
            self.expiration_start_button.config(state='disabled')
            self.expiration_stop_button.config(state='normal')
        else:
            self.expiration_start_button.config(state='normal')
            self.expiration_stop_button.config(state='disabled')

        # Update main status display
        self.update_status()
    
    def run_php_script(self, script_path, script_name):
        """Run a PHP script and return results"""
        try:
            # Hide console window
            startupinfo = None
            creation_flags = 0
            
            if sys.platform == "win32":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            result = subprocess.run([self.php_path, script_path],
                                  capture_output=True, text=True, timeout=30,
                                  cwd=self.web_root,
                                  startupinfo=startupinfo,
                                  creationflags=creation_flags)
            
            if result.returncode == 0:
                return self.parse_script_output(result.stdout, script_name)
            else:
                # Get detailed error information with null checks
                error_msg = "Unknown error"

                if result.stderr and result.stderr.strip():
                    error_msg = result.stderr.strip()
                elif result.stdout and result.stdout.strip():
                    # Sometimes errors are in stdout
                    stdout_lines = result.stdout.strip().split('\n')
                    for line in stdout_lines:
                        if line and any(keyword in line.lower() for keyword in ['error', 'fatal', 'warning', 'exception']):
                            error_msg = line.strip()
                            break
                    else:
                        # Show first few lines of stdout if no specific error found
                        valid_lines = [line for line in stdout_lines if line.strip()]
                        error_msg = ' | '.join(valid_lines[:3]) if valid_lines else "No output"

                # Clean up HTML tags and formatting
                if error_msg:
                    error_msg = re.sub(r'<[^>]+>', '', error_msg)
                    error_msg = error_msg.replace('&nbsp;', ' ').strip()

                self.log_message(f"✗ {script_name} failed (exit code: {result.returncode})", "ERROR")
                self.log_message(f"Error details: {error_msg}", "ERROR")
                return f"Failed: {error_msg[:100]}"
                
        except subprocess.TimeoutExpired:
            self.log_message(f"✗ {script_name} timed out (30s)", "ERROR")
            return "Timeout"
        except FileNotFoundError:
            self.log_message(f"✗ File not found: {script_path}", "ERROR")
            return "File not found"
        except Exception as e:
            self.log_message(f"✗ {script_name} error: {str(e)}", "ERROR")
            return f"Error: {str(e)}"
    
    def parse_script_output(self, output, script_name):
        """Parse PHP script output and extract meaningful information"""
        if not output or not output.strip():
            return "No output"

        # Log the raw output for debugging
        self.log_message(f"🔧 {script_name} raw output: {output[:200]}...", "INFO")

        # Clean HTML tags for better parsing
        clean_output = re.sub(r'<[^>]+>', '', output)

        # Look for pending payments patterns
        if "Found 0 pending payments" in clean_output or "Found 0 pending" in clean_output:
            self.log_message(f"📋 {script_name}: No pending payments found", "INFO")
            return "No pending payments"
        elif "Found" in clean_output and "pending" in clean_output:
            # Try multiple patterns
            patterns = [
                r'Found (\d+) pending payments',
                r'Found (\d+) pending',
                r'Total pending payments checked: (\d+)'
            ]
            for pattern in patterns:
                match = re.search(pattern, clean_output)
                if match:
                    count = match.group(1)
                    self.log_message(f"📋 {script_name}: Found {count} pending payments", "INFO")
                    return f"Found {count} pending"

        # Check for verification results
        if "verified successfully" in clean_output or "Payments verified:" in clean_output:
            # Look for verification count
            verified_match = re.search(r'Payments verified: (\d+)', clean_output)
            if verified_match:
                count = verified_match.group(1)
                self.log_message(f"✅ {script_name}: {count} payments verified!", "SUCCESS")
                return f"{count} payments verified"
            else:
                self.log_message(f"✅ {script_name}: Payment verification completed", "SUCCESS")
                return "Verification completed"

        # Check for Jellyfin user enabling
        if "Users enabled in Jellyfin:" in clean_output:
            enabled_match = re.search(r'Users enabled in Jellyfin: (\d+)', clean_output)
            if enabled_match:
                count = enabled_match.group(1)
                self.log_message(f"✅ {script_name}: {count} users enabled in Jellyfin", "SUCCESS")
                return f"{count} users enabled"

        # Check for summary information
        if "Summary" in clean_output:
            summary_lines = []
            lines = clean_output.split('\n')
            in_summary = False
            for line in lines:
                if "Summary" in line:
                    in_summary = True
                    continue
                if in_summary and line.strip():
                    if line.strip().startswith('Total') or line.strip().startswith('Payments') or line.strip().startswith('Users'):
                        summary_lines.append(line.strip())
                    if len(summary_lines) >= 3:  # Get first 3 summary items
                        break

            if summary_lines:
                summary = " | ".join(summary_lines)
                self.log_message(f"📊 {script_name}: {summary}", "INFO")
                return f"Summary: {summary[:100]}"

        # Check for errors
        if "error" in clean_output.lower() or "failed" in clean_output.lower():
            error_lines = [line.strip() for line in clean_output.split('\n')
                          if "error" in line.lower() or "failed" in line.lower()]
            if error_lines:
                clean_error = error_lines[0][:100]
                self.log_message(f"⚠️ {script_name}: {clean_error}", "WARNING")
                return f"Warning: {clean_error}"

        # If we get here, return a generic completion message
        self.log_message(f"✅ {script_name}: Execution completed", "SUCCESS")
        return "Completed successfully"
    
    def run_payment_checks(self):
        """Run both PayNoi and Manual payment checks"""
        paynoi_result = None
        manual_result = None

        if self.is_production:
            # Production mode - use API calls
            self.log_message("🔄 Running PayNoi API check (Production)...", "INFO")
            paynoi_result = self.call_production_api("paynoi_check")

            self.log_message("🔄 Running Manual slip check (Production)...", "INFO")
            manual_result = self.call_production_api("manual_check")
        else:
            # Local mode - use PHP scripts
            self.log_message("🔄 Running PayNoi API check (Local)...", "INFO")
            paynoi_result = self.run_php_script(self.paynoi_script, "PayNoi API")

            self.log_message("🔄 Running Manual slip check (Local)...", "INFO")
            manual_result = self.run_php_script(self.manual_script, "Manual Check")

        # Log results
        if paynoi_result:
            self.log_message(f"PayNoi result: {paynoi_result}", "INFO")
        if manual_result:
            self.log_message(f"Manual result: {manual_result}", "INFO")

        return paynoi_result, manual_result

    def call_production_api(self, endpoint_name):
        """Call production API endpoint"""
        try:
            if endpoint_name not in self.api_endpoints:
                self.log_message(f"❌ Unknown endpoint: {endpoint_name}", "ERROR")
                return None

            url = f"{self.base_url}{self.api_endpoints[endpoint_name]}"

            # Make API call with timeout
            response = requests.post(url, timeout=30, verify=False)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        self.log_message(f"✅ {endpoint_name}: {result.get('message', 'Success')}", "SUCCESS")
                        return result.get('message', 'Success')
                    else:
                        self.log_message(f"⚠️ {endpoint_name}: {result.get('message', 'Unknown error')}", "WARNING")
                        return result.get('message', 'Unknown error')
                except json.JSONDecodeError:
                    self.log_message(f"✅ {endpoint_name}: {response.text[:100]}", "SUCCESS")
                    return response.text[:100]
            else:
                self.log_message(f"❌ {endpoint_name}: HTTP {response.status_code}", "ERROR")
                return f"HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            self.log_message(f"⏰ {endpoint_name}: Request timeout", "ERROR")
            return "Request timeout"
        except requests.exceptions.ConnectionError:
            self.log_message(f"🔌 {endpoint_name}: Connection error", "ERROR")
            return "Connection error"
        except Exception as e:
            self.log_message(f"❌ {endpoint_name}: {str(e)}", "ERROR")
            return str(e)
    
    def auto_check_loop(self):
        """Main auto check loop"""
        while self.is_running:
            try:
                self.check_count += 1
                self.last_check_time = datetime.now().strftime("%H:%M:%S")
                
                self.log_message(f"🔍 Auto check #{self.check_count} started", "INFO")
                
                # Run payment checks
                paynoi_result, manual_result = self.run_payment_checks()
                
                # Update UI
                self.root.after(0, self.update_status)
                
                self.log_message(f"✅ Auto check #{self.check_count} completed", "SUCCESS")
                
                # Wait for the specified interval
                for i in range(self.interval.get()):
                    if not self.is_running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.log_message(f"✗ Auto check error: {str(e)}", "ERROR")
                time.sleep(5)  # Wait 5 seconds before retrying
    
    def check_files_exist(self):
        """Check if required files exist"""
        if not os.path.exists(self.php_path):
            self.log_message(f"❌ PHP executable not found: {self.php_path}", "ERROR")
            return False

        if not os.path.exists(self.paynoi_script):
            self.log_message(f"❌ PayNoi script not found: {self.paynoi_script}", "ERROR")
            return False

        return True

    def start_auto_check(self):
        """Start the auto check process"""
        if not self.check_files_exist():
            return

        if self.is_running:
            return

        # Always enable both check types for simplicity
        # if not self.check_paynoi.get() and not self.check_manual.get():
        #     messagebox.showwarning("Warning", "Please select at least one check type!")
        #     return

        # Start Cloudflare tunnel first (only once)
        self.start_cloudflare_tunnel()

        self.is_running = True
        self.check_count = 0
        self.update_status()

        # Start the check thread
        self.check_thread = threading.Thread(target=self.auto_check_loop, daemon=True)
        self.check_thread.start()

        # Always use both check types
        check_types = ["PayNoi API", "Manual Slip"]
        self.log_message(f"🚀 Auto check started - Types: {', '.join(check_types)} (interval: {self.interval.get()}s)", "SUCCESS")
    
    def stop_auto_check(self):
        """Stop the auto check process"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.update_status()
        
        self.log_message("🛑 Auto check stopped", "WARNING")
    
    def run_manual_check(self):
        """Run a single manual check"""
        if not self.check_files_exist():
            return
        
        self.log_message("🔍 Running manual payment check...", "INFO")
        
        # Run in a separate thread to avoid blocking UI
        def manual_check_thread():
            try:
                paynoi_result, manual_result = self.run_payment_checks()
                self.log_message("✅ Manual check completed", "SUCCESS")
            except Exception as e:
                self.log_message(f"✗ Manual check error: {str(e)}", "ERROR")
        
        threading.Thread(target=manual_check_thread, daemon=True).start()

    def test_apis(self):
        """Test PayNoi API and system connectivity"""
        mode_text = "Production" if self.is_production else "Local"
        self.log_message(f"🧪 Testing APIs and system connectivity ({mode_text} mode)...", "INFO")

        def test_thread():
            try:
                if self.is_production:
                    self.test_production_apis()
                else:
                    self.test_local_apis()

            except Exception as e:
                self.log_message(f"❌ Test error: {str(e)}", "ERROR")

        threading.Thread(target=test_thread, daemon=True).start()

    def test_production_apis(self):
        """Test production API endpoints"""
        self.log_message("🌐 Testing production API endpoints...", "INFO")

        # Test health endpoint
        self.log_message("1️⃣ Testing health endpoint...", "INFO")
        health_result = self.call_production_api("health")

        # Test PayNoi API endpoint
        self.log_message("2️⃣ Testing PayNoi check endpoint...", "INFO")
        paynoi_result = self.call_production_api("paynoi_check")

        # Test manual check endpoint
        self.log_message("3️⃣ Testing manual check endpoint...", "INFO")
        manual_result = self.call_production_api("manual_check")

        # Test expiration check endpoint
        self.log_message("4️⃣ Testing expiration check endpoint...", "INFO")
        expiration_result = self.call_production_api("expiration_check")

        # Test direct PayNoi API
        self.log_message("5️⃣ Testing PayNoi API directly...", "INFO")
        try:
            api_url = f"{self.paynoi_api_url}?api_key={self.paynoi_api_key}&record_key={self.paynoi_record_key}"
            response = requests.get(api_url, timeout=10)

            if response.status_code == 200:
                self.log_message("✅ PayNoi API connection successful", "SUCCESS")
                try:
                    data = response.json()
                    if isinstance(data, list):
                        self.log_message(f"✅ PayNoi API returned {len(data)} transactions", "SUCCESS")
                    else:
                        self.log_message("✅ PayNoi API returned valid response", "SUCCESS")
                except:
                    self.log_message("✅ PayNoi API returned response (non-JSON)", "SUCCESS")
            else:
                self.log_message(f"❌ PayNoi API returned status: {response.status_code}", "ERROR")
        except Exception as e:
            self.log_message(f"❌ PayNoi API error: {str(e)}", "ERROR")

        self.log_message("🧪 Production API testing completed", "INFO")

    def test_local_apis(self):
        """Test local environment"""
        # Test 0: Check Cloudflare tunnel status
        self.log_message("0️⃣ Checking Cloudflare tunnel status...", "INFO")

        # Check if cloudflare batch script exists
        if hasattr(self, 'cloudflare_script') and os.path.exists(self.cloudflare_script):
            self.log_message(f"✅ Cloudflare script found: {os.path.basename(self.cloudflare_script)}", "SUCCESS")
        else:
            self.log_message(f"❌ Cloudflare script not found", "ERROR")

        # Check if cloudflare executable exists
        cloudflare_path = r"C:\Users\<USER>\.cloudflared\cloudflared.exe"
        if os.path.exists(cloudflare_path):
            self.log_message(f"✅ Cloudflare executable found: {cloudflare_path}", "SUCCESS")
        else:
            self.log_message(f"❌ Cloudflare executable not found: {cloudflare_path}", "ERROR")

        # Check if tunnel is actually running
        tunnel_running = self.check_cloudflare_status()
        if tunnel_running:
            self.log_message("✅ Cloudflare tunnel: Currently running", "SUCCESS")
        else:
            self.log_message("⚠️ Cloudflare tunnel: Not running", "WARNING")

        # Check internal status
        if self.cloudflare_started:
            self.log_message("✅ Internal status: Marked as started", "INFO")
        else:
            self.log_message("⚠️ Internal status: Not started", "INFO")

        # Test 1: Check PHP executable
        self.log_message("1️⃣ Testing PHP executable...", "INFO")
        if hasattr(self, 'php_path') and os.path.exists(self.php_path):
            self.log_message(f"✅ PHP found: {self.php_path}", "SUCCESS")
        else:
            self.log_message(f"❌ PHP not found or not in local mode", "ERROR")
            return

        # Test 2: Check script files
        self.log_message("2️⃣ Testing script files...", "INFO")
        if hasattr(self, 'paynoi_script') and os.path.exists(self.paynoi_script):
            self.log_message(f"✅ PayNoi script found: {os.path.basename(self.paynoi_script)}", "SUCCESS")
        else:
            self.log_message(f"❌ PayNoi script not found", "ERROR")

        if hasattr(self, 'manual_script') and os.path.exists(self.manual_script):
            self.log_message(f"✅ Manual script found: {os.path.basename(self.manual_script)}", "SUCCESS")
        else:
            self.log_message(f"❌ Manual script not found", "ERROR")

        # Test 3: Test PayNoi API directly
        self.log_message("3️⃣ Testing PayNoi API directly...", "INFO")
        try:
            api_url = f"{self.paynoi_api_url}?api_key={self.paynoi_api_key}&record_key={self.paynoi_record_key}"
            response = requests.get(api_url, timeout=10)

            if response.status_code == 200:
                self.log_message("✅ PayNoi API connection successful", "SUCCESS")

                try:
                    data = response.json()
                    if isinstance(data, list):
                        self.log_message(f"✅ PayNoi API returned {len(data)} transactions", "SUCCESS")
                    else:
                        self.log_message(f"✅ PayNoi API response: {str(data)[:100]}", "SUCCESS")
                except json.JSONDecodeError:
                    self.log_message("⚠️ PayNoi API returned non-JSON data", "WARNING")
                    self.log_message(f"Response: {response.text[:200]}", "INFO")
            else:
                self.log_message(f"❌ PayNoi API failed: HTTP {response.status_code}", "ERROR")
                self.log_message(f"Response: {response.text[:200]}", "ERROR")

        except requests.exceptions.Timeout:
            self.log_message("❌ PayNoi API timeout (10s)", "ERROR")
        except requests.exceptions.ConnectionError:
            self.log_message("❌ PayNoi API connection error", "ERROR")
        except Exception as e:
            self.log_message(f"❌ PayNoi API error: {str(e)}", "ERROR")

        # Test 4: Test PHP script execution
        self.log_message("4️⃣ Testing PHP script execution...", "INFO")
        try:
            # Hide console window
            startupinfo = None
            creation_flags = 0

            if sys.platform == "win32":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NO_WINDOW

            # Test with a simple PHP command
            result = subprocess.run([self.php_path, "-v"],
                                  capture_output=True, text=True, timeout=10,
                                  startupinfo=startupinfo,
                                  creationflags=creation_flags)
            if result.returncode == 0:
                php_version = result.stdout.split('\n')[0]
                self.log_message(f"✅ PHP execution successful: {php_version}", "SUCCESS")
            else:
                self.log_message(f"❌ PHP execution failed: {result.stderr}", "ERROR")
        except Exception as e:
            self.log_message(f"❌ PHP execution error: {str(e)}", "ERROR")

        # Test 5: Check expiration script
        self.log_message("5️⃣ Checking expiration script...", "INFO")
        if hasattr(self, 'expiration_script') and os.path.exists(self.expiration_script):
            self.log_message(f"✅ Expiration script found: {os.path.basename(self.expiration_script)}", "SUCCESS")
        else:
            self.log_message(f"❌ Expiration script not found", "ERROR")

        # Test 6: Test database connectivity (through PHP)
        self.log_message("6️⃣ Testing database connectivity...", "INFO")
        test_php_code = '''<?php
        require_once "includes/config.php";
        try {
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            echo "Database connection successful";
        } catch (Exception $e) {
            echo "Database error: " . $e->getMessage();
        }
        ?>'''

        try:
            # Write temporary PHP file
            temp_file = os.path.join(self.web_root, "temp_db_test.php")
            with open(temp_file, 'w') as f:
                f.write(test_php_code)

            # Hide console window
            startupinfo = None
            creation_flags = 0

            if sys.platform == "win32":
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NO_WINDOW

            # Execute the test
            result = subprocess.run([self.php_path, temp_file],
                                  capture_output=True, text=True, timeout=10,
                                  cwd=self.web_root,
                                  startupinfo=startupinfo,
                                  creationflags=creation_flags)

            # Clean up
            if os.path.exists(temp_file):
            os.remove(temp_file)

            if "Database connection successful" in result.stdout:
                self.log_message("✅ Database connection successful", "SUCCESS")
            else:
                self.log_message(f"❌ Database connection failed: {result.stdout}", "ERROR")

        except Exception as e:
            self.log_message(f"❌ Database test error: {str(e)}", "ERROR")

        self.log_message("🧪 Local API testing completed", "INFO")

    def run_php_test(self):
        """Run PHP test script"""
        self.log_message("🔬 Running PHP PayNoi test script...", "INFO")

        def php_test_thread():
            try:
                test_script = os.path.join(self.web_root, "test_paynoi_simple.php")

                if not os.path.exists(test_script):
                    self.log_message(f"❌ Test script not found: {test_script}", "ERROR")
                    return

                # Run the PHP test script
                startupinfo = None
                creation_flags = 0

                if sys.platform == "win32":
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    creation_flags = subprocess.CREATE_NO_WINDOW

                result = subprocess.run([self.php_path, test_script],
                                      capture_output=True, text=True, timeout=60,
                                      cwd=self.web_root,
                                      startupinfo=startupinfo,
                                      creationflags=creation_flags)

                if result.returncode == 0:
                    self.log_message("✅ PHP test completed successfully", "SUCCESS")

                    # Parse and display important results
                    output_lines = result.stdout.split('\n')
                    for line in output_lines:
                        line = line.strip()
                        if line and (line.startswith('✓') or line.startswith('✗') or
                                   'Found' in line or 'Error' in line or 'Success' in line):
                            self.log_message(f"Test: {line}", "INFO")
                else:
                    self.log_message(f"❌ PHP test failed (exit code: {result.returncode})", "ERROR")
                    if result.stderr:
                        self.log_message(f"Error: {result.stderr.strip()}", "ERROR")

            except subprocess.TimeoutExpired:
                self.log_message("❌ PHP test timed out (60s)", "ERROR")
            except Exception as e:
                self.log_message(f"❌ PHP test error: {str(e)}", "ERROR")

        threading.Thread(target=php_test_thread, daemon=True).start()

    def open_log_folder(self):
        """Open log folder in file explorer"""
        try:
            log_folder = os.path.join(self.web_root, "logs")

            if not os.path.exists(log_folder):
                os.makedirs(log_folder, exist_ok=True)
                self.log_message(f"📁 Created log folder: {log_folder}", "INFO")

            if sys.platform == "win32":
                os.startfile(log_folder)
            elif sys.platform == "darwin":  # macOS
                subprocess.run(["open", log_folder],
                             stdout=subprocess.DEVNULL,
                             stderr=subprocess.DEVNULL)
            else:  # Linux
                subprocess.run(["xdg-open", log_folder],
                             stdout=subprocess.DEVNULL,
                             stderr=subprocess.DEVNULL)

            self.log_message(f"📁 Opened log folder: {log_folder}", "INFO")

        except Exception as e:
            self.log_message(f"❌ Failed to open log folder: {str(e)}", "ERROR")

def main():
    # Create the main window
    root = tk.Tk()
    
    # Set icon (if available)
    try:
        root.iconbitmap("payment.ico")
    except:
        pass
    
    # Create the application
    app = AutoPaymentChecker(root)
    
    # Handle window closing
    def on_closing():
        active_processes = []

        if app.is_running:
            active_processes.append("Payment Auto Check")
        if app.expiration_running:
            active_processes.append("Expiration Auto Check")

        if active_processes:
            processes_text = " and ".join(active_processes)
            if messagebox.askokcancel("Quit", f"{processes_text} running. Stop and quit?"):
                if app.is_running:
                    app.stop_auto_check()
                if app.expiration_running:
                    app.stop_expiration_check()
                time.sleep(1)  # Give time to stop
                root.destroy()
        else:
            # Ask if user wants to stop Cloudflare tunnel when closing
            if app.cloudflare_started:
                if messagebox.askyesno("Cloudflare Tunnel", "Stop Cloudflare tunnel before closing?"):
                    app.stop_cloudflare_tunnel()
                    time.sleep(1)  # Give time to stop
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
