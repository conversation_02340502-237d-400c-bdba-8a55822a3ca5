<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']);
$isAdmin = $isLoggedIn && isset($_SESSION['is_admin']) && $_SESSION['is_admin'];
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jellyfin by <PERSON></title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
        <style>
        .referral-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
        }
        .referral-code {
            background: linear-gradient(135deg, #ffc107, #ff8f00);
            color: #000;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 1rem 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffc107;
        }
        .stat-label {
            color: #ccc;
            margin-top: 0.5rem;
        }
        .transactions-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 2rem 0;
        }
        .transactions-table th,
        .transactions-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .transactions-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .copy-button {
            background: #28a745;
            color: #fff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 1rem;
        }
        .copy-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
        <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <?php if ($isLoggedIn): ?>
                    <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                    <a href="/packages" class="nav-link">แพ็คเกจ</a>
                    <a href="/guide" class="nav-link">คู่มือ</a>
                    <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                    <?php if ($isAdmin): ?>
                        <div class="admin-dropdown" id="adminDropdown">
                            <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                            <div class="dropdown-menu">
                                <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                                <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                                <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                                <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                                <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                                <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                                <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="profile-dropdown" id="profileDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                        <div class="dropdown-menu">
                            <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                            <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                            <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="/login" class="nav-link">เข้าสู่ระบบ</a>
                    <a href="/register" class="nav-link">สมัครสมาชิก</a>
                    <a href="/guide" class="nav-link">คู่มือ</a>
                    <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php endif; ?>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="hero-section">
            <div class="container">
                <h2 class="hero-title fade-in-up">เติมเต็มทุกช่วงเวลา ด้วยคลังภาพยนตร์มากมาย</h2>
                <p class="hero-description fade-in-up delay-1">ตอบโจทย์ทุกความต้องการ สตรีมภาพยนตร์และรายการโปรดของคุณด้วยแพลตฟอร์ม Jellyfin</p>

                <?php if (!$isLoggedIn): ?>
                    <div class="hero-actions fade-in-up delay-2">
                        <a href="/register" class="btn btn-primary noir-button">เริ่มต้นใช้งาน</a>
                        <a href="/login" class="btn btn-secondary noir-button">เข้าสู่ระบบ</a>
                    </div>
                <?php else: ?>
                    <div class="hero-actions fade-in-up delay-2">
                        <a href="/dashboard" class="btn btn-primary noir-button">ไปที่แดชบอร์ด</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <section class="features-section">
            <div class="container">
                <div class="features-grid">
                    <div class="feature-card fade-in-scroll">
                        <h3>🎬 สตรีมทุกที่</h3>
                        <p>เข้าถึงไลบรารีสื่อของคุณจากอุปกรณ์ใดก็ได้ ทุกที่ในโลก</p>
                    </div>
                    <div class="feature-card fade-in-scroll">
                        <h3>📱 คุณภาพสูง</h3>
                        <p>เพลิดเพลินกับเนื้อหาในคุณภาพสูงสุดด้วยการสตรีมแบบปรับตัว</p>
                    </div>
                    <div class="feature-card fade-in-scroll">
                        <h3>🔒 ปลอดภัยและเป็นส่วนตัว</h3>
                        <p>ข้อมูลของคุณยังคงเป็นส่วนตัวด้วยโซลูชันที่ปลอดภัยและโฮสต์เอง</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>
    </div>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
</body>
</html>
