<?php
/**
 * API Endpoint for PayNoi Payment Check
 * สำหรับ AutoPaymentChecker Production Mode
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Include required files - use production config
    if (file_exists('../config/database_production.php')) {
        require_once '../config/database_production.php';
    } else {
        require_once '../includes/config.php';
    }
    require_once '../includes/PayNoiAPIClass.php';
    
    // Initialize PayNoi API
    $paynoiAPI = new PayNoiAPIClass();
    
    // Log the check attempt
    error_log("[" . date('Y-m-d H:i:s') . "] PayNoi API check started via API endpoint");
    
    // Get pending payments
    $stmt = $pdo->prepare("
        SELECT id, user_id, amount, transaction_id, created_at 
        FROM payment_transaction 
        WHERE status = 'pending' 
        AND payment_method = 'paynoi'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $pending_payments = $stmt->fetchAll();
    
    $verified_count = 0;
    $failed_count = 0;
    $processed_payments = [];
    
    if (empty($pending_payments)) {
        echo json_encode([
            'success' => true,
            'message' => 'No pending PayNoi payments found',
            'verified_count' => 0,
            'failed_count' => 0,
            'processed_payments' => []
        ]);
        exit;
    }
    
    // Get PayNoi transactions
    $transactions = $paynoiAPI->getTransactions();
    
    if (!$transactions || !isset($transactions['data'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to fetch PayNoi transactions',
            'verified_count' => 0,
            'failed_count' => count($pending_payments)
        ]);
        exit;
    }
    
    // Process each pending payment
    foreach ($pending_payments as $payment) {
        $payment_verified = false;
        
        // Check against PayNoi transactions
        foreach ($transactions['data'] as $transaction) {
            if ($paynoiAPI->verifyTransaction($transaction, $payment)) {
                // Payment verified - update status
                $stmt = $pdo->prepare("
                    UPDATE payment_transaction 
                    SET status = 'verified', 
                        verified_at = NOW(),
                        paynoi_transaction_id = ?
                    WHERE id = ?
                ");
                $stmt->execute([$transaction['id'], $payment['id']]);
                
                // Add days to user account
                $days_to_add = floor($payment['amount'] / PRICE_PER_DAY);
                
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET expiry_date = CASE 
                        WHEN expiry_date > NOW() THEN DATE_ADD(expiry_date, INTERVAL ? DAY)
                        ELSE DATE_ADD(NOW(), INTERVAL ? DAY)
                    END
                    WHERE id = ?
                ");
                $stmt->execute([$days_to_add, $days_to_add, $payment['user_id']]);
                
                // Enable user in Jellyfin if needed
                require_once '../includes/jellyfin_api.php';
                $jellyfin = new JellyfinAPI();
                $jellyfin->enableUser($payment['user_id']);
                
                $verified_count++;
                $payment_verified = true;
                
                $processed_payments[] = [
                    'payment_id' => $payment['id'],
                    'amount' => $payment['amount'],
                    'days_added' => $days_to_add,
                    'status' => 'verified'
                ];
                
                error_log("[" . date('Y-m-d H:i:s') . "] PayNoi payment verified: ID {$payment['id']}, Amount {$payment['amount']}, Days added: {$days_to_add}");
                break;
            }
        }
        
        if (!$payment_verified) {
            $failed_count++;
            $processed_payments[] = [
                'payment_id' => $payment['id'],
                'amount' => $payment['amount'],
                'status' => 'still_pending'
            ];
        }
    }
    
    $message = "PayNoi check completed: {$verified_count} verified, {$failed_count} still pending";
    error_log("[" . date('Y-m-d H:i:s') . "] " . $message);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'verified_count' => $verified_count,
        'failed_count' => $failed_count,
        'processed_payments' => $processed_payments,
        'total_pending' => count($pending_payments)
    ]);
    
} catch (Exception $e) {
    error_log("[" . date('Y-m-d H:i:s') . "] PayNoi API check error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'PayNoi check failed: ' . $e->getMessage(),
        'verified_count' => 0,
        'failed_count' => 0
    ]);
}
?>
