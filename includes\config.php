<?php
/**
 * Configuration file for Jellyfin TopUp System
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'jellyfin_registration');
define('DB_USER', 'root');
define('DB_PASS', 'Wxmujwsofu@1234');

// PayNoi API configuration
define('PAYNOI_API_KEY', '6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446');
define('PAYNOI_RECORD_KEY', '100568');
define('PAYNOI_API_URL', 'https://paynoi.com/api_line');

// Jellyfin configuration
define('JELLYFIN_URL', 'https://jellyfin.embyjames.xyz');
define('JELLYFIN_SERVER_URL', 'https://jellyfin.embyjames.xyz');
define('JELLYFIN_API_KEY', '1ffb3f05e955493fb2e8edfe42bfd52a');

// System configuration
define('SITE_URL', 'https://embyjames.xyz');
define('SITE_NAME', 'Jellyfin TopUp');

// Pricing configuration (THB)
define('PRICE_PER_DAY', 3.33); // 100 THB = 30 days
define('MIN_TOPUP_AMOUNT', 50);
define('MAX_TOPUP_AMOUNT', 5000);

// Affiliate configuration
define('AFFILIATE_COMMISSION_RATE', 0.05); // 5%
define('MIN_AFFILIATE_PAYOUT', 100); // Minimum 100 THB to payout

// Email configuration (if needed)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', 'noreply@localhost');
define('FROM_NAME', 'Jellyfin TopUp');

// Security
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Logging
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', 'logs/system.log');

// PayNoi sync settings
define('PAYNOI_SYNC_INTERVAL', 5); // seconds
define('PAYMENT_VERIFICATION_TIMEOUT', 7200); // 2 hours
define('AMOUNT_TOLERANCE', 1.00); // 1 baht tolerance for random decimal (0.01-0.99)

// Timezone
date_default_timezone_set('Asia/Bangkok');

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Create logs directory if it doesn't exist
$logsDir = __DIR__ . '/../logs';
if (!is_dir($logsDir)) {
    mkdir($logsDir, 0755, true);
}

// Database connection
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please check your configuration.");
}
?>
