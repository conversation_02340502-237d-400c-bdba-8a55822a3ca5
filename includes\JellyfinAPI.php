<?php
/**
 * Jellyfin API Integration Class
 * Handles communication with Jellyfin server for user management and server operations
 */

class JellyfinAPI {
    private $serverUrl;
    private $apiKey;
    private $accessToken;
    private $userId;
    
    public function __construct($serverUrl = null, $apiKey = null) {
        $this->serverUrl = $serverUrl ?: (defined('JELLYFIN_SERVER_URL') ? JELLYFIN_SERVER_URL : 'https://jellyfin.embyjames.xyz');
        $this->apiKey = $apiKey ?: (defined('JELLYFIN_API_KEY') ? JELLYFIN_API_KEY : '1ffb3f05e955493fb2e8edfe42bfd52a');

        // Remove trailing slash
        $this->serverUrl = rtrim($this->serverUrl, '/');
    }
    
    /**
     * Authenticate with Jellyfin server using username and password
     */
    public function authenticate($username, $password) {
        $url = $this->serverUrl . '/Users/<USER>';
        
        $data = [
            'Username' => $username,
            'Pw' => $password
        ];
        
        $headers = [
            'Content-Type: application/json',
            'X-Emby-Authorization: MediaBrowser Client="Jellyfin by James Registration System", Device="Web", DeviceId="' . $this->generateDeviceId() . '", Version="1.0.0"'
        ];
        
        $response = $this->makeRequest('POST', $url, $data, $headers);
        
        if ($response && isset($response['AccessToken'])) {
            $this->accessToken = $response['AccessToken'];
            $this->userId = $response['User']['Id'];
            return $response;
        }
        
        return false;
    }
    
    /**
     * Create a new user in Jellyfin
     */
    public function createUser($username, $password = null, $disabled = true) {
        if (!$this->accessToken && !$this->apiKey) {
            throw new Exception('Authentication required to create users');
        }

        $url = $this->serverUrl . '/Users/<USER>';

        $data = [
            'Name' => $username,
            'Password' => $password ?: $this->generateRandomPassword()
        ];

        $headers = $this->getAuthHeaders();
        $headers[] = 'Content-Type: application/json';

        $response = $this->makeRequest('POST', $url, $data, $headers);

        if ($response && isset($response['Id'])) {
            // Set user policy for new user
            $this->setUserPolicy($response['Id'], null, $disabled);
            return $response;
        }

        return false;
    }
    
    /**
     * Delete a user from Jellyfin
     */
    public function deleteUser($userId) {
        if (!$this->accessToken && !$this->apiKey) {
            throw new Exception('Authentication required to delete users');
        }
        
        $url = $this->serverUrl . '/Users/' . $userId;
        $headers = $this->getAuthHeaders();
        
        $response = $this->makeRequest('DELETE', $url, null, $headers);
        return $response !== false;
    }
    
    /**
     * Get user information
     */
    public function getUser($userId) {
        $url = $this->serverUrl . '/Users/' . $userId;
        $headers = $this->getAuthHeaders();
        
        return $this->makeRequest('GET', $url, null, $headers);
    }
    
    /**
     * Get all users
     */
    public function getUsers() {
        $url = $this->serverUrl . '/Users';
        $headers = $this->getAuthHeaders();
        
        return $this->makeRequest('GET', $url, null, $headers);
    }
    
    /**
     * Update user password
     */
    public function updateUserPassword($userId, $newPassword, $currentPassword = null) {
        $url = $this->serverUrl . '/Users/' . $userId . '/Password';
        
        $data = [
            'NewPw' => $newPassword
        ];
        
        if ($currentPassword) {
            $data['CurrentPw'] = $currentPassword;
        }
        
        $headers = $this->getAuthHeaders();
        $headers[] = 'Content-Type: application/json';
        
        $response = $this->makeRequest('POST', $url, $data, $headers);
        return $response !== false;
    }

    /**
     * Reset user password in Jellyfin (admin only)
     */
    public function resetUserPassword($userId, $newPassword) {
        $url = $this->serverUrl . '/Users/' . $userId . '/Password';

        $data = [
            'NewPw' => $newPassword,
            'ResetPassword' => true
        ];

        $headers = $this->getAuthHeaders();
        $headers[] = 'Content-Type: application/json';

        $response = $this->makeRequest('POST', $url, $data, $headers);
        return $response !== false;
    }

    /**
     * Set user policy (permissions and restrictions)
     */
    public function setUserPolicy($userId, $policy = null, $disabled = false) {
        global $pdo;

        if (!$policy) {
            // Get default policy from database
            $stmt = $pdo->prepare("SELECT config_value FROM server_config WHERE config_key = 'default_user_policy'");
            $stmt->execute();
            $result = $stmt->fetch();
            $policy = $result ? json_decode($result['config_value'], true) : $this->getDefaultUserPolicy();
        }

        // Set disabled status
        $policy['IsDisabled'] = $disabled;

        $url = $this->serverUrl . '/Users/' . $userId . '/Policy';
        $headers = $this->getAuthHeaders();
        $headers[] = 'Content-Type: application/json';

        $response = $this->makeRequest('POST', $url, $policy, $headers);
        return $response !== false;
    }

    /**
     * Enable or disable a user
     */
    public function setUserEnabled($userId, $enabled = true) {
        return $this->setUserPolicy($userId, null, !$enabled);
    }

    /**
     * Get server URL
     */
    public function getServerUrl() {
        return $this->serverUrl;
    }

    /**
     * Get API key (masked)
     */
    public function getApiKey() {
        return $this->apiKey;
    }

    /**
     * Enable a user (simple version)
     */
    public function enableUser($userId) {
        return $this->enableAllLibraries($userId, 1);
    }

    /**
     * Enable access to all libraries for a user with specific max sessions
     */
    public function enableAllLibraries($userId, $maxSessions = 1) {
        global $pdo;

        // Get default policy with all libraries enabled
        $stmt = $pdo->prepare("SELECT config_value FROM server_config WHERE config_key = 'default_user_policy'");
        $stmt->execute();
        $result = $stmt->fetch();
        $policy = $result ? json_decode($result['config_value'], true) : $this->getDefaultUserPolicy();

        // Enable all libraries and folders
        $policy['IsDisabled'] = false;
        $policy['EnableAllFolders'] = true;
        $policy['EnabledFolders'] = [];
        $policy['BlockedMediaFolders'] = [];
        $policy['EnableAllChannels'] = true;
        $policy['EnabledChannels'] = [];
        $policy['BlockedChannels'] = [];

        // Set max simultaneous sessions
        $policy['MaxActiveSessions'] = (int)$maxSessions;

        $url = $this->serverUrl . '/Users/' . $userId . '/Policy';
        $headers = $this->getAuthHeaders();
        $headers[] = 'Content-Type: application/json';

        $response = $this->makeRequest('POST', $url, $policy, $headers);
        return $response !== false;
    }

    /**
     * Set max simultaneous sessions for a user
     */
    public function setMaxSessions($userId, $maxSessions = 1) {
        global $pdo;

        // Get current user policy
        $url = $this->serverUrl . '/Users/' . $userId;
        $headers = $this->getAuthHeaders();
        $userResponse = $this->makeRequest('GET', $url, null, $headers);

        if (!$userResponse || !isset($userResponse['Policy'])) {
            return false;
        }

        $policy = $userResponse['Policy'];
        $policy['MaxActiveSessions'] = (int)$maxSessions;

        $url = $this->serverUrl . '/Users/' . $userId . '/Policy';
        $headers = $this->getAuthHeaders();
        $headers[] = 'Content-Type: application/json';

        $response = $this->makeRequest('POST', $url, $policy, $headers);

        if ($response !== false) {
            // Update database record
            $stmt = $pdo->prepare("UPDATE jellyfin_users SET max_simultaneous_sessions = ? WHERE jellyfin_user_id = ?");
            $stmt->execute([$maxSessions, $userId]);
        }

        return $response !== false;
    }
    
    /**
     * Get server information
     */
    public function getServerInfo() {
        $url = $this->serverUrl . '/System/Info';
        $headers = $this->getAuthHeaders();
        
        return $this->makeRequest('GET', $url, null, $headers);
    }
    
    /**
     * Check if server is running
     */
    public function isServerRunning() {
        try {
            $response = $this->getServerInfo();
            return $response !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get server status
     */
    public function getServerStatus() {
        try {
            $info = $this->getServerInfo();
            if ($info) {
                return [
                    'status' => 'running',
                    'version' => $info['Version'] ?? 'Unknown',
                    'name' => $info['ServerName'] ?? 'Jellyfin Server'
                ];
            }
        } catch (Exception $e) {
            // Server not responding
        }
        
        return [
            'status' => 'stopped',
            'version' => null,
            'name' => 'Jellyfin Server'
        ];
    }
    
    /**
     * Get authentication headers
     */
    private function getAuthHeaders() {
        $headers = [];
        
        if ($this->accessToken) {
            $headers[] = 'X-Emby-Authorization: MediaBrowser Client="Jellyfin by James Registration System", Device="Web", DeviceId="' . $this->generateDeviceId() . '", Version="1.0.0", Token="' . $this->accessToken . '"';
        } elseif ($this->apiKey) {
            $headers[] = 'X-Emby-Authorization: MediaBrowser Client="Jellyfin by James Registration System", Device="Web", DeviceId="' . $this->generateDeviceId() . '", Version="1.0.0", Token="' . $this->apiKey . '"';
        }
        
        return $headers;
    }
    
    /**
     * Make HTTP request to Jellyfin API
     */
    private function makeRequest($method, $url, $data = null, $headers = []) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        if ($httpCode >= 400) {
            $errorData = json_decode($response, true);
            $errorMessage = isset($errorData['Message']) ? $errorData['Message'] : 'HTTP Error ' . $httpCode;
            throw new Exception($errorMessage);
        }
        
        return $response ? json_decode($response, true) : true;
    }
    
    /**
     * Generate device ID for API authentication
     */
    private function generateDeviceId() {
        return 'jellyfin-registration-' . md5($_SERVER['HTTP_HOST'] ?? 'localhost');
    }
    
    /**
     * Generate random password
     */
    private function generateRandomPassword($length = 12) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        return substr(str_shuffle($chars), 0, $length);
    }
    


    /**
     * Get default user policy
     */
    private function getDefaultUserPolicy() {
        return [
            'IsAdministrator' => false,
            'IsHidden' => false,
            'IsDisabled' => false,
            'MaxParentalRating' => null,
            'BlockedTags' => [],
            'EnableUserPreferenceAccess' => true,
            'AccessSchedules' => [],
            'BlockUnratedItems' => [],
            'EnableRemoteControlOfOtherUsers' => false,
            'EnableSharedDeviceControl' => false,
            'EnableRemoteAccess' => true,
            'EnableLiveTvManagement' => false,
            'EnableLiveTvAccess' => false,
            'EnableMediaPlayback' => true,
            'EnableAudioPlaybackTranscoding' => true,
            'EnableVideoPlaybackTranscoding' => true,
            'EnablePlaybackRemuxing' => true,
            'ForceRemoteSourceTranscoding' => false,
            'EnableContentDeletion' => false,
            'EnableContentDownloading' => false,
            'EnableSyncTranscoding' => false,
            'EnableMediaConversion' => false,
            'EnabledDevices' => [],
            'EnableAllDevices' => true,
            'EnabledChannels' => [],
            'EnableAllChannels' => true,
            'EnabledFolders' => [],
            'EnableAllFolders' => true,
            'InvalidLoginAttemptCount' => 3,
            'LoginAttemptsBeforeLockout' => 3,
            'MaxActiveSessions' => 1,
            'EnablePublicSharing' => false,
            'BlockedMediaFolders' => [],
            'BlockedChannels' => [],
            'RemoteClientBitrateLimit' => 0,
            'AuthenticationProviderId' => 'Jellyfin.Server.Implementations.Users.DefaultAuthenticationProvider',
            'PasswordResetProviderId' => 'Jellyfin.Server.Implementations.Users.DefaultPasswordResetProvider'
        ];
    }
}
?>
