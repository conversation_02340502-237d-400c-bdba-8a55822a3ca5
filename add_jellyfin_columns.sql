-- Add Jellyfin-related columns to users table
-- Run this SQL to add missing columns

USE jellyfin_topup;

-- Add jellyfin_user_id column if it doesn't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS jellyfin_user_id VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS jellyfin_username VARCHAR(100) NULL,
ADD COLUMN IF NOT EXISTS jellyfin_password VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS max_simultaneous_sessions INT DEFAULT 1,
ADD COLUMN IF NOT EXISTS jellyfin_last_activity TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS jellyfin_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS affiliate_points INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS referred_by INT NULL;

-- Add indexes for better performance
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_jellyfin_user_id (jellyfin_user_id),
ADD INDEX IF NOT EXISTS idx_jellyfin_username (jellyfin_username),
ADD INDEX IF NOT EXISTS idx_referred_by (referred_by);

-- Add foreign key constraint for referred_by
ALTER TABLE users 
ADD CONSTRAINT fk_users_referred_by 
FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add package_id column to payments table if it doesn't exist
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS package_id INT NULL,
ADD INDEX IF NOT EXISTS idx_package_id (package_id);

-- Show updated table structure
DESCRIBE users;
DESCRIBE payments;
