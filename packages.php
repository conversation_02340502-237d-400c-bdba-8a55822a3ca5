<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

// Check if user already has an active subscription
$stmt = $pdo->prepare("
    SELECT us.*, p.name as package_name, p.duration_days, p.price, pt.slip_image
    FROM user_subscriptions us
    JOIN packages p ON us.package_id = p.id
    LEFT JOIN payment_transactions pt ON us.id = pt.subscription_id
    WHERE us.user_id = ? AND us.status IN ('active', 'pending')
    ORDER BY us.created_at DESC
    LIMIT 1
");
$stmt->execute([$_SESSION['user_id']]);
$current_subscription = $stmt->fetch();

// Get available packages
$stmt = $pdo->prepare("SELECT * FROM packages WHERE is_active = 1 ORDER BY duration_days ASC");
$stmt->execute();
$packages = $stmt->fetchAll();

$message = '';
$message_type = 'info';

// Handle subscription cancellation
if ($_POST && isset($_POST['cancel_subscription'])) {
    $subscription_id = (int)$_POST['cancel_subscription'];

    try {
        $pdo->beginTransaction();

        // Verify subscription belongs to user and is pending
        $stmt = $pdo->prepare("
            SELECT id FROM user_subscriptions
            WHERE id = ? AND user_id = ? AND status = 'pending'
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id']]);

        if ($stmt->fetch()) {
            // Update subscription status to cancelled
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions
                SET status = 'cancelled', updated_at = NOW()
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$subscription_id, $_SESSION['user_id']]);

            // Update payment transaction status
            $stmt = $pdo->prepare("
                UPDATE payment_transactions
                SET status = 'cancelled', updated_at = NOW()
                WHERE subscription_id = ? AND user_id = ?
            ");
            $stmt->execute([$subscription_id, $_SESSION['user_id']]);

            $pdo->commit();

            log_activity($_SESSION['user_id'], 'subscription_cancelled', "Cancelled subscription ID: {$subscription_id}");

            $message = 'ยกเลิกการสมัครเรียบร้อยแล้ว กรุณาเลือกแพ็คเกจใหม่';
            $message_type = 'success';

            // Refresh current subscription data
            $current_subscription = null;
        } else {
            $message = 'ไม่สามารถยกเลิกการสมัครได้';
            $message_type = 'error';
        }

    } catch (Exception $e) {
        $pdo->rollBack();
        $message = 'เกิดข้อผิดพลาดในการยกเลิกการสมัคร';
        $message_type = 'error';
        error_log('Subscription cancellation error: ' . $e->getMessage());
    }
}

// Handle package selection
if ($_POST && isset($_POST['package_id'])) {
    $package_id = (int)$_POST['package_id'];
    
    // Get package details
    $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ? AND is_active = 1");
    $stmt->execute([$package_id]);
    $package = $stmt->fetch();
    
    if ($package) {
        try {
            $pdo->beginTransaction();
            
            // Generate random decimal amount (not more than 0.10 baht difference)
            $random_decimal = mt_rand(1, 10) / 100; // 0.01 to 0.10
            $payment_amount = $package['price'] + $random_decimal;
            
            // Create subscription record
            $stmt = $pdo->prepare("
                INSERT INTO user_subscriptions (user_id, package_id, status, payment_amount, start_date, created_at)
                VALUES (?, ?, 'pending', ?, NOW(), NOW())
            ");
            $stmt->execute([$_SESSION['user_id'], $package_id, $payment_amount]);
            $subscription_id = $pdo->lastInsertId();
            
            // Generate QR Code URL
            $qr_url = "https://promptpay.io/0820722972/" . number_format($payment_amount, 2, '.', '') . ".png";
            
            // Create payment transaction
            $transaction_ref = 'TXN' . date('YmdHis') . str_pad($subscription_id, 4, '0', STR_PAD_LEFT);
            $stmt = $pdo->prepare("
                INSERT INTO payment_transactions (subscription_id, user_id, amount, transaction_ref, qr_code_url, status, created_at) 
                VALUES (?, ?, ?, ?, ?, 'pending', NOW())
            ");
            $stmt->execute([$subscription_id, $_SESSION['user_id'], $payment_amount, $transaction_ref, $qr_url]);
            
            // Update subscription with QR code URL
            $stmt = $pdo->prepare("UPDATE user_subscriptions SET qr_code_url = ?, payment_reference = ? WHERE id = ?");
            $stmt->execute([$qr_url, $transaction_ref, $subscription_id]);
            
            $pdo->commit();
            
            // Log activity
            log_activity($_SESSION['user_id'], 'package_selected', "Selected package: {$package['name']} - Amount: {$payment_amount} THB");
            
            // Redirect to payment page
            header("Location: /payment?subscription_id={$subscription_id}");
            exit();
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $message = 'เกิดข้อผิดพลาดในการสร้างคำสั่งซื้อ กรุณาลองใหม่อีกครั้ง';
            $message_type = 'error';
            error_log('Package selection error: ' . $e->getMessage());
        }
    } else {
        $message = 'ไม่พบแพ็คเกจที่เลือก';
        $message_type = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เลือกแพ็คเกจ - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .packages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .package-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 8px;
            border: 2px solid #333;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .package-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border-color: #666;
        }
        .package-card.popular {
            border-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }
        .package-card.popular::before {
            content: 'แนะนำ';
            position: absolute;
            top: 15px;
            right: -30px;
            background: #ffc107;
            color: #000;
            padding: 5px 40px;
            font-size: 0.8rem;
            font-weight: bold;
            transform: rotate(45deg);
        }
        .package-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #fff;
        }
        .package-price {
            font-size: 3rem;
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 0.5rem;
        }
        .package-currency {
            font-size: 1.2rem;
            color: #ccc;
        }
        .package-duration {
            font-size: 1.1rem;
            color: #ccc;
            margin-bottom: 2rem;
        }
        .package-features {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
        }
        .package-features li {
            padding: 0.5rem 0;
            color: #ccc;
            border-bottom: 1px solid #333;
        }
        .package-features li:last-child {
            border-bottom: none;
        }
        .package-features li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .select-package-btn {
            width: 100%;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .current-subscription {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            padding: 1rem;
            border-radius: 4px;
            margin: 2rem 0;
            text-align: center;
        }
        .pending-payment {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            padding: 1rem;
            border-radius: 4px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                <a href="/packages" class="nav-link active">แพ็คเกจ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php if (is_admin()): ?>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                            <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                            <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                            <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">เลือกแพ็คเกจของคุณ</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">เลือกแพ็คเกจที่เหมาะสมกับการใช้งานของคุณ</p>
            </div>

            <?php if ($message): ?>
                <div class="flash-message flash-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($current_subscription): ?>
                <?php if ($current_subscription['status'] === 'active'): ?>
                    <div class="current-subscription fade-in-scroll">
                        <h3>แพ็คเกจปัจจุบันของคุณ</h3>
                        <p><strong><?php echo htmlspecialchars($current_subscription['package_name']); ?></strong></p>
                        <p>หมดอายุ: <?php echo $current_subscription['end_date'] ? date('d/m/Y H:i', strtotime($current_subscription['end_date'])) : 'ไม่ระบุ'; ?></p>
                    </div>
                <?php elseif ($current_subscription['status'] === 'pending'): ?>
                    <div class="pending-payment fade-in-scroll">
                        <h3>รอการชำระเงิน</h3>
                        <p><strong><?php echo htmlspecialchars($current_subscription['package_name']); ?></strong></p>
                        <p>จำนวนเงิน: <?php echo number_format($current_subscription['payment_amount'] ?? 0, 2); ?> บาท</p>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap; justify-content: center;">
                            <a href="/payment?subscription_id=<?php echo $current_subscription['id']; ?>" class="btn btn-primary noir-button">
                                <?php echo !empty($current_subscription['slip_image']) ? 'ตรวจสอบสถานะ' : 'ดำเนินการชำระเงิน'; ?>
                            </a>
                            <?php if (empty($current_subscription['slip_image'])): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="cancel_subscription" value="<?php echo $current_subscription['id']; ?>">
                                    <button type="submit" class="btn btn-secondary noir-button" style="background: #dc3545;"
                                            onclick="return confirm('คุณต้องการยกเลิกการสมัครนี้และเลือกแพ็คเกจใหม่ใช่หรือไม่?')">
                                        ยกเลิกและเลือกใหม่
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="packages-grid">
                <?php foreach ($packages as $index => $package): ?>
                    <div class="package-card fade-in-scroll <?php echo $index === 1 ? 'popular' : ''; ?>">
                        <div class="package-name"><?php echo htmlspecialchars($package['name']); ?></div>
                        <div class="package-price">
                            <?php echo number_format($package['price'], 0); ?>
                            <span class="package-currency">บาท</span>
                        </div>
                        <div class="package-duration"><?php echo $package['duration_days']; ?> วัน</div>
                        
                        <ul class="package-features">
                            <li>เข้าถึงคอนเทนต์ทั้งหมด</li>
                            <li>คุณภาพ HD/4K</li>
                            <li>ดูได้ <?php echo $package['max_sessions'] ?? 1; ?> อุปกรณ์พร้อมกัน</li>
                            <li>รองรับทุกแพลตฟอร์ม</li>
                            <li>ไม่มีโฆษณา</li>
                        </ul>
                        
                        <?php if (!$current_subscription || $current_subscription['status'] === 'expired'): ?>
                            <form method="POST" style="margin-top: 2rem;">
                                <input type="hidden" name="package_id" value="<?php echo $package['id']; ?>">
                                <button type="submit" class="btn btn-primary noir-button select-package-btn"
                                        onclick="return confirm('คุณต้องการเลือกแพ็คเกจ <?php echo htmlspecialchars($package['name']); ?> ราคา <?php echo number_format($package['price'], 0); ?> บาท ใช่หรือไม่?')">
                                    เลือกแพ็คเกจนี้
                                </button>
                            </form>
                        <?php else: ?>
                            <button class="btn btn-secondary noir-button select-package-btn" disabled>
                                <?php echo $current_subscription['status'] === 'active' ? 'กำลังใช้งาน' : 'รอการชำระเงิน'; ?>
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="hero-section" style="padding: 2rem 0;">
                <h3 class="fade-in-scroll">วิธีการชำระเงิน</h3>
                <div class="fade-in-scroll" style="text-align: center;">
                    <p style="color: #ccc; margin-bottom: 1rem;">ชำระเงินผ่าน PromptPay QR Code</p>
                    <p style="color: #ffc107; font-size: 0.9rem;">
                        หลังจากเลือกแพ็คเกจ ระบบจะสร้าง QR Code สำหรับการชำระเงิน<br>
                        เมื่อชำระเงินเรียบร้อยแล้ว กรุณาแจ้งการชำระเงินเพื่อเปิดใช้งานบัญชี
                    </p>
                </div>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
</body>
</html>
