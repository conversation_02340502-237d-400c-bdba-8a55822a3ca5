-- Add max_simultaneous_sessions column to packages table
-- This fixes the PayNoi transactions sync error

USE jellyfin_registration;

-- Add max_simultaneous_sessions column to packages table if it doesn't exist
ALTER TABLE packages 
ADD COLUMN IF NOT EXISTS max_simultaneous_sessions INT DEFAULT 1 AFTER price;

-- Update existing packages with default values
UPDATE packages SET max_simultaneous_sessions = 1 WHERE max_simultaneous_sessions IS NULL;

-- Update specific packages based on price (as mentioned in memories)
-- Package 150 บาท should allow 2 simultaneous devices
UPDATE packages SET max_simultaneous_sessions = 2 WHERE price = 150.00;

-- Show updated table structure
DESCRIBE packages;

-- Show current packages
SELECT id, name, duration_days, price, max_simultaneous_sessions, is_active FROM packages;
