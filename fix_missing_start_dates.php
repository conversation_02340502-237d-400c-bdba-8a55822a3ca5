<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require admin login
require_login();
if (!$_SESSION['is_admin']) {
    header('Location: /dashboard');
    exit();
}

echo "<h2>🔧 Fix Missing Start Dates</h2>";

$message = '';
$message_type = 'info';

// Handle form submission
if ($_POST && isset($_POST['fix_all_start_dates'])) {
    try {
        $pdo->beginTransaction();
        
        // Fix all subscriptions with missing start_date
        $stmt = $pdo->prepare("
            UPDATE user_subscriptions 
            SET start_date = created_at 
            WHERE start_date IS NULL
        ");
        $result = $stmt->execute();
        $affected_rows = $stmt->rowCount();
        
        $pdo->commit();
        
        if ($affected_rows > 0) {
            $message = "✅ แก้ไข start_date สำเร็จ! อัปเดต {$affected_rows} รายการ";
            $message_type = 'success';
        } else {
            $message = "ℹ️ ไม่มี start_date ที่ต้องแก้ไข ทุกรายการมี start_date แล้ว";
            $message_type = 'info';
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $message = "❌ เกิดข้อผิดพลาด: " . $e->getMessage();
        $message_type = 'error';
    }
}

// Get subscriptions with missing start_date
try {
    $stmt = $pdo->prepare("
        SELECT us.*, u.username, p.name as package_name
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN packages p ON us.package_id = p.id
        WHERE us.start_date IS NULL
        ORDER BY us.created_at DESC
    ");
    $stmt->execute();
    $missing_start_dates = $stmt->fetchAll();
    
    // Get all subscriptions for comparison
    $stmt = $pdo->prepare("
        SELECT us.*, u.username, p.name as package_name
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN packages p ON us.package_id = p.id
        ORDER BY us.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $all_subscriptions = $stmt->fetchAll();
    
} catch (Exception $e) {
    $message = "❌ เกิดข้อผิดพลาดในการดึงข้อมูล: " . $e->getMessage();
    $message_type = 'error';
    $missing_start_dates = [];
    $all_subscriptions = [];
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Missing Start Dates - Jellyfin by James</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: #2a2a2a;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #444;
        }
        th {
            background: #333;
            font-weight: bold;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-pending { color: #ffc107; }
        .status-active { color: #28a745; }
        .status-expired { color: #dc3545; }
        .status-cancelled { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Missing Start Dates</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div style="background: #2a2a2a; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3>📊 Summary</h3>
            <p><strong>Subscriptions ที่ขาด start_date:</strong> <?php echo count($missing_start_dates); ?> รายการ</p>
            <p><strong>Total subscriptions:</strong> <?php echo count($all_subscriptions); ?> รายการ (แสดง 20 รายการล่าสุด)</p>
            
            <?php if (count($missing_start_dates) > 0): ?>
                <form method="POST" style="margin-top: 20px;">
                    <button type="submit" name="fix_all_start_dates" class="btn btn-success" 
                            onclick="return confirm('คุณต้องการแก้ไข start_date ทั้งหมด <?php echo count($missing_start_dates); ?> รายการหรือไม่?')">
                        🔧 แก้ไข Start Date ทั้งหมด
                    </button>
                </form>
            <?php endif; ?>
        </div>
        
        <?php if (count($missing_start_dates) > 0): ?>
            <h3>❌ Subscriptions ที่ขาด Start Date</h3>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Package</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>จะแก้ไขเป็น</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($missing_start_dates as $sub): ?>
                        <tr>
                            <td><?php echo $sub['id']; ?></td>
                            <td><?php echo htmlspecialchars($sub['username']); ?></td>
                            <td><?php echo htmlspecialchars($sub['package_name']); ?></td>
                            <td class="status-<?php echo $sub['status']; ?>">
                                <?php echo $sub['status']; ?>
                            </td>
                            <td><?php echo date('d/m/Y H:i', strtotime($sub['created_at'])); ?></td>
                            <td style="color: #dc3545;">NULL</td>
                            <td>
                                <?php if ($sub['end_date']): ?>
                                    <?php echo date('d/m/Y H:i', strtotime($sub['end_date'])); ?>
                                <?php else: ?>
                                    <span style="color: #ffc107;">NULL</span>
                                <?php endif; ?>
                            </td>
                            <td style="color: #28a745;">
                                <?php echo date('d/m/Y H:i', strtotime($sub['created_at'])); ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>✅ ไม่มีปัญหา!</h3>
                <p>ทุก subscription มี start_date แล้ว ไม่ต้องแก้ไขอะไร</p>
            </div>
        <?php endif; ?>
        
        <h3>📋 All Subscriptions (20 รายการล่าสุด)</h3>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>User</th>
                    <th>Package</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Days Left</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($all_subscriptions as $sub): ?>
                    <tr>
                        <td><?php echo $sub['id']; ?></td>
                        <td><?php echo htmlspecialchars($sub['username']); ?></td>
                        <td><?php echo htmlspecialchars($sub['package_name']); ?></td>
                        <td class="status-<?php echo $sub['status']; ?>">
                            <?php echo $sub['status']; ?>
                        </td>
                        <td><?php echo date('d/m/Y H:i', strtotime($sub['created_at'])); ?></td>
                        <td>
                            <?php if ($sub['start_date']): ?>
                                <?php echo date('d/m/Y H:i', strtotime($sub['start_date'])); ?>
                            <?php else: ?>
                                <span style="color: #dc3545;">NULL</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($sub['end_date']): ?>
                                <?php echo date('d/m/Y H:i', strtotime($sub['end_date'])); ?>
                            <?php else: ?>
                                <span style="color: #ffc107;">NULL</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($sub['end_date'] && $sub['status'] === 'active'): ?>
                                <?php 
                                $days_left = ceil((strtotime($sub['end_date']) - time()) / (60 * 60 * 24));
                                if ($days_left > 0): 
                                ?>
                                    <span style="color: #28a745;"><?php echo $days_left; ?> วัน</span>
                                <?php else: ?>
                                    <span style="color: #dc3545;">หมดอายุแล้ว</span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span style="color: #6c757d;">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div style="margin: 30px 0;">
            <a href="/admin/packages" class="btn btn-primary">← กลับไปหน้า Admin Packages</a>
            <a href="/test_timezone_dates.php" class="btn btn-primary">🧪 Test Timezone & Dates</a>
            <a href="/dashboard" class="btn btn-primary">🏠 Dashboard</a>
        </div>
    </div>
</body>
</html>
