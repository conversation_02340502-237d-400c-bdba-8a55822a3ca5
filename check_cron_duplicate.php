<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🔍 Check Cron Job Duplicates</h2>";

try {
    echo "<h3>📋 Current Cron Configuration</h3>";
    
    // Read cron configuration
    $cronFiles = [
        '/etc/cron.d/jellyfin-cron',
        '/var/spool/cron/crontabs/root',
        '/etc/crontab'
    ];
    
    foreach ($cronFiles as $file) {
        echo "<h4>File: {$file}</h4>";
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if (strpos($content, 'manual_check') !== false || strpos($content, 'linux_manual_check') !== false) {
                echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
                echo htmlspecialchars($content);
                echo "</pre>";
            } else {
                echo "<p>No manual check cron jobs found in this file</p>";
            }
        } else {
            echo "<p>❌ File not found</p>";
        }
    }
    
    echo "<h3>🔍 Process Check</h3>";
    
    // Check running processes
    $processes = shell_exec('ps aux | grep manual_check | grep -v grep');
    if ($processes) {
        echo "<h4>Running manual_check processes:</h4>";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
        echo htmlspecialchars($processes);
        echo "</pre>";
    } else {
        echo "<p>No manual_check processes currently running</p>";
    }
    
    echo "<h3>📊 Log Analysis</h3>";
    
    // Analyze recent logs for duplicates
    $logFile = '/var/log/jellyfin/manual_cron.log';
    if (file_exists($logFile)) {
        $lines = file($logFile);
        $recentLines = array_slice($lines, -50); // Last 50 lines
        
        $timestamps = [];
        $duplicateCount = 0;
        
        foreach ($recentLines as $line) {
            if (preg_match('/\[([^\]]+)\]/', $line, $matches)) {
                $timestamp = $matches[1];
                if (isset($timestamps[$timestamp])) {
                    $timestamps[$timestamp]++;
                    if ($timestamps[$timestamp] > 1) {
                        $duplicateCount++;
                    }
                } else {
                    $timestamps[$timestamp] = 1;
                }
            }
        }
        
        echo "<h4>Recent Log Analysis (last 50 lines):</h4>";
        echo "<p>Total duplicate timestamp entries: <strong>{$duplicateCount}</strong></p>";
        
        if ($duplicateCount > 0) {
            echo "<h4>Duplicate Timestamps:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Timestamp</th><th>Count</th></tr>";
            
            foreach ($timestamps as $timestamp => $count) {
                if ($count > 1) {
                    echo "<tr style='background: yellow;'>";
                    echo "<td>{$timestamp}</td>";
                    echo "<td>{$count}</td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
        
        echo "<h4>Recent Log Entries:</h4>";
        echo "<textarea style='width: 100%; height: 200px; font-family: monospace;'>";
        echo htmlspecialchars(implode('', array_slice($recentLines, -20)));
        echo "</textarea>";
        
    } else {
        echo "<p>❌ Log file not found: {$logFile}</p>";
    }
    
    echo "<h3>🔧 Fix Suggestions</h3>";
    
    if ($duplicateCount > 0) {
        echo "<div style='background: #ffeeee; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<h4>⚠️ Duplicate Cron Jobs Detected!</h4>";
        echo "<p>The same cron job is running multiple times simultaneously.</p>";
        echo "<p><strong>Possible causes:</strong></p>";
        echo "<ul>";
        echo "<li>Multiple cron entries for the same job</li>";
        echo "<li>Cron job taking longer than the interval to complete</li>";
        echo "<li>Multiple cron files containing the same job</li>";
        echo "</ul>";
        
        echo "<p><strong>Recommended fixes:</strong></p>";
        echo "<ol>";
        echo "<li>Check all cron files and remove duplicates</li>";
        echo "<li>Add file locking to prevent concurrent execution</li>";
        echo "<li>Increase cron interval if jobs are taking too long</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    echo "<h3>🛠️ Quick Fixes</h3>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='kill_processes' value='1'>";
    echo "<button type='submit' style='background: red; color: white; padding: 10px; border: none; cursor: pointer;'>Kill All manual_check Processes</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin-top: 10px;'>";
    echo "<input type='hidden' name='test_single_run' value='1'>";
    echo "<button type='submit' style='background: blue; color: white; padding: 10px; border: none; cursor: pointer;'>Test Single Manual Check Run</button>";
    echo "</form>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['kill_processes'])) {
        echo "<h3>🔪 Killing Processes:</h3>";
        $result = shell_exec('pkill -f manual_check 2>&1');
        echo "<p>Result: " . ($result ? htmlspecialchars($result) : "No output") . "</p>";
        echo "<p>✅ Attempted to kill all manual_check processes</p>";
    }
    
    if (isset($_POST['test_single_run'])) {
        echo "<h3>🧪 Testing Single Run:</h3>";
        $output = shell_exec('cd /var/www/html && php cron/linux_manual_check.php 2>&1');
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
    }
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
?>
