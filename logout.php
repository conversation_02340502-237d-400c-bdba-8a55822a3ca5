<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Log activity if user is logged in
if (is_logged_in()) {
    log_activity($_SESSION['user_id'], 'user_logout', 'User logged out from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
}

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    // Remove from database
    if (is_logged_in()) {
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE session_token = ?");
        $stmt->execute([$_COOKIE['remember_token']]);
    }
    
    // Clear cookie
    setcookie('remember_token', '', time() - 3600, '/');
}

// Destroy session
session_destroy();

// Redirect to home page
header('Location: /?logged_out=1');
exit();
?>
