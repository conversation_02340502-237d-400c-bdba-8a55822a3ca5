@echo off
echo ========================================
echo  AutoPaymentChecker Compiler v2.0
echo  With Cloudflare GUI Integration
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found in PATH. Trying alternative paths...

    REM Try common Python installation paths
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" (
        set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
        echo Found Python 3.13 at: %PYTHON_PATH%
    ) else if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" (
        set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
        echo Found Python 3.12 at: %PYTHON_PATH%
    ) else if exist "C:\Python39\python.exe" (
        set PYTHON_PATH=C:\Python39\python.exe
        echo Found Python 3.9 at: %PYTHON_PATH%
    ) else if exist "C:\Python310\python.exe" (
        set PYTHON_PATH=C:\Python310\python.exe
        echo Found Python 3.10 at: %PYTHON_PATH%
    ) else if exist "C:\Python311\python.exe" (
        set PYTHON_PATH=C:\Python311\python.exe
        echo Found Python 3.11 at: %PYTHON_PATH%
    ) else if exist "C:\Python312\python.exe" (
        set PYTHON_PATH=C:\Python312\python.exe
        echo Found Python 3.12 at: %PYTHON_PATH%
    ) else if exist "C:\Python313\python.exe" (
        set PYTHON_PATH=C:\Python313\python.exe
        echo Found Python 3.13 at: %PYTHON_PATH%
    ) else (
        echo ❌ Python not found. Please install Python or update the path.
        echo Tried paths:
        echo   - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
        echo   - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
        echo   - C:\Python39\python.exe to C:\Python313\python.exe
        pause
        exit /b 1
    )
) else (
    set PYTHON_PATH=python
    echo Using Python from PATH
)

echo.
echo ========================================

REM Check if AutoPaymentChecker.py exists
if not exist "AutoPaymentChecker.py" (
    echo ❌ AutoPaymentChecker.py not found!
    echo Please make sure the file exists in the current directory.
    pause
    exit /b 1
)

echo ✅ AutoPaymentChecker.py found
echo 📊 File size:
for %%A in (AutoPaymentChecker.py) do echo    %%~zA bytes

echo.
echo 🔧 Installing/Updating PyInstaller...
%PYTHON_PATH% -m pip install pyinstaller --upgrade --quiet

echo.
echo 🚀 Compiling AutoPaymentChecker.py...
echo 📋 Features included:
echo    • GUI Payment Checker
echo    • Cloudflare Tunnel Integration
echo    • Expiration Management System (NO CMD WINDOWS)
echo    • Hidden Background Execution
echo    • Process Management
echo    • Status Monitoring
echo    • Complete CMD Window Suppression
echo.

REM Backup old executable if exists
if exist "AutoPaymentChecker.exe" (
    echo 📦 Backing up old executable...
    copy "AutoPaymentChecker.exe" "AutoPaymentChecker_backup.exe" >nul
    echo    ✅ Backup created: AutoPaymentChecker_backup.exe
)

REM Check if icon file exists
if exist "payment.ico" (
    echo 🎨 Using custom icon: payment.ico
    %PYTHON_PATH% -m PyInstaller --onefile --windowed --name="AutoPaymentChecker" --icon=payment.ico --distpath=. AutoPaymentChecker.py
) else (
    echo 🎨 No custom icon found, compiling without icon...
    %PYTHON_PATH% -m PyInstaller --onefile --windowed --name="AutoPaymentChecker" --distpath=. AutoPaymentChecker.py
)

echo.
echo ========================================

REM Check if compilation was successful
if exist "AutoPaymentChecker.exe" (
    echo ✅ Compilation successful!

    REM Get file size
    for %%A in (AutoPaymentChecker.exe) do (
        echo 📁 Executable: AutoPaymentChecker.exe
        echo 📊 File size: %%~zA bytes
    )

    echo.
    echo 🗑️ Cleaning up build files...
    rmdir /s /q build >nul 2>&1
    rmdir /s /q __pycache__ >nul 2>&1
    del AutoPaymentChecker.spec >nul 2>&1
    echo    ✅ Cleanup complete!

    echo.
    echo ========================================
    echo 🎉 AutoPaymentChecker.exe is ready!
    echo ========================================
    echo.
    echo 📋 Integrated Management System:
    echo    • 🌐 Cloudflare Tunnel Management
    echo    • 💰 Payment Transaction Checking
    echo    • 🕒 Subscription Expiration Management (FIXED - NO CMD)
    echo    • 🔒 Jellyfin Account Auto-disable
    echo    • 🚀 Auto-start capabilities
    echo    • 🔒 No CMD windows (completely hidden)
    echo    • 🧹 Clean exit with process cleanup
    echo    • ✅ All subprocess calls now hidden
    echo.
    echo 🚀 How to use:
    echo    1. Run AutoPaymentChecker.exe
    echo    2. Payment Checking: Click 'Start Auto Check'
    echo    3. Expiration Management: Click 'Start Expiration Check'
    echo    4. Cloudflare: Use individual tunnel control buttons
    echo    5. All operations run silently in background
    echo.
    echo ✅ Ready for production deployment!

) else (
    echo ❌ Compilation failed!
    echo.
    echo 🔍 Troubleshooting:
    echo    • Check if Python and PyInstaller are properly installed
    echo    • Ensure AutoPaymentChecker.py has no syntax errors
    echo    • Check the error messages above for details
    echo.
    echo 💡 Try running: python -m py_compile AutoPaymentChecker.py
    echo    to check for syntax errors first.
)

echo.
echo ========================================
pause
