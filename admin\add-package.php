<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';
$errors = [];

// Handle form submission
if ($_POST && isset($_POST['add_package'])) {
    $name = trim($_POST['name'] ?? '');
    $duration_days = (int)($_POST['duration_days'] ?? 0);
    $price = (float)($_POST['price'] ?? 0);
    $max_simultaneous_sessions = (int)($_POST['max_simultaneous_sessions'] ?? 1);
    $description = trim($_POST['description'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validation
    if (empty($name)) {
        $errors[] = 'กรุณากรอกชื่อแพ็คเกจ';
    }
    
    if ($duration_days <= 0) {
        $errors[] = 'จำนวนวันต้องมากกว่า 0';
    }
    
    if ($price <= 0) {
        $errors[] = 'ราคาต้องมากกว่า 0';
    }
    
    if ($max_simultaneous_sessions <= 0 || $max_simultaneous_sessions > 10) {
        $errors[] = 'จำนวน Max Sessions ต้องอยู่ระหว่าง 1-10';
    }

    // Check if package name already exists
    if (empty($errors)) {
        try {
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $stmt = $pdo->prepare("SELECT id FROM packages WHERE name = ?");
            $stmt->execute([$name]);
            if ($stmt->rowCount() > 0) {
                $errors[] = 'ชื่อแพ็คเกจนี้มีอยู่แล้ว';
            }
        } catch (PDOException $e) {
            $errors[] = 'เกิดข้อผิดพลาดในการตรวจสอบข้อมูล';
        }
    }

    // Insert new package
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO packages (name, duration_days, price, max_simultaneous_sessions, description, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$name, $duration_days, $price, $max_simultaneous_sessions, $description, $is_active]);
            
            $package_id = $pdo->lastInsertId();
            
            $message = "เพิ่มแพ็คเกจ '{$name}' เรียบร้อยแล้ว";
            $message_type = 'success';
            
            log_activity($_SESSION['user_id'], 'package_created', "Created package: {$name} (ID: {$package_id})");
            
            // Clear form data
            $name = $duration_days = $price = $max_simultaneous_sessions = $description = '';
            $is_active = 1;
            
        } catch (PDOException $e) {
            $errors[] = 'เกิดข้อผิดพลาดในการเพิ่มแพ็คเกจ: ' . $e->getMessage();
        }
    }
    
    if (!empty($errors)) {
        $message_type = 'error';
    }
} else {
    // Default values for new package
    $name = '';
    $duration_days = 30;
    $price = 100.00;
    $max_simultaneous_sessions = 1;
    $description = '';
    $is_active = 1;
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เพิ่มแพ็คเกจใหม่ - Jellyfin by James</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .form-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #fff;
            font-weight: bold;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #333;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #ffc107;
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }
        .package-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        .preview-title {
            color: #ffc107;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        .preview-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .preview-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <div class="header-brand">
                <h1><a href="../dashboard.php">Jellyfin by James</a></h1>
            </div>
            <nav class="main-nav">
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="users.php" class="dropdown-item">จัดการผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item">อนุมัติการชำระเงิน</a>
                        <a href="view_packages.php" class="dropdown-item">ดูแพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="add-package.php" class="dropdown-item active">เพิ่มแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="form-container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">เพิ่มแพ็คเกจใหม่</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">สร้างแพ็คเกจสำหรับผู้ใช้งาน Jellyfin</p>
            </div>

            <?php if ($message): ?>
                <div class="flash-message flash-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message ?? ''); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <div class="flash-message flash-error">
                    <ul style="margin: 0; padding-left: 1.5rem;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error ?? ''); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" class="fade-in-scroll">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">ชื่อแพ็คเกจ *</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" 
                               placeholder="เช่น 30 วัน, 60 วัน" required>
                    </div>
                    <div class="form-group">
                        <label for="duration_days">จำนวนวัน *</label>
                        <input type="number" id="duration_days" name="duration_days" value="<?php echo $duration_days ?? 30; ?>" 
                               min="1" max="365" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="price">ราคา (บาท) *</label>
                        <input type="number" id="price" name="price" value="<?php echo $price ?? 100; ?>" 
                               min="0.01" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="max_simultaneous_sessions">Max Sessions *</label>
                        <select id="max_simultaneous_sessions" name="max_simultaneous_sessions" required>
                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                <option value="<?php echo $i; ?>" <?php echo ($max_simultaneous_sessions == $i) ? 'selected' : ''; ?>>
                                    <?php echo $i; ?> อุปกรณ์
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">คำอธิบาย</label>
                    <textarea id="description" name="description" rows="3" 
                              placeholder="คำอธิบายแพ็คเกจ (ไม่บังคับ)"><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               <?php echo $is_active ? 'checked' : ''; ?>>
                        <label for="is_active">เปิดใช้งานแพ็คเกจ</label>
                    </div>
                </div>

                <div class="btn-group">
                    <a href="manage-packages.php" class="btn btn-secondary">ยกเลิก</a>
                    <button type="submit" name="add_package" class="btn btn-primary">เพิ่มแพ็คเกจ</button>
                </div>
            </form>

            <!-- Package Preview -->
            <div class="package-preview fade-in-scroll">
                <div class="preview-title">ตัวอย่างแพ็คเกจ</div>
                <div class="preview-item">
                    <span>ชื่อ:</span>
                    <span id="preview-name"><?php echo htmlspecialchars($name ?: 'ชื่อแพ็คเกจ'); ?></span>
                </div>
                <div class="preview-item">
                    <span>ระยะเวลา:</span>
                    <span id="preview-duration"><?php echo $duration_days ?? 30; ?> วัน</span>
                </div>
                <div class="preview-item">
                    <span>ราคา:</span>
                    <span id="preview-price"><?php echo number_format($price ?? 100, 2); ?> บาท</span>
                </div>
                <div class="preview-item">
                    <span>Max Sessions:</span>
                    <span id="preview-sessions"><?php echo $max_simultaneous_sessions ?? 1; ?> อุปกรณ์</span>
                </div>
                <div class="preview-item">
                    <span>สถานะ:</span>
                    <span id="preview-status"><?php echo $is_active ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?></span>
                </div>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
    <script>
        // Live preview update
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.getElementById('name');
            const durationInput = document.getElementById('duration_days');
            const priceInput = document.getElementById('price');
            const sessionsInput = document.getElementById('max_simultaneous_sessions');
            const activeInput = document.getElementById('is_active');

            function updatePreview() {
                document.getElementById('preview-name').textContent = nameInput.value || 'ชื่อแพ็คเกจ';
                document.getElementById('preview-duration').textContent = durationInput.value + ' วัน';
                document.getElementById('preview-price').textContent = parseFloat(priceInput.value || 0).toLocaleString('th-TH', {minimumFractionDigits: 2}) + ' บาท';
                document.getElementById('preview-sessions').textContent = sessionsInput.value + ' อุปกรณ์';
                document.getElementById('preview-status').textContent = activeInput.checked ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
            }

            nameInput.addEventListener('input', updatePreview);
            durationInput.addEventListener('input', updatePreview);
            priceInput.addEventListener('input', updatePreview);
            sessionsInput.addEventListener('change', updatePreview);
            activeInput.addEventListener('change', updatePreview);
        });
    </script>
</body>
</html>
