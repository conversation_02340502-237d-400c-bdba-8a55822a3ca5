<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cinema Logo - Jellyfin by <PERSON></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            background: #0a0a0a;
            padding: 20px;
        }
        .test-section {
            margin: 40px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid #333;
        }
        .test-title {
            color: #FFD700;
            margin-bottom: 20px;
            text-align: center;
        }
        .logo-showcase {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.9));
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <div class="container">
        <h1 style="text-align: center; color: #FFD700; margin-bottom: 40px;">🎬 Cinema Logo Test Page</h1>
        
        <div class="test-section">
            <h2 class="test-title">1. Header Logo (Full Size)</h2>
            <div class="logo-showcase">
                <header class="main-header" style="background: transparent; border: none; position: static;">
                    <div class="container">
                        <h1 class="logo fade-in-up">
                            <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo">
                            <span class="logo-text">
                                Jellyfin by James
                                <small class="cinema-subtitle">Cinema</small>
                            </span>
                        </h1>
                    </div>
                </header>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">2. Logo SVG Only</h2>
            <div class="logo-showcase">
                <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 60px; width: auto;">
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">3. Logo with Different Sizes</h2>
            <div style="display: flex; gap: 20px; justify-content: center; align-items: center; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <p style="color: #ccc; margin-bottom: 10px;">Small (30px)</p>
                    <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 30px; width: auto;">
                </div>
                <div style="text-align: center;">
                    <p style="color: #ccc; margin-bottom: 10px;">Medium (50px)</p>
                    <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 50px; width: auto;">
                </div>
                <div style="text-align: center;">
                    <p style="color: #ccc; margin-bottom: 10px;">Large (80px)</p>
                    <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 80px; width: auto;">
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">4. Logo on Different Backgrounds</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="background: #000; padding: 20px; text-align: center; border-radius: 5px;">
                    <p style="color: #ccc; margin-bottom: 10px;">Black Background</p>
                    <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 40px; width: auto;">
                </div>
                <div style="background: #333; padding: 20px; text-align: center; border-radius: 5px;">
                    <p style="color: #ccc; margin-bottom: 10px;">Dark Gray Background</p>
                    <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 40px; width: auto;">
                </div>
                <div style="background: linear-gradient(45deg, #1a1a1a, #333); padding: 20px; text-align: center; border-radius: 5px;">
                    <p style="color: #ccc; margin-bottom: 10px;">Gradient Background</p>
                    <img src="assets/images/logo.svg" alt="Cinema Logo" style="height: 40px; width: auto;">
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">5. Interactive Logo Test</h2>
            <div class="logo-showcase">
                <div class="logo" style="cursor: pointer;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo">
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </div>
            </div>
            <p style="text-align: center; color: #ccc; margin-top: 10px;">
                ↑ Hover over the logo to see animations
            </p>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                ← กลับไปหน้าแรก
            </a>
        </div>
    </div>
</body>
</html>
