<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

$subscription_id = (int)($_GET['subscription_id'] ?? 0);

echo "<h2>🔍 Debug Payment Status</h2>";
echo "<p>User ID: {$_SESSION['user_id']}</p>";
echo "<p>Subscription ID: {$subscription_id}</p>";

try {
    // Get subscription details (same query as payment.php)
    $stmt = $pdo->prepare("
        SELECT us.*, p.name as package_name, p.duration_days, p.price, p.description,
               pt.transaction_ref, pt.qr_code_url, pt.slip_image, pt.status as payment_status
        FROM user_subscriptions us
        JOIN packages p ON us.package_id = p.id
        LEFT JOIN payment_transactions pt ON us.id = pt.subscription_id
        WHERE us.id = ? AND us.user_id = ?
    ");
    $stmt->execute([$subscription_id, $_SESSION['user_id']]);
    $subscription = $stmt->fetch();

    if (!$subscription) {
        echo "<p>❌ ไม่พบ subscription</p>";
        exit();
    }

    echo "<h3>📋 Subscription Details:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    foreach ($subscription as $key => $value) {
        echo "<tr><td>{$key}</td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
    }
    echo "</table>";

    echo "<h3>🔍 Upload Slip Section Conditions:</h3>";
    echo "<p>Condition 1: payment_status === 'pending' → " . ($subscription['payment_status'] === 'pending' ? '✅ TRUE' : '❌ FALSE (' . $subscription['payment_status'] . ')') . "</p>";
    echo "<p>Condition 2: !slip_image → " . (!$subscription['slip_image'] ? '✅ TRUE' : '❌ FALSE (has slip: ' . $subscription['slip_image'] . ')') . "</p>";
    echo "<p>Show Upload Section: " . (($subscription['payment_status'] === 'pending' && !$subscription['slip_image']) ? '✅ YES' : '❌ NO') . "</p>";

    echo "<h3>💰 All Payment Transactions for this subscription:</h3>";
    $stmt = $pdo->prepare("
        SELECT * FROM payment_transactions
        WHERE subscription_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$subscription_id]);
    $payments = $stmt->fetchAll();

    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Amount</th><th>Status</th><th>Method</th><th>Slip Image</th><th>Created</th></tr>";
        foreach ($payments as $payment) {
            $highlight = ($payment['status'] === 'pending') ? 'style="background-color: yellow;"' : '';
            echo "<tr {$highlight}>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . $payment['user_id'] . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . $payment['payment_method'] . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅ ' . basename($payment['slip_image']) : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ ไม่พบ payment transactions สำหรับ subscription นี้</p>";
    }

    echo "<h3>🔧 Possible Solutions:</h3>";
    if ($subscription['payment_status'] !== 'pending') {
        echo "<p>🔴 Payment status is not 'pending'. Current status: " . $subscription['payment_status'] . "</p>";
        echo "<p>💡 Solution: Check if payment transaction was created properly when package was selected.</p>";
    }
    
    if ($subscription['slip_image']) {
        echo "<p>🔴 Slip image already exists: " . $subscription['slip_image'] . "</p>";
        echo "<p>💡 Solution: User has already uploaded a slip. Check payment status or allow re-upload.</p>";
    }

    if (!$payments) {
        echo "<p>🔴 No payment transactions found for this subscription.</p>";
        echo "<p>💡 Solution: Create a payment transaction manually or check package selection process.</p>";
        
        echo "<h4>🛠️ Create Payment Transaction:</h4>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='create_payment' value='1'>";
        echo "<button type='submit' style='background: green; color: white; padding: 10px; border: none; cursor: pointer;'>Create Payment Transaction</button>";
        echo "</form>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle create payment transaction
if ($_POST && isset($_POST['create_payment'])) {
    try {
        $transaction_ref = 'DEBUG' . date('YmdHis') . str_pad($subscription_id, 4, '0', STR_PAD_LEFT);
        $stmt = $pdo->prepare("
            INSERT INTO payment_transactions (subscription_id, user_id, amount, transaction_ref, status, created_at) 
            VALUES (?, ?, ?, ?, 'pending', NOW())
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id'], $subscription['price'], $transaction_ref]);
        
        echo "<p>✅ Created payment transaction successfully!</p>";
        echo "<p><a href='?subscription_id={$subscription_id}'>Refresh Page</a></p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error creating payment transaction: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='/payment?subscription_id={$subscription_id}'>← กลับไปหน้า Payment</a></p>";
echo "<p><a href='/packages'>← กลับไปหน้า Packages</a></p>";
?>
