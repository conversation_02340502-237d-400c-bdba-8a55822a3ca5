# AutoPaymentChecker v2.1 - Production Ready

## 🎬 Jellyfin by <PERSON> - <PERSON> Payment Checker

### ✨ New Features v2.1:
- ✅ **Dual Environment Support**: Automatic detection of Local vs Production
- ✅ **Production API Integration**: Direct HTTPS API calls to embyjames.xyz
- ✅ **Smart Environment Detection**: Automatically switches between local PHP scripts and production APIs
- ✅ **Production Database Support**: Uses production database (*************)
- ✅ **Enhanced Error Handling**: Better error reporting and connection management
- ✅ **Environment Indicator**: UI shows current environment (Local/Production)

### 🌐 Environment Detection:
The application automatically detects the environment:
- **Local Mode**: When running on Windows with localhost accessible
- **Production Mode**: When running on Linux or localhost not accessible

### 🔧 Configuration:

#### Local Environment:
- PHP Path: `C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe`
- Web Root: `C:\laragon\www`
- Database: localhost
- Uses PHP scripts directly

#### Production Environment:
- Base URL: `https://embyjames.xyz`
- Database: `*************`
- Uses API endpoints:
  - `/api/payment-check.php` - PayNoi payment verification
  - `/api/manual-check.php` - Manual slip verification
  - `/api/expiration-check.php` - User expiration management
  - `/api/health.php` - System health check

### 🚀 How to Use:

#### For Local Development:
1. Run on Windows with Laragon
2. Application will detect local environment automatically
3. Uses local PHP scripts and localhost database

#### For Production:
1. Deploy to Linux server (************* or embyjames.xyz)
2. Application will detect production environment automatically
3. Uses HTTPS API calls to embyjames.xyz
4. Connects to production database

### 📋 API Endpoints:

#### POST /api/payment-check.php
- Checks PayNoi transactions against pending payments
- Automatically verifies and processes payments
- Updates user accounts and Jellyfin access

#### POST /api/manual-check.php
- Checks manual payment slips
- Validates slip image files
- Prepares for manual verification

#### POST /api/expiration-check.php
- Checks for expired users
- Automatically disables expired accounts in Jellyfin
- Maintains account security

#### GET/POST /api/health.php
- System health monitoring
- Database connectivity check
- PayNoi API status
- Environment information

### 🔍 Testing:
- Click "Test APIs" to verify connectivity
- Local mode: Tests PHP scripts, database, and PayNoi API
- Production mode: Tests all API endpoints and connectivity

### 📊 Monitoring:
- Real-time log display
- Payment verification status
- User expiration tracking
- API response monitoring

### 🛠️ Troubleshooting:

#### Local Mode Issues:
- Verify PHP path is correct
- Check Laragon is running
- Ensure database is accessible
- Verify script files exist

#### Production Mode Issues:
- Check internet connectivity
- Verify embyjames.xyz is accessible
- Test API endpoints manually
- Check production database connection

### 🔐 Security:
- Production database credentials secured
- API endpoints protected
- HTTPS communication in production
- Error logging for debugging

### 📝 Logs:
- Real-time activity monitoring
- Payment processing logs
- Error tracking and reporting
- API response logging

---

## 🎯 Quick Start:

1. **Run the application**: `python AutoPaymentChecker.py`
2. **Check environment**: Look for "(Local)" or "(Production)" in title
3. **Test connectivity**: Click "Test APIs" button
4. **Start monitoring**: Click "Start Auto Check"
5. **Monitor logs**: Watch real-time activity in log window

The application will automatically adapt to your environment and use the appropriate configuration!
