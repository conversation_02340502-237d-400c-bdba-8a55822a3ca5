<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require admin access
require_login();
require_admin();

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get all packages
$stmt = $pdo->query("SELECT * FROM packages ORDER BY price ASC");
$packages = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ดูแพ็คเกจทั้งหมด - Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .packages-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 2rem 0;
        }
        .packages-table th,
        .packages-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .packages-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .packages-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-active { background: #28a745; color: #fff; }
        .status-inactive { background: #dc3545; color: #fff; }
        .max-sessions-highlight {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="main-header">
            <div class="container">
                <div class="logo">Jellyfin Admin</div>
                <nav class="main-nav">
                    <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                    <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                            <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                            <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                    <div class="profile-dropdown" id="profileDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                        <div class="dropdown-menu">
                            <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                            <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                        </div>
                    </div>
                </nav>
            </div>
        </header>

        <main class="main-content">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">แพ็คเกจทั้งหมด</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">ดูข้อมูลแพ็คเกจและการตั้งค่า Max Sessions</p>
            </div>

            <div class="fade-in-scroll">
                <h3>รายการแพ็คเกจ</h3>
                <div style="overflow-x: auto;">
                    <table class="packages-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>ชื่อแพ็คเกจ</th>
                                <th>ราคา</th>
                                <th>ระยะเวลา</th>
                                <th>Max Sessions</th>
                                <th>คำอธิบาย</th>
                                <th>สถานะ</th>
                                <th>วันที่สร้าง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($packages as $package): ?>
                                <tr>
                                    <td><?php echo $package['id']; ?></td>
                                    <td><strong><?php echo htmlspecialchars($package['name']); ?></strong></td>
                                    <td><strong><?php echo number_format($package['price'], 2); ?> บาท</strong></td>
                                    <td><?php echo $package['duration_days']; ?> วัน</td>
                                    <td>
                                        <span class="max-sessions-highlight">
                                            <?php echo $package['max_sessions'] ?? 1; ?> อุปกรณ์
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($package['description']); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $package['is_active'] ? 'active' : 'inactive'; ?>">
                                            <?php echo $package['is_active'] ? 'ใช้งานได้' : 'ปิดใช้งาน'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($package['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 2rem; padding: 1rem; background: rgba(255, 193, 7, 0.1); border-radius: 8px; border: 1px solid rgba(255, 193, 7, 0.3);">
                    <h4 style="color: #ffc107; margin-bottom: 1rem;">📋 ข้อมูลสำคัญ:</h4>
                    <ul style="color: #ccc; margin: 0; padding-left: 1.5rem;">
                        <li><strong>Package 60 บาท:</strong> ดูได้ 1 อุปกรณ์ (15 วัน)</li>
                        <li><strong>Package 100 บาท:</strong> ดูได้ 1 อุปกรณ์ (30 วัน)</li>
                        <li><strong>Package 150 บาท:</strong> ดูได้ 2 อุปกรณ์ (30 วัน) 🎯</li>
                    </ul>
                    <p style="color: #ccc; margin-top: 1rem; font-size: 0.9rem;">
                        💡 เมื่อผู้ใช้ซื้อ Package 150 บาท ระบบจะตั้งค่า Max Sessions ใน Jellyfin เป็น 2 อุปกรณ์อัตโนมัติ
                    </p>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
