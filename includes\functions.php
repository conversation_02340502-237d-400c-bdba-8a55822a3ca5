<?php
/**
 * Common functions for the Jellyfin Registration System
 */

/**
 * Sanitize input data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Safe htmlspecialchars that handles null values
 */
function safe_html($value, $default = '') {
    if ($value === null) {
        return htmlspecialchars($default);
    }
    return htmlspecialchars((string)$value);
}

/**
 * Validate email format
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Hash password securely
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate random string for tokens
 */
function generate_random_string($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Check if user is logged in
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user is admin
 */
function is_admin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'];
}

/**
 * Redirect to login if not authenticated
 */
function require_login() {
    if (!is_logged_in()) {
        header('Location: /login');
        exit();
    }
}

/**
 * Redirect to login if not admin
 */
function require_admin() {
    if (!is_admin()) {
        header('Location: /');
        exit();
    }
}

/**
 * Display flash messages
 */
function display_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        echo "<div class='flash-message flash-{$type}'>{$message}</div>";
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
    }
}

/**
 * Set flash message
 */
function set_flash_message($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

/**
 * Log activity
 */
function log_activity($user_id, $action, $details = '') {
    global $pdo;

    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$user_id, $action, $details]);
}

/**
 * Safe number format for displaying amounts
 */
function format_amount($amount, $decimals = 2) {
    return number_format($amount ?? 0, $decimals);
}

/**
 * Safe number format for displaying currency
 */
function format_currency($amount, $currency = 'บาท') {
    return number_format($amount ?? 0, 2) . ' ' . $currency;
}
?>
