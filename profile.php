<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

$errors = [];
$success = false;

// Get user information
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Handle form submission
if ($_POST) {
    $phone = sanitize_input($_POST['phone']);
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validation
    if (empty($phone)) {
        $errors[] = 'กรุณากรอกเบอร์โทรศัพท์';
    } elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
        $errors[] = 'เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลัก';
    }

    // Check if phone is already taken by another user
    if (empty($errors) && $phone !== $user['phone']) {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ? AND id != ?");
        $stmt->execute([$phone, $_SESSION['user_id']]);
        if ($stmt->fetch()) {
            $errors[] = 'เบอร์โทรศัพท์นี้ถูกใช้งานแล้ว';
        }
    }

    // Password change validation
    $change_password = !empty($new_password);
    if ($change_password) {
        if (empty($current_password)) {
            $errors[] = 'กรุณากรอกรหัสผ่านปัจจุบัน';
        } elseif (!verify_password($current_password, $user['password_hash'])) {
            $errors[] = 'รหัสผ่านปัจจุบันไม่ถูกต้อง';
        }

        if (strlen($new_password) < 6) {
            $errors[] = 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
        }

        if ($new_password !== $confirm_password) {
            $errors[] = 'รหัสผ่านใหม่ไม่ตรงกัน';
        }
    }
    
    // Update user if no errors
    if (empty($errors)) {
        try {
            if ($change_password) {
                $password_hash = hash_password($new_password);
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET phone = ?, password_hash = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$phone, $password_hash, $_SESSION['user_id']]);

                // Update password in Jellyfin
                try {
                    require_once 'includes/JellyfinAPI.php';

                    // Get user's Jellyfin ID
                    $stmt = $pdo->prepare("SELECT jellyfin_user_id FROM jellyfin_users WHERE user_id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                    $jellyfin_user = $stmt->fetch();

                    if ($jellyfin_user && $jellyfin_user['jellyfin_user_id']) {
                        $jellyfin = new JellyfinAPI();
                        $jellyfin->updateUserPassword($jellyfin_user['jellyfin_user_id'], $new_password, $current_password);

                        // Update stored password in database
                        $stmt = $pdo->prepare("UPDATE jellyfin_users SET jellyfin_password = ? WHERE user_id = ?");
                        $stmt->execute([$new_password, $_SESSION['user_id']]);
                    }
                } catch (Exception $e) {
                    error_log('Failed to update Jellyfin password: ' . $e->getMessage());
                    // Don't fail the entire operation if Jellyfin update fails
                }
            } else {
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET phone = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$phone, $_SESSION['user_id']]);
            }

            // Log activity
            $activity_details = $change_password ? 'User profile and password updated' : 'User profile updated';
            log_activity($_SESSION['user_id'], 'profile_updated', $activity_details);

            $success = true;
            $success_message = $change_password ? 'โปรไฟล์และรหัสผ่านอัปเดตเรียบร้อยแล้ว!' : 'โปรไฟล์อัปเดตเรียบร้อยแล้ว!';
            set_flash_message($success_message, 'success');

            // Refresh user data
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();

        } catch (Exception $e) {
            $errors[] = 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์ กรุณาลองใหม่อีกครั้ง';
            error_log('Profile update error: ' . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>โปรไฟล์ - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        html {
            scroll-behavior: smooth;
        }
        .guide-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.3s ease, border-color 0.3s ease;
        }
        .device-card:hover {
            transform: translateY(-5px);
            border-color: #ffc107;
        }
        .device-icon {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .device-title {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1rem;
            color: #ffc107;
        }
        .device-steps {
            list-style: none;
            padding: 0;
        }
        .device-steps li {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
            position: relative;
        }
        .device-steps li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background: #ffc107;
            color: #000;
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .device-steps {
            counter-reset: step-counter;
        }
        .download-links {
            margin-top: 1rem;
            text-align: center;
        }
        .download-btn {
            display: inline-block;
            background: #ffc107;
            color: #000;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            margin: 0.25rem;
            font-weight: bold;
            transition: background 0.3s ease;
        }
        .download-btn:hover {
            background: #e0a800;
            color: #000;
        }

        .server-info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .server-info h3 {
            color: #17a2b8;
            margin-top: 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 4px;
        }
        .info-label {
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 0.5rem;
        }
        .info-value {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem;
            border-radius: 4px;
            word-break: break-all;
        }
        .troubleshooting {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .troubleshooting h3 {
            color: #dc3545;
            margin-top: 0;
        }
        .faq-item {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .faq-question {
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 0.5rem;
        }
        .faq-answer {
            color: #ccc;
        }
        @media (max-width: 768px) {
            .device-grid {
                grid-template-columns: 1fr;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
            .guide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                <a href="/packages" class="nav-link">แพ็คเกจ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php if (is_admin()): ?>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                            <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                            <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                            <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="form-container fade-in-up">
                <h2 style="text-align: center; margin-bottom: 2rem;">แก้ไขโปรไฟล์</h2>
                
                <?php display_flash_message(); ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="flash-message flash-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="flash-message flash-success">
                        อัปเดตโปรไฟล์เรียบร้อยแล้ว!
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="profileForm">
                    <div class="form-group">
                        <label for="username">ชื่อผู้ใช้</label>
                        <input type="text" id="username" class="form-control"
                               value="<?php echo htmlspecialchars($user['username']); ?>"
                               disabled>
                        <small style="color: #888;">ไม่สามารถเปลี่ยนชื่อผู้ใช้ได้</small>
                    </div>

                    <div class="form-group">
                        <label for="phone">เบอร์โทรศัพท์ *</label>
                        <input type="tel" id="phone" name="phone" class="form-control"
                               value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
                               required placeholder="0812345678" pattern="[0-9]{10}" maxlength="10">
                        <small style="color: #888; font-size: 0.8rem;">กรุณาใส่เบอร์โทรศัพท์ 10 หลัก</small>
                    </div>
                    
                    <hr style="border: 1px solid #333; margin: 2rem 0;">
                    
                    <h3 style="margin-bottom: 1rem;">เปลี่ยนรหัสผ่าน (ไม่บังคับ)</h3>

                    <div class="form-group">
                        <label for="current_password">รหัสผ่านปัจจุบัน</label>
                        <input type="password" id="current_password" name="current_password" class="form-control"
                               placeholder="กรอกรหัสผ่านปัจจุบัน">
                    </div>

                    <div class="form-group">
                        <label for="new_password">รหัสผ่านใหม่</label>
                        <input type="password" id="new_password" name="new_password" class="form-control"
                               placeholder="กรอกรหัสผ่านใหม่">
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">ยืนยันรหัสผ่านใหม่</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control"
                               placeholder="ยืนยันรหัสผ่านใหม่">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary noir-button" style="width: 100%;">
                            อัปเดตโปรไฟล์
                        </button>
                    </div>

                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="/dashboard" style="color: #ccc;">กลับไปหน้าหลัก</a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
</body>
</html>
