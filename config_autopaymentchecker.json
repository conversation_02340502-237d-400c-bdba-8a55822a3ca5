{"application": {"name": "AutoPaymentChecker", "version": "2.0.0", "description": "Dual-mode payment checker for <PERSON><PERSON><PERSON> by <PERSON>"}, "modes": {"local": {"enabled": true, "description": "Local PHP execution mode", "base_url": "http://localhost", "php_path": "C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64\\php.exe", "web_root": "C:\\laragon\\www", "scripts": {"paynoi": "paynoi-transactions.php", "manual": "manual_check_payments.php", "expiration": "cron/check_expiration.php", "cloudflare": "start_cloudflare.bat"}}, "production": {"enabled": true, "description": "Production API mode", "base_url": "https://embyjames.xyz", "endpoints": {"paynoi": "/api/paynoi-check", "manual": "/api/manual-check", "expiration": "/api/expiration-check", "stats": "/api/payment-stats", "health": "/api/health"}}}, "default_settings": {"use_local_mode": true, "payment_interval": 5, "expiration_interval": 3600, "connection_timeout": 30, "retry_attempts": 3}, "paynoi_api": {"api_key": "6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446", "record_key": "100568", "api_url": "https://paynoi.com/api_line"}, "logging": {"level": "INFO", "max_lines": 1000, "auto_clear": true}, "ui": {"theme": "dark", "window_size": {"width": 1000, "height": 700}, "auto_scroll": true}}