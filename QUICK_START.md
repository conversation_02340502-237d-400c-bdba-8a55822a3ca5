# 🚀 Jellyfin by <PERSON> - Quick Start Guide

## 📋 Production Deployment (************* / embyjames.xyz)

### 🎯 Option 1: Root User Installation (Recommended)

```bash
# 1. Upload files to server
scp *.py *.sh *.service *.txt root@*************:/tmp/

# 2. Connect to server
ssh root@*************

# 3. Go to files directory
cd /tmp

# 4. Make script executable and run
chmod +x install_production_root.sh
./install_production_root.sh
```

### 🎯 Option 2: Regular User Installation

```bash
# 1. Upload files to server
scp *.py *.sh *.service *.txt user@*************:/tmp/

# 2. Connect to server
ssh user@*************

# 3. Go to files directory
cd /tmp

# 4. Make script executable and run
chmod +x install_production.sh
./install_production.sh
```

---

## ✅ Verify Installation

### 1. Check Service Status
```bash
systemctl status jellyfin-payment-checker
```

### 2. Monitor System
```bash
python3 /opt/jellyfin/monitor_production.py
```

### 3. View Logs
```bash
# Real-time payment logs
tail -f /var/log/jellyfin/continuous_payment.log

# Service logs
journalctl -u jellyfin-payment-checker -f
```

---

## 🔧 Common Commands

### Service Management
```bash
# Start service
systemctl start jellyfin-payment-checker

# Stop service
systemctl stop jellyfin-payment-checker

# Restart service
systemctl restart jellyfin-payment-checker

# Check status
systemctl status jellyfin-payment-checker
```

### Manual Testing
```bash
# Health check
python3 /opt/jellyfin/production_cron.py health

# Payment check
python3 /opt/jellyfin/production_cron.py payment

# Full system check
python3 /opt/jellyfin/production_cron.py full
```

### Monitoring
```bash
# Complete system status
python3 /opt/jellyfin/monitor_production.py

# API health only
python3 /opt/jellyfin/monitor_production.py api

# Service status only
python3 /opt/jellyfin/monitor_production.py service

# Recent activity
python3 /opt/jellyfin/monitor_production.py activity
```

---

## 📊 What Gets Installed

### 🔄 Services
- **Continuous Payment Checker**: Runs every 5 seconds
- **Cron Jobs**: Health checks, full system checks, maintenance
- **API Endpoints**: Production-ready REST APIs

### 📁 File Locations
- **Scripts**: `/opt/jellyfin/`
- **Logs**: `/var/log/jellyfin/`
- **Service**: `/etc/systemd/system/jellyfin-payment-checker.service`
- **Crontab**: `crontab -u www-data -l`

### 🌐 API Endpoints
- **Health**: `https://embyjames.xyz/api/health.php`
- **Payment Check**: `https://embyjames.xyz/api/payment-check.php`
- **Manual Check**: `https://embyjames.xyz/api/manual-check.php`
- **Expiration**: `https://embyjames.xyz/api/expiration-check.php`

---

## 🚨 Troubleshooting

### Service Won't Start
```bash
# Check detailed status
systemctl status jellyfin-payment-checker -l

# View error logs
journalctl -u jellyfin-payment-checker --no-pager

# Check file permissions
ls -la /opt/jellyfin/
ls -la /var/log/jellyfin/
```

### API Not Working
```bash
# Test API directly
curl -X POST https://embyjames.xyz/api/health.php

# Check web server logs
tail -f /var/log/apache2/error.log
tail -f /var/log/nginx/error.log
```

### Database Issues
```bash
# Test database connection
mysql -h ************* -u root -p jellyfin_registration

# Check database status
python3 /opt/jellyfin/monitor_production.py database
```

---

## 📈 Success Indicators

### ✅ Everything Working When:
- Service status shows "active (running)"
- Monitor shows all green checkmarks
- Recent activity in logs (< 5 minutes old)
- API endpoints return 200 OK
- Database connections successful

### ❌ Check These If Issues:
- Service status "failed" or "inactive"
- API timeouts or connection errors
- Stale log files (> 1 hour old)
- Database connection failures
- High error rates in logs

---

## 🎉 Quick Verification Checklist

After installation, run these commands to verify everything works:

```bash
# 1. Service running?
systemctl is-active jellyfin-payment-checker

# 2. APIs working?
python3 /opt/jellyfin/monitor_production.py api

# 3. Database connected?
python3 /opt/jellyfin/monitor_production.py database

# 4. Recent activity?
python3 /opt/jellyfin/monitor_production.py activity

# 5. Full system check
python3 /opt/jellyfin/monitor_production.py
```

If all commands show green/success status, your system is ready! 🎬

---

## 📞 Need Help?

1. **Check logs first**: `python3 /opt/jellyfin/monitor_production.py`
2. **Service issues**: `journalctl -u jellyfin-payment-checker -f`
3. **API problems**: `python3 /opt/jellyfin/monitor_production.py api`
4. **Database issues**: `python3 /opt/jellyfin/monitor_production.py database`

**Your Jellyfin by James system is now running 24/7 on production!** 🚀
