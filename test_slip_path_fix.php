<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test Slip Path Fix</h2>";

try {
    echo "<h3>🔍 Step 1: Check Current Slip Paths</h3>";
    
    $stmt = $pdo->prepare("
        SELECT pt.id, pt.user_id, pt.slip_image, pt.status, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    echo "<p>Found " . count($payments) . " payments with slip images</p>";
    
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Status</th><th>Slip Path (DB)</th><th>File Exists</th><th>Issue</th></tr>";
        
        $issueCount = 0;
        foreach ($payments as $payment) {
            $dbPath = $payment['slip_image'];
            $fileExists = file_exists($dbPath);
            $issue = '';
            
            // Check for path issues
            if (strpos($dbPath, 'uploads/slips/uploads/slips/') !== false) {
                $issue = '❌ Duplicate prefix';
                $issueCount++;
            } elseif (strpos($dbPath, 'uploads/slips/') !== 0 && !empty($dbPath)) {
                $issue = '⚠️ Missing prefix';
                $issueCount++;
            } elseif (!$fileExists) {
                $issue = '❌ File not found';
                $issueCount++;
            } else {
                $issue = '✅ OK';
            }
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . htmlspecialchars($dbPath) . "</td>";
            echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
            echo "<td>" . $issue . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Issues found: {$issueCount}</strong></p>";
        
        if ($issueCount > 0) {
            echo "<form method='POST'>";
            echo "<input type='hidden' name='fix_all_paths' value='1'>";
            echo "<button type='submit' style='background: red; color: white; padding: 10px; border: none; cursor: pointer;'>Fix All Path Issues</button>";
            echo "</form>";
        }
    }
    
    echo "<h3>🧪 Step 2: Test Manual Cron Logic</h3>";
    
    // Test the same logic as manual cron
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
        ORDER BY pt.created_at ASC
        LIMIT 5
    ");
    $stmt->execute();
    $manualPayments = $stmt->fetchAll();
    
    echo "<p>Manual payments found: " . count($manualPayments) . "</p>";
    
    if ($manualPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Slip Path</th><th>Fixed Path</th><th>File Exists</th></tr>";
        
        foreach ($manualPayments as $payment) {
            $slipImage = $payment['slip_image'];
            
            // Apply the same fix as in cron
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $fixedPath = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $fixedPath = 'uploads/slips/' . $slipImage;
            } else {
                $fixedPath = $slipImage;
            }
            
            $fileExists = file_exists($fixedPath);
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . htmlspecialchars($slipImage) . "</td>";
            echo "<td>" . htmlspecialchars($fixedPath) . "</td>";
            echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='POST'>";
        echo "<input type='hidden' name='test_manual_cron' value='1'>";
        echo "<button type='submit' style='background: blue; color: white; padding: 10px; border: none; cursor: pointer;'>Test Manual Cron with Fixed Paths</button>";
        echo "</form>";
    }
    
    echo "<h3>📁 Step 3: Directory Contents</h3>";
    
    $uploadDir = 'uploads/slips/';
    if (is_dir($uploadDir)) {
        $files = scandir($uploadDir);
        $imageFiles = array_filter($files, function($file) {
            return preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
        });
        
        echo "<p>✅ Upload directory: {$uploadDir}</p>";
        echo "<p>📁 Image files: " . count($imageFiles) . "</p>";
        
        if (count($imageFiles) > 0) {
            echo "<h4>Recent files (last 5):</h4>";
            $recentFiles = array_slice($imageFiles, -5);
            echo "<ul>";
            foreach ($recentFiles as $file) {
                $filePath = $uploadDir . $file;
                $fileSize = number_format(filesize($filePath));
                $fileTime = date('Y-m-d H:i:s', filemtime($filePath));
                echo "<li>{$file} ({$fileSize} bytes, {$fileTime})</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ Upload directory not found: {$uploadDir}</p>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['fix_all_paths'])) {
        echo "<h3>🔧 Fixing All Path Issues:</h3>";
        try {
            $stmt = $pdo->prepare("
                SELECT id, slip_image FROM payment_transactions
                WHERE slip_image IS NOT NULL AND slip_image != ''
            ");
            $stmt->execute();
            $allPayments = $stmt->fetchAll();
            
            $fixedCount = 0;
            
            foreach ($allPayments as $payment) {
                $currentPath = $payment['slip_image'];
                $newPath = null;
                
                // Fix duplicate prefix
                if (strpos($currentPath, 'uploads/slips/uploads/slips/') !== false) {
                    $newPath = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $currentPath);
                }
                // Add missing prefix
                elseif (strpos($currentPath, 'uploads/slips/') !== 0 && !empty($currentPath)) {
                    $newPath = 'uploads/slips/' . basename($currentPath);
                }
                
                if ($newPath && $newPath !== $currentPath) {
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET slip_image = ?
                        WHERE id = ?
                    ");
                    $updateStmt->execute([$newPath, $payment['id']]);
                    
                    echo "<p>✅ Fixed payment ID {$payment['id']}: {$currentPath} → {$newPath}</p>";
                    $fixedCount++;
                }
            }
            
            echo "<p><strong>✅ Fixed {$fixedCount} path issues</strong></p>";
            echo "<p><a href='?'>Refresh Page</a></p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Error fixing paths: " . $e->getMessage() . "</p>";
        }
    }
    
    if (isset($_POST['test_manual_cron'])) {
        echo "<h3>🧪 Testing Manual Cron Logic:</h3>";
        try {
            // Simulate the manual cron logic
            $stmt = $pdo->prepare("
                SELECT pt.*, u.username
                FROM payment_transactions pt
                JOIN users u ON pt.user_id = u.id
                WHERE pt.status = 'pending'
                AND pt.payment_method = 'manual'
                AND pt.slip_image IS NOT NULL
                AND pt.slip_image != ''
                AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
                ORDER BY pt.created_at ASC
                LIMIT 5
            ");
            $stmt->execute();
            $testPayments = $stmt->fetchAll();
            
            echo "<p>Testing " . count($testPayments) . " payments...</p>";
            
            foreach ($testPayments as $payment) {
                echo "<h4>Payment ID {$payment['id']} - {$payment['username']} - {$payment['amount']} THB:</h4>";
                
                // Apply path fix
                $slipImage = $payment['slip_image'];
                if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                    $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
                } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                    $slipImage = 'uploads/slips/' . $slipImage;
                }
                
                echo "<p>Slip path: {$slipImage}</p>";
                
                if (!file_exists($slipImage)) {
                    echo "<p>❌ File not found</p>";
                    continue;
                }
                
                $fileSize = filesize($slipImage);
                $fileTime = filemtime($slipImage);
                $paymentTime = strtotime($payment['created_at']);
                
                echo "<p>✅ File found ({$fileSize} bytes)</p>";
                
                // Check auto-verification criteria
                $isValidSize = $fileSize > 10000 && $fileSize < 10000000;
                $isRecent = abs($fileTime - $paymentTime) < 3600;
                $isSmallAmount = $payment['amount'] <= 100;
                
                echo "<p>Valid size: " . ($isValidSize ? '✅' : '❌') . "</p>";
                echo "<p>Recent file: " . ($isRecent ? '✅' : '❌') . "</p>";
                echo "<p>Small amount: " . ($isSmallAmount ? '✅' : '❌') . "</p>";
                
                if ($isSmallAmount && $isValidSize && $isRecent) {
                    echo "<p>🎉 <strong>Would be auto-verified!</strong></p>";
                } else {
                    echo "<p>⚠️ Would need manual review</p>";
                }
                
                echo "<hr>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Test error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/debug_slip_path.php'>🔍 Debug Slip Path</a></p>";
?>
