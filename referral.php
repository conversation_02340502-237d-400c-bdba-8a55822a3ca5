<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/ReferralSystem.php';

// Require login
require_login();

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

$referralSystem = new ReferralSystem($pdo);
$stats = $referralSystem->getReferralStats($_SESSION['user_id']);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบแนะนำเพื่อน - Jellyfin</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .referral-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem 0;
        }
        .referral-code {
            background: linear-gradient(135deg, #ffc107, #ff8f00);
            color: #000;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 1rem 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffc107;
        }
        .stat-label {
            color: #ccc;
            margin-top: 0.5rem;
        }
        .transactions-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 2rem 0;
        }
        .transactions-table th,
        .transactions-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .transactions-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .copy-button {
            background: #28a745;
            color: #fff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 1rem;
        }
        .copy-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                <a href="/packages" class="nav-link active">แพ็คเกจ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php if (is_admin()): ?>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                            <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                            <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                            <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

        <main class="main-content">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">ระบบแนะนำเพื่อน</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">แนะนำเพื่อนและรับ 15 points เมื่อเพื่อนซื้อแพ็คเกจ</p>
            </div>

            <div class="fade-in-scroll">
                <div class="referral-card">
                    <h3>รหัสแนะนำของคุณ</h3>
                    <div class="referral-code">
                        <?php echo htmlspecialchars($stats['referral_code']); ?>
                        <button class="copy-button" onclick="copyReferralCode()">คัดลอก</button>
                    </div>
                    <p style="color: #ccc; text-align: center;">
                        แชร์รหัสนี้กับเพื่อนๆ เมื่อเพื่อนสมัครและซื้อแพ็คเกจ คุณจะได้รับ 15 points
                    </p>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <p style="color: #ffc107; font-weight: bold;">ลิงก์แนะนำ:</p>
                        <div style="background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 4px; word-break: break-all;">
                            <span id="referralLink"><?php echo SITE_URL . "/register?ref=" . urlencode($stats['referral_code']); ?></span>
                            <button class="copy-button" onclick="copyReferralLink()">คัดลอกลิงก์</button>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($stats['current_points'], 2); ?></div>
                        <div class="stat-label">Points ปัจจุบัน</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $stats['total_referrals']; ?></div>
                        <div class="stat-label">เพื่อนที่แนะนำ</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value"><?php echo number_format($stats['total_earned'], 2); ?></div>
                        <div class="stat-label">Points ที่ได้รับทั้งหมด</div>
                    </div>
                </div>

                <?php if (!empty($stats['recent_transactions'])): ?>
                <h3>ประวัติการได้รับ Points</h3>
                <div style="overflow-x: auto;">
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th>วันที่</th>
                                <th>เพื่อนที่แนะนำ</th>
                                <th>Points ที่ได้</th>
                                <th>รายละเอียด</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($stats['recent_transactions'] as $transaction): ?>
                                <tr>
                                    <td><?php echo date('d/m/Y H:i', strtotime($transaction['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['referred_username'] ?? 'N/A'); ?></td>
                                    <td style="color: #28a745; font-weight: bold;">+<?php echo number_format($transaction['points_earned'], 2); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>

                <div class="referral-card">
                    <h3>วิธีการใช้งาน</h3>
                    <ol style="color: #ccc; line-height: 1.6;">
                        <li>แชร์รหัสแนะนำหรือลิงก์ให้เพื่อนๆ</li>
                        <li>เพื่อนสมัครสมาชิกโดยใส่รหัสแนะนำของคุณ</li>
                        <li>เมื่อเพื่อนซื้อแพ็คเกจครั้งแรก คุณจะได้รับ 15 points</li>
                        <li>Points สามารถใช้ซื้อแพ็คเกจได้ในอนาคต</li>
                    </ol>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
    <script>
        function copyReferralCode() {
            const code = '<?php echo $stats['referral_code']; ?>';
            navigator.clipboard.writeText(code).then(() => {
                alert('คัดลอกรหัสแนะนำแล้ว: ' + code);
            });
        }
        
        function copyReferralLink() {
            const link = document.getElementById('referralLink').textContent;
            navigator.clipboard.writeText(link).then(() => {
                alert('คัดลอกลิงก์แนะนำแล้ว');
            });
        }
    </script>
</body>
</html>
