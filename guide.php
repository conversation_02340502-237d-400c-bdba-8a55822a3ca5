<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Require login
require_login();

// Connect to database and get user info
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get user info with Jellyfin data
    $stmt = $pdo->prepare("
        SELECT u.*, ju.jellyfin_username, ju.max_simultaneous_sessions, ju.jellyfin_user_id
        FROM users u
        LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
        WHERE u.id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        header('Location: /login');
        exit();
    }
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>คู่มือการใช้งาน - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        html {
            scroll-behavior: smooth;
        }
        .guide-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .device-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.3s ease, border-color 0.3s ease;
        }
        .device-card:hover {
            transform: translateY(-5px);
            border-color: #ffc107;
        }
        .device-icon {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .device-title {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1rem;
            color: #ffc107;
        }
        .device-steps {
            list-style: none;
            padding: 0;
        }
        .device-steps li {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
            position: relative;
        }
        .device-steps li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background: #ffc107;
            color: #000;
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .device-steps {
            counter-reset: step-counter;
        }
        .download-links {
            margin-top: 1rem;
            text-align: center;
        }
        .download-btn {
            display: inline-block;
            background: #ffc107;
            color: #000;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            margin: 0.25rem;
            font-weight: bold;
            transition: background 0.3s ease;
        }
        .download-btn:hover {
            background: #e0a800;
            color: #000;
        }

        .server-info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .server-info h3 {
            color: #17a2b8;
            margin-top: 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 4px;
        }
        .info-label {
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 0.5rem;
        }
        .info-value {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem;
            border-radius: 4px;
            word-break: break-all;
        }
        .troubleshooting {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .troubleshooting h3 {
            color: #dc3545;
            margin-top: 0;
        }
        .faq-item {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .faq-question {
            font-weight: bold;
            color: #ffc107;
            margin-bottom: 0.5rem;
        }
        .faq-answer {
            color: #ccc;
        }
        @media (max-width: 768px) {
            .device-grid {
                grid-template-columns: 1fr;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
            .guide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link">หน้าหลัก</a>
                <a href="/packages" class="nav-link">แพ็คเกจ</a>
                <a href="/guide" class="nav-link active">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php if (is_admin()): ?>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                            <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                            <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                            <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($user['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="guide-container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">คู่มือการใช้งาน Jellyfin</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">วิธีการเชื่อมต่อและใช้งาน Jellyfin บนอุปกรณ์ต่างๆ พร้อมคำแนะนำและเทิป</p>
            </div>

            <!-- Server Information -->
            <div class="server-info fade-in-scroll">
                <h3>ข้อมูลเซิร์ฟเวอร์</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Server URL:</div>
                        <div class="info-value"><?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Username:</div>
                        <div class="info-value"><?php echo htmlspecialchars($user['jellyfin_username'] ?? $user['username']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Max Sessions:</div>
                        <div class="info-value"><?php echo $user['max_simultaneous_sessions'] ?? 1; ?> อุปกรณ์</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Status:</div>
                        <div class="info-value"><?php echo $user['is_active'] ? '🟢 Active' : '🔴 Inactive'; ?></div>
                    </div>
                </div>

                <!-- QR Codes Section -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0;">
                    <!-- Jellyfin QR Code -->
                    <div style="text-align: center; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 8px;">
                        <h4 style="color: #ffc107; margin-bottom: 1rem;">📱 QR Code เชื่อมต่อ Jellyfin</h4>
                        <div style="display: inline-block; background: white; padding: 1rem; border-radius: 8px;">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=<?php echo urlencode(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?>"
                                 alt="QR Code for Jellyfin Server"
                                 style="display: block;">
                        </div>
                        <p style="color: #ccc; font-size: 0.9rem; margin-top: 1rem;">
                            สแกนด้วยแอป Jellyfin เพื่อเชื่อมต่อเซิร์ฟเวอร์อัตโนมัติ
                        </p>
                    </div>

                    <!-- LINE Group QR Code -->
                    <div id="line-group" style="text-align: center; padding: 1rem; background: rgba(0, 195, 0, 0.1); border: 1px solid #00c300; border-radius: 8px;">
                        <h4 style="color: #00c300; margin-bottom: 1rem;">💬 เข้าร่วม LINE กลุ่ม</h4>
                        <div style="display: inline-block; background: white; padding: 1rem; border-radius: 8px; position: relative;">
                            <img id="line-qr-image"
                                 src="line/line.jpg"
                                 alt="QR Code for LINE Group"
                                 style="display: block; width: 200px; height: 200px; object-fit: contain;"
                                 onerror="this.style.display='none'; document.getElementById('line-qr-fallback').style.display='flex';">

                            <!-- Fallback when image fails to load -->
                            <div id="line-qr-fallback" style="display: none; width: 200px; height: 200px; background: #f0f0f0; border: 2px dashed #00c300; flex-direction: column; align-items: center; justify-content: center; color: #666;">
                                <div style="font-size: 3rem; margin-bottom: 10px;">📱</div>
                                <div style="font-weight: bold; color: #00c300;">QR Code ไม่พร้อมใช้งาน</div>
                                <div style="font-size: 0.8rem; margin-top: 5px;">กรุณาติดต่อ Admin</div>
                                <div style="font-size: 0.7rem; margin-top: 10px; color: #999;">
                                    Path: line/line.jpg
                                </div>
                            </div>
                        </div>
                        <p style="color: #ccc; font-size: 0.9rem; margin-top: 1rem;">
                            สแกนเพื่อเข้าร่วม LINE กลุ่มสำหรับการสนับสนุนและอัปเดต
                        </p>



                    </div>
                </div>

                <p style="color: #ffc107; margin-top: 1rem;">
                    <strong>หมายเหตุ:</strong> กรุณาใช้ Username และ Password เดียวกับที่ใช้ล็อกอินเว็บไซต์นี้
                </p>
            </div>



            <!-- Device Guides -->
            <div class="fade-in-scroll">
                <h3 style="text-align: center; margin-bottom: 2rem; color: #ffc107;">📱 คู่มือการเชื่อมต่อตามอุปกรณ์</h3>
                
                <div class="device-grid">
                    <!-- Android -->
                    <div class="device-card">
                        <div class="device-icon">📱</div>
                        <div class="device-title">Android</div>
                        <ol class="device-steps">
                            <li>ดาวน์โหลดแอป Jellyfin จาก Google Play Store</li>
                            <li>เปิดแอปและเลือก "Add Server"</li>
                            <li>ใส่ Server URL: <code><?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?></code></li>
                            <li>กรอก Username และ Password</li>
                            <li>เลือก "Sign In" เพื่อเข้าสู่ระบบ</li>
                            <li>เริ่มรับชมได้ทันที!</li>
                        </ol>
                        <div class="download-links">
                            <a href="https://play.google.com/store/apps/details?id=org.jellyfin.mobile" target="_blank" class="download-btn">
                                📥 ดาวน์โหลด Android
                            </a>
                        </div>
                    </div>

                    <!-- iOS -->
                    <div class="device-card">
                        <div class="device-icon">📱</div>
                        <div class="device-title">iPhone / iPad</div>
                        <ol class="device-steps">
                            <li>ดาวน์โหลดแอป Jellyfin จาก App Store</li>
                            <li>เปิดแอปและแตะ "Add Server"</li>
                            <li>ใส่ Server Address: <code><?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?></code></li>
                            <li>กรอก Username และ Password</li>
                            <li>แตะ "Sign In"</li>
                            <li>เพลิดเพลินกับการรับชม!</li>
                        </ol>
                        <div class="download-links">
                            <a href="https://apps.apple.com/app/jellyfin-mobile/id1480192618" target="_blank" class="download-btn">
                                📥 ดาวน์โหลด iOS
                            </a>
                        </div>
                    </div>

                    <!-- Windows -->
                    <div class="device-card">
                        <div class="device-icon">💻</div>
                        <div class="device-title">Windows</div>
                        <ol class="device-steps">
                            <li>ดาวน์โหลด Jellyfin Media Player สำหรับ Windows</li>
                            <li>ติดตั้งและเปิดโปรแกรม</li>
                            <li>คลิก "Add Server"</li>
                            <li>ใส่ Server URL: <code><?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?></code></li>
                            <li>ล็อกอินด้วย Username และ Password</li>
                            <li>เริ่มรับชมบนหน้าจอใหญ่!</li>
                        </ol>
                        <div class="download-links">
                            <a href="https://jellyfin.org/downloads/clients" target="_blank" class="download-btn">
                                📥 ดาวน์โหลด Windows
                            </a>
                        </div>
                    </div>

                    <!-- macOS -->
                    <div class="device-card">
                        <div class="device-icon">🖥️</div>
                        <div class="device-title">macOS</div>
                        <ol class="device-steps">
                            <li>ดาวน์โหลด Jellyfin Media Player สำหรับ macOS</li>
                            <li>ลากไฟล์ .dmg ไปยังโฟลเดอร์ Applications</li>
                            <li>เปิดแอป Jellyfin</li>
                            <li>เลือก "Add Server"</li>
                            <li>ใส่ Server URL และข้อมูลล็อกอิน</li>
                            <li>เพลิดเพลินกับ Jellyfin บน Mac!</li>
                        </ol>
                        <div class="download-links">
                            <a href="https://jellyfin.org/downloads/clients" target="_blank" class="download-btn">
                                📥 ดาวน์โหลด macOS
                            </a>
                        </div>
                    </div>

                    <!-- Smart TV -->
                    <div class="device-card">
                        <div class="device-icon">📺</div>
                        <div class="device-title">Smart TV</div>
                        <ol class="device-steps">
                            <li>เปิดเว็บเบราว์เซอร์บน Smart TV</li>
                            <li>ไปที่: <code><?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?></code></li>
                            <li>ล็อกอินด้วย Username และ Password</li>
                            <li>หรือติดตั้งแอป Jellyfin จาก App Store ของ TV</li>
                            <li>รับชมบนหน้าจอทีวีขนาดใหญ่!</li>
                        </ol>
                        <div class="download-links">
                            <a href="https://jellyfin.org/downloads/clients" target="_blank" class="download-btn">
                                📥 ดูแอปสำหรับ TV
                            </a>
                        </div>
                    </div>

                    <!-- Web Browser -->
                    <div class="device-card">
                        <div class="device-icon">🌐</div>
                        <div class="device-title">เว็บเบราว์เซอร์</div>
                        <ol class="device-steps">
                            <li>เปิดเว็บเบราว์เซอร์ (Chrome, Firefox, Safari)</li>
                            <li>ไปที่: <code><?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?></code></li>
                            <li>กรอก Username และ Password</li>
                            <li>คลิก "Sign In"</li>
                            <li>รับชมได้ทันทีผ่านเบราว์เซอร์!</li>
                        </ol>
                        <div class="download-links">
                            <a href="<?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?>" target="_blank" class="download-btn">
                                🌐 เปิด Jellyfin Web
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Troubleshooting -->
            <div class="troubleshooting fade-in-scroll">
                <h3>🔧 แก้ไขปัญหาที่พบบ่อย</h3>
                
                <div class="faq-item">
                    <div class="faq-question">Q: เชื่อมต่อเซิร์ฟเวอร์ไม่ได้</div>
                    <div class="faq-answer">
                        A: ตรวจสอบ URL ให้ถูกต้อง และแน่ใจว่าเชื่อมต่ออินเทอร์เน็ตแล้ว หากยังไม่ได้ ลองใช้ VPN หรือเปลี่ยน DNS
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Q: ล็อกอินไม่ได้</div>
                    <div class="faq-answer">
                        A: ใช้ Username และ Password เดียวกับที่ใช้ล็อกอินเว็บไซต์นี้ หากลืมรหัสผ่าน สามารถรีเซ็ตได้ที่หน้าโปรไฟล์
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Q: เนื้อหาเล่นไม่ได้หรือกระตุก</div>
                    <div class="faq-answer">
                        A: ลองลดคุณภาพในการตั้งค่า หรือตรวจสอบความเร็วอินเทอร์เน็ต แนะนำให้ใช้ WiFi แทน 4G/5G
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Q: ใช้งานได้กี่อุปกรณ์พร้อมกัน?</div>
                    <div class="faq-answer">
                        A: ขึ้นอยู่กับแพ็คเกจที่ซื้อ แพ็คเกจปกติใช้ได้ 1 อุปกรณ์ แพ็คเกจ 150 บาทใช้ได้ 2 อุปกรณ์พร้อมกัน
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Q: ต้องการความช่วยเหลือเพิ่มเติม</div>
                    <div class="faq-answer">
                        A: เข้าร่วม LINE กลุ่มด้านบน เพื่อรับการสนับสนุนและแจ้งปัญหา หรือติดต่อ Admin ผ่านหน้าโปรไฟล์
                    </div>
                </div>
            </div>

            <!-- Quick Download Links -->
            <div class="fade-in-scroll" style="text-align: center; margin: 3rem 0;">
                <h3 style="color: #ffc107; margin-bottom: 2rem;">📥 ดาวน์โหลดแอป Jellyfin</h3>
                <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 1rem;">
                    <a href="https://play.google.com/store/apps/details?id=org.jellyfin.mobile" target="_blank" class="download-btn" style="font-size: 1.1rem; padding: 1rem 2rem;">
                        📱 Android
                    </a>
                    <a href="https://apps.apple.com/app/jellyfin-mobile/id1480192618" target="_blank" class="download-btn" style="font-size: 1.1rem; padding: 1rem 2rem;">
                        🍎 iOS
                    </a>
                    <a href="https://jellyfin.org/downloads/clients" target="_blank" class="download-btn" style="font-size: 1.1rem; padding: 1rem 2rem;">
                        💻 Desktop
                    </a>
                    <a href="<?php echo htmlspecialchars(JELLYFIN_SERVER_URL ?? 'http://your-server.com:8096'); ?>" target="_blank" class="download-btn" style="font-size: 1.1rem; padding: 1rem 2rem;">
                        🌐 Web Player
                    </a>
                </div>
            </div>

            <!-- Additional Tips -->
            <div class="server-info fade-in-scroll">
                <h3>💡 เทิปการใช้งาน</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                    <div style="background: rgba(40, 167, 69, 0.2); border: 1px solid #28a745; padding: 1rem; border-radius: 8px;">
                        <h4 style="color: #28a745; margin-top: 0;">🚀 เพื่อประสิทธิภาพที่ดีที่สุด</h4>
                        <ul style="color: #ccc; margin: 0; padding-left: 1.5rem;">
                            <li>ใช้ WiFi แทน 4G/5G เมื่อเป็นไปได้</li>
                            <li>ปิดแอปอื่นที่ไม่จำเป็นขณะดูหนัง</li>
                            <li>ลดคุณภาพเนื้อหาหากอินเทอร์เน็ตช้า</li>
                        </ul>
                    </div>
                    <div style="background: rgba(255, 193, 7, 0.2); border: 1px solid #ffc107; padding: 1rem; border-radius: 8px;">
                        <h4 style="color: #ffc107; margin-top: 0;">⚠️ ข้อควรระวัง</h4>
                        <ul style="color: #ccc; margin: 0; padding-left: 1.5rem;">
                            <li>อย่าแชร์ Username/Password ให้ผู้อื่น</li>
                            <li>ล็อกเอาท์เมื่อใช้งานเสร็จบนอุปกรณ์สาธารณะ</li>
                            <li>ตรวจสอบจำนวนอุปกรณ์ที่เชื่อมต่อ</li>
                        </ul>
                    </div>
                    <div style="background: rgba(0, 195, 0, 0.2); border: 1px solid #00c300; padding: 1rem; border-radius: 8px;">
                        <h4 style="color: #00c300; margin-top: 0;">💬 การสนับสนุน</h4>
                        <ul style="color: #ccc; margin: 0; padding-left: 1.5rem;">
                            <li>เข้าร่วม LINE กลุ่มเพื่อรับข่าวสารและความช่วยเหลือ</li>
                            <li>แจ้งปัญหาหรือข้อเสนอแนะผ่าน LINE กลุ่ม</li>
                            <li>รับการแจ้งเตือนเมื่อมีอัปเดตหรือปัญหาเซิร์ฟเวอร์</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>

    <script>
        // Download LINE QR Code function
        function downloadLineQR() {
            const qrImage = document.getElementById('line-qr-image');
            if (!qrImage || !qrImage.src || qrImage.style.display === 'none') {
                alert('❌ ไม่พบ QR Code ที่จะดาวน์โหลด\nกรุณาติดต่อ Admin เพื่อขอ QR Code');
                return;
            }

            // Check if image is loaded
            if (!qrImage.complete || qrImage.naturalHeight === 0) {
                alert('❌ QR Code ยังโหลดไม่เสร็จ\nกรุณารอสักครู่แล้วลองใหม่');
                return;
            }

            try {
                // Create canvas to convert image
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = qrImage.naturalWidth || 200;
                canvas.height = qrImage.naturalHeight || 200;

                // Draw image on canvas
                ctx.drawImage(qrImage, 0, 0);

                // Convert to data URL
                const dataURL = canvas.toDataURL('image/png');

                // Try to download
                if (downloadWithDataURL(dataURL)) {
                    return; // Success
                }
            } catch (error) {
                console.error('Canvas download failed:', error);
            }

            // Fallback: open image in new window
            openImageForSave(qrImage.src);
        }

        function downloadWithDataURL(dataURL) {
            try {
                const a = document.createElement('a');
                a.href = dataURL;
                a.download = 'line-qr-code.png';
                a.style.display = 'none';

                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                return true;
            } catch (error) {
                console.error('Data URL download failed:', error);
                return false;
            }
        }

        function openImageForSave(imageSrc) {
            const newWindow = window.open('', '_blank');
            if (newWindow) {
                newWindow.document.write(`
                    <html>
                        <head>
                            <title>LINE QR Code - บันทึกรูปภาพ</title>
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <style>
                                body {
                                    margin: 0;
                                    padding: 20px;
                                    background: #00c300;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    justify-content: center;
                                    min-height: 100vh;
                                    font-family: Arial, sans-serif;
                                }
                                .instruction {
                                    color: #fff;
                                    text-align: center;
                                    margin-bottom: 20px;
                                    font-size: 18px;
                                    background: rgba(255, 255, 255, 0.2);
                                    padding: 20px;
                                    border-radius: 10px;
                                    border: 2px solid #fff;
                                    max-width: 90%;
                                    animation: pulse 2s infinite;
                                }
                                img {
                                    max-width: 90%;
                                    max-height: 70vh;
                                    border: 4px solid #fff;
                                    border-radius: 10px;
                                    background: white;
                                    padding: 10px;
                                }
                                .close-btn {
                                    margin-top: 20px;
                                    padding: 10px 20px;
                                    background: #fff;
                                    color: #00c300;
                                    border: none;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    font-weight: bold;
                                    font-size: 16px;
                                }
                                @keyframes pulse {
                                    0% { transform: scale(1); }
                                    50% { transform: scale(1.05); }
                                    100% { transform: scale(1); }
                                }
                            </style>
                        </head>
                        <body>
                            <div class="instruction">
                                📱 <strong>วิธีบันทึก QR Code LINE:</strong><br><br>
                                <strong>📲 Android:</strong> กดค้างที่รูป → "บันทึกรูปภาพ"<br>
                                <strong>🍎 iPhone:</strong> กดค้างที่รูป → "บันทึกลงในรูปภาพ"<br><br>
                                💬 สแกนด้วยแอป LINE เพื่อเข้าร่วมกลุ่ม
                            </div>
                            <img src="${imageSrc}" alt="LINE QR Code">
                            <button class="close-btn" onclick="window.close()">ปิดหน้าต่าง</button>
                        </body>
                    </html>
                `);
                newWindow.document.close();
            } else {
                alert('❌ ไม่สามารถเปิดหน้าต่างใหม่ได้\nกรุณาอนุญาต popup ในเบราว์เซอร์');
            }
        }

        // Check if LINE QR image loads successfully
        document.addEventListener('DOMContentLoaded', function() {
            const lineQRImage = document.getElementById('line-qr-image');
            if (lineQRImage) {
                lineQRImage.addEventListener('load', function() {
                    console.log('LINE QR Code loaded successfully');
                });

                lineQRImage.addEventListener('error', function() {
                    console.error('Failed to load LINE QR Code from:', this.src);
                    // Show fallback
                    this.style.display = 'none';
                    document.getElementById('line-qr-fallback').style.display = 'flex';
                });
            }
        });
    </script>
</body>
</html>
