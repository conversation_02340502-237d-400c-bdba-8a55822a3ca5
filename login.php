<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: /dashboard');
    exit();
}

$errors = [];

// Handle form submission
if ($_POST) {
    $username_or_phone = sanitize_input($_POST['username_or_phone']);
    $password = $_POST['password'];
    $remember_me = isset($_POST['remember_me']);
    
    // Validation
    if (empty($username_or_phone)) {
        $errors[] = 'Username หรือเบอร์โทรศัพท์จำเป็นต้องใส่';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    }
    
    // Authenticate user
    if (empty($errors)) {
        $stmt = $pdo->prepare("
            SELECT id, username, phone, password_hash, is_admin, is_active, email_verified, last_login
            FROM users
            WHERE (username = ? OR phone = ?) AND is_active = 1
        ");
        $stmt->execute([$username_or_phone, $username_or_phone]);
        $user = $stmt->fetch();
        
        if ($user && verify_password($password, $user['password_hash'])) {
            // Check if email verification is required
            $stmt = $pdo->prepare("SELECT config_value FROM server_config WHERE config_key = 'email_verification_required'");
            $stmt->execute();
            $email_verification_required = $stmt->fetchColumn();
            
            if ($email_verification_required && !$user['email_verified']) {
                $errors[] = 'Please verify your email address before logging in.';
            } else {
                // Successful login
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['is_admin'] = (bool)$user['is_admin'];
                
                // Update last login
                $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                // Create session token if remember me is checked
                if ($remember_me) {
                    $session_token = generate_random_string(64);
                    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user['id'], 
                        $session_token, 
                        $_SERVER['REMOTE_ADDR'] ?? '', 
                        $_SERVER['HTTP_USER_AGENT'] ?? '', 
                        $expires_at
                    ]);
                    
                    // Set cookie
                    setcookie('remember_token', $session_token, strtotime('+30 days'), '/', '', false, true);
                }
                
                // Log activity
                log_activity($user['id'], 'user_login', 'User logged in from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
                
                // Redirect to dashboard or intended page
                $redirect = $_GET['redirect'] ?? '/dashboard';
                header('Location: ' . $redirect);
                exit();
            }
        } else {
            $errors[] = 'Invalid username/email or password.';
            
            // Log failed login attempt
            if ($user) {
                log_activity($user['id'], 'failed_login', 'Failed login attempt from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
            }
        }
    }
}

// Check for remember me cookie
if (!is_logged_in() && isset($_COOKIE['remember_token'])) {
    $stmt = $pdo->prepare("
        SELECT us.user_id, u.username, u.is_admin 
        FROM user_sessions us 
        JOIN users u ON us.user_id = u.id 
        WHERE us.session_token = ? AND us.expires_at > NOW() AND u.is_active = 1
    ");
    $stmt->execute([$_COOKIE['remember_token']]);
    $session = $stmt->fetch();
    
    if ($session) {
        // Auto login
        $_SESSION['user_id'] = $session['user_id'];
        $_SESSION['username'] = $session['username'];
        $_SESSION['is_admin'] = (bool)$session['is_admin'];
        
        // Update session activity
        $stmt = $pdo->prepare("UPDATE user_sessions SET last_activity = NOW() WHERE session_token = ?");
        $stmt->execute([$_COOKIE['remember_token']]);
        
        header('Location: /dashboard');
        exit();
    } else {
        // Invalid or expired token, remove cookie
        setcookie('remember_token', '', time() - 3600, '/');
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
        <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/" class="nav-link">หน้าแรก</a>
                <a href="/register" class="nav-link">สมัครสมาชิก</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="form-container fade-in-up">
                <h2 style="text-align: center; margin-bottom: 2rem;">เข้าสู่ระบบ</h2>
                
                <?php display_flash_message(); ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="flash-message flash-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="loginForm">
                    <div class="form-group">
                        <label for="username_or_phone">Username หรือเบอร์โทรศัพท์ *</label>
                        <input type="text" id="username_or_phone" name="username_or_phone" class="form-control"
                               value="<?php echo htmlspecialchars($_POST['username_or_phone'] ?? ''); ?>"
                               required placeholder="ใส่ username หรือเบอร์โทรศัพท์">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">รหัสผ่าน *</label>
                        <input type="password" id="password" name="password" class="form-control"
                               required placeholder="ใส่รหัสผ่าน">
                    </div>
                    
                    <div class="form-group" style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" id="remember_me" name="remember_me" 
                               <?php echo isset($_POST['remember_me']) ? 'checked' : ''; ?>>
                        <label for="remember_me" style="margin: 0; cursor: pointer;">จดจำการเข้าสู่ระบบ 30 วัน</label>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary noir-button" style="width: 100%;">
                            เข้าสู่ระบบ
                        </button>
                    </div>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <p><a href="forgot-password.php" style="color: #ccc;">ลืมรหัสผ่าน?</a></p>
                        <p>ยังไม่มีบัญชี? <a href="/register" style="color: #ccc;">สมัครสมาชิก</a></p>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
