<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🔍 Check Payment Transactions Schema</h2>";

try {
    // Check table structure
    $stmt = $pdo->query("DESCRIBE payment_transactions");
    $columns = $stmt->fetchAll();
    
    echo "<h3>📋 Current Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasVerifiedAt = false;
    $hasAdminNotes = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'verified_at') $hasVerifiedAt = true;
        if ($column['Field'] === 'admin_notes') $hasAdminNotes = true;
    }
    echo "</table>";
    
    echo "<h3>🔍 Missing Columns Check:</h3>";
    echo "<ul>";
    echo "<li>verified_at: " . ($hasVerifiedAt ? '✅ Exists' : '❌ Missing') . "</li>";
    echo "<li>admin_notes: " . ($hasAdminNotes ? '✅ Exists' : '❌ Missing') . "</li>";
    echo "</ul>";
    
    if (!$hasVerifiedAt || !$hasAdminNotes) {
        echo "<h3>🔧 Add Missing Columns:</h3>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='add_columns' value='1'>";
        echo "<button type='submit' style='background: green; color: white; padding: 10px; border: none; cursor: pointer;'>Add Missing Columns</button>";
        echo "</form>";
    }
    
    echo "<h3>📊 Sample Data:</h3>";
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        ORDER BY pt.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Method</th><th>Slip</th><th>Created</th>";
        if ($hasVerifiedAt) echo "<th>Verified At</th>";
        echo "</tr>";
        
        foreach ($payments as $payment) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . ($payment['payment_method'] ?? 'NULL') . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅' : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            if ($hasVerifiedAt) echo "<td>" . ($payment['verified_at'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle add columns
if ($_POST && isset($_POST['add_columns'])) {
    try {
        echo "<h3>🔧 Adding Missing Columns:</h3>";
        
        // Add verified_at column
        $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'verified_at'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN verified_at TIMESTAMP NULL AFTER updated_at");
            echo "<p>✅ Added verified_at column</p>";
        }
        
        // Add admin_notes column
        $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'admin_notes'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN admin_notes TEXT NULL AFTER verified_at");
            echo "<p>✅ Added admin_notes column</p>";
        }
        
        echo "<p><strong>✅ Schema updated successfully!</strong></p>";
        echo "<p><a href='?'>Refresh Page</a></p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error adding columns: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
?>
