<?php
/**
 * API Endpoint for User Expiration Check
 * สำหรับ AutoPaymentChecker Production Mode
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Include required files - use production config
    if (file_exists('../config/database_production.php')) {
        require_once '../config/database_production.php';
    } else {
        require_once '../includes/config.php';
    }
    require_once '../includes/jellyfin_api.php';
    
    // Log the check attempt
    error_log("[" . date('Y-m-d H:i:s') . "] User expiration check started via API endpoint");
    
    // Get expired users who are still enabled
    $stmt = $pdo->prepare("
        SELECT id, username, expiry_date, jellyfin_user_id 
        FROM users 
        WHERE expiry_date < NOW() 
        AND jellyfin_user_id IS NOT NULL
        AND is_admin = 0
        ORDER BY expiry_date ASC
    ");
    $stmt->execute();
    $expired_users = $stmt->fetchAll();
    
    $disabled_count = 0;
    $failed_count = 0;
    $processed_users = [];
    
    if (empty($expired_users)) {
        echo json_encode([
            'success' => true,
            'message' => 'No expired users found',
            'disabled_count' => 0,
            'failed_count' => 0,
            'processed_users' => []
        ]);
        exit;
    }
    
    // Initialize Jellyfin API
    $jellyfin = new JellyfinAPI();
    
    // Process each expired user
    foreach ($expired_users as $user) {
        try {
            // Check if user is currently enabled in Jellyfin
            $jellyfinUser = $jellyfin->getUserById($user['jellyfin_user_id']);
            
            if ($jellyfinUser && !$jellyfinUser['Policy']['IsDisabled']) {
                // User is enabled but expired - disable them
                $result = $jellyfin->disableUser($user['jellyfin_user_id']);
                
                if ($result) {
                    $disabled_count++;
                    $processed_users[] = [
                        'user_id' => $user['id'],
                        'username' => $user['username'],
                        'expiry_date' => $user['expiry_date'],
                        'status' => 'disabled',
                        'message' => 'User disabled due to expiration'
                    ];
                    
                    error_log("[" . date('Y-m-d H:i:s') . "] Disabled expired user: {$user['username']} (expired: {$user['expiry_date']})");
                } else {
                    $failed_count++;
                    $processed_users[] = [
                        'user_id' => $user['id'],
                        'username' => $user['username'],
                        'expiry_date' => $user['expiry_date'],
                        'status' => 'failed',
                        'message' => 'Failed to disable user in Jellyfin'
                    ];
                    
                    error_log("[" . date('Y-m-d H:i:s') . "] Failed to disable expired user: {$user['username']}");
                }
            } else {
                // User is already disabled or not found
                $processed_users[] = [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'expiry_date' => $user['expiry_date'],
                    'status' => 'already_disabled',
                    'message' => 'User already disabled or not found in Jellyfin'
                ];
            }
            
        } catch (Exception $e) {
            $failed_count++;
            $processed_users[] = [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'expiry_date' => $user['expiry_date'],
                'status' => 'error',
                'message' => 'Error: ' . $e->getMessage()
            ];
            
            error_log("[" . date('Y-m-d H:i:s') . "] Error processing expired user {$user['username']}: " . $e->getMessage());
        }
    }
    
    $message = "Expiration check completed: {$disabled_count} users disabled, {$failed_count} failed";
    error_log("[" . date('Y-m-d H:i:s') . "] " . $message);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'disabled_count' => $disabled_count,
        'failed_count' => $failed_count,
        'total_expired' => count($expired_users),
        'processed_users' => $processed_users
    ]);
    
} catch (Exception $e) {
    error_log("[" . date('Y-m-d H:i:s') . "] Expiration check error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Expiration check failed: ' . $e->getMessage(),
        'disabled_count' => 0,
        'failed_count' => 0
    ]);
}
?>
