<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle manual verification run
if ($_POST && isset($_POST['run_manual_check'])) {
    header('Location: ../manual_check_payments.php');
    exit;
}

// Handle scheduled task setup
if ($_POST && isset($_POST['setup_scheduled_task'])) {
    $output = [];
    $return_var = 0;
    
    // Find PHP executable
    $php_exe = null;
    foreach (glob('C:\\laragon\\bin\\php\\php-*\\php.exe') as $path) {
        if (file_exists($path)) {
            $php_exe = $path;
            break;
        }
    }
    
    if ($php_exe) {
        $script_path = realpath('../paynoi-transactions.php');
        $command = "schtasks /create /tn \"PayNoiTransactionsSync\" /tr \"\\\"{$php_exe}\\\" \\\"{$script_path}\\\"\" /sc minute /mo 1 /f";
        
        exec($command, $output, $return_var);
        
        if ($return_var === 0) {
            $message = "Scheduled task created successfully! The system will now check for payments every minute.";
            $message_type = 'success';
        } else {
            $message = "Failed to create scheduled task. Please run as Administrator. Error: " . implode("\n", $output);
            $message_type = 'error';
        }
    } else {
        $message = "PHP executable not found. Please check your Laragon installation.";
        $message_type = 'error';
    }
}

// Check current scheduled task status
$task_status = 'Not Found';
$task_next_run = 'N/A';
$task_last_run = 'N/A';

$output = [];
exec('schtasks /query /tn "PayNoiTransactionsSync" /fo LIST 2>nul', $output, $return_var);
if ($return_var === 0) {
    $task_status = 'Active';
    foreach ($output as $line) {
        if (strpos($line, 'Next Run Time:') !== false) {
            $task_next_run = trim(substr($line, strpos($line, ':') + 1));
        }
        if (strpos($line, 'Last Run Time:') !== false) {
            $task_last_run = trim(substr($line, strpos($line, ':') + 1));
        }
    }
}

// Get recent payment statistics
$stats = [
    'pending_24h' => 0,
    'verified_today' => 0,
    'verified_week' => 0
];

// Check if payment_transactions table exists and get statistics
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'payment_transactions'");
    if ($stmt->rowCount() > 0) {
        // Pending payments
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM payment_transactions WHERE status = 'pending'");
        $stats['pending_24h'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Completed payments today
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM payment_transactions WHERE status = 'completed' AND paid_at >= CURDATE()");
        $stats['verified_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Total completed this week
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM payment_transactions WHERE status = 'completed' AND paid_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stats['verified_week'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    }
} catch (Exception $e) {
    // If payment_transactions table doesn't exist, use default values
    error_log("Error fetching payment statistics: " . $e->getMessage());
}

// Recent verification activity - check if payment_transactions table exists first
$recent_verifications = [];
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'payment_transactions'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->prepare("
            SELECT pt.*, u.username, pt.paid_at as verified_at
            FROM payment_transactions pt
            LEFT JOIN users u ON pt.user_id = u.id
            WHERE pt.status = 'completed'
            AND pt.paid_at IS NOT NULL
            ORDER BY pt.paid_at DESC
            LIMIT 10
        ");
        $stmt->execute();
        $recent_verifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    // If payment_transactions table doesn't exist or has issues, continue without recent verifications
    error_log("Error fetching recent verifications: " . $e->getMessage());
}

// Include header - create simple header since includes/header.php doesn't exist
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบเช็คสลิปอัตโนมัติ - Jellyfin Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="film-grain-overlay"></div>

    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up"><a href="../index.php" style="color: inherit; text-decoration: none;">Jellyfin by James</a></h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="auto-verification.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">เช็คสลิปอัตโนมัติ</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h2>ระบบเช็คสลิปอัตโนมัติ</h2>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : ($message_type === 'error' ? 'danger' : 'info'); ?>">
                    <?php echo nl2br(htmlspecialchars($message)); ?>
                </div>
            <?php endif; ?>
            
            <!-- System Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>สถานะระบบ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Scheduled Task:</strong></td>
                                    <td>
                                        <span class="badge badge-<?php echo $task_status === 'Active' ? 'success' : 'danger'; ?>">
                                            <?php echo $task_status; ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Last Run:</strong></td>
                                    <td><?php echo htmlspecialchars($task_last_run); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Next Run:</strong></td>
                                    <td><?php echo htmlspecialchars($task_next_run); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Pending:</strong></td>
                                    <td><span class="badge badge-warning"><?php echo $stats['pending_24h']; ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Verified Today:</strong></td>
                                    <td><span class="badge badge-success"><?php echo $stats['verified_today']; ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Verified This Week:</strong></td>
                                    <td><span class="badge badge-info"><?php echo $stats['verified_week']; ?></span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>การจัดการ</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="d-inline">
                        <button type="submit" name="run_manual_check" class="btn btn-primary">
                            <i class="fas fa-play"></i> เช็คสลิปแบบ Manual
                        </button>
                    </form>
                    
                    <?php if ($task_status !== 'Active'): ?>
                    <form method="post" class="d-inline ml-2">
                        <button type="submit" name="setup_scheduled_task" class="btn btn-success" 
                                onclick="return confirm('ต้องการตั้งค่า Scheduled Task สำหรับเช็คสลิปอัตโนมัติหรือไม่?')">
                            <i class="fas fa-cog"></i> ตั้งค่า Auto Check
                        </button>
                    </form>
                    <?php else: ?>
                    <button type="button" class="btn btn-success ml-2" disabled>
                        <i class="fas fa-check"></i> Auto Check เปิดใช้งานแล้ว
                    </button>
                    <?php endif; ?>
                    
                    <a href="../test_paynoi.php" class="btn btn-info ml-2" target="_blank">
                        <i class="fas fa-test-tube"></i> ทดสอบ PayNoi API
                    </a>
                </div>
            </div>
            
            <!-- Recent Verifications -->
            <?php if (!empty($recent_verifications)): ?>
            <div class="card">
                <div class="card-header">
                    <h5>การตรวจสอบล่าสุด</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>ผู้ใช้</th>
                                    <th>จำนวนเงิน</th>
                                    <th>วันที่ตรวจสอบ</th>
                                    <th>Transaction ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_verifications as $verification): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($verification['id']); ?></td>
                                    <td><?php echo htmlspecialchars($verification['username'] ?? 'N/A'); ?></td>
                                    <td><?php echo number_format($verification['amount'], 2); ?> THB</td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($verification['verified_at'])); ?></td>
                                    <td>
                                        <small><?php echo htmlspecialchars($verification['transaction_id'] ?? 'N/A'); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Instructions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5>คำแนะนำ</h5>
                </div>
                <div class="card-body">
                    <h6>การทำงานของระบบ:</h6>
                    <ol>
                        <li>ผู้ใช้อัปโหลดสลิปการโอนเงิน</li>
                        <li>ระบบตั้งสถานะเป็น "pending"</li>
                        <li>Scheduled Task จะเช็คทุกนาทีเพื่อจับคู่กับ PayNoi API</li>
                        <li>หากพบธุรกรรมที่ตรงกัน จะอนุมัติอัตโนมัติ</li>
                        <li>เปิดใช้งาน Jellyfin และเพิ่มวันใช้งาน</li>
                    </ol>
                    
                    <h6 class="mt-3">เงื่อนไขการจับคู่:</h6>
                    <ul>
                        <li>จำนวนเงินต้องตรงกัน (ผิดเพียง 0.01 บาท)</li>
                        <li>เวลาต้องอยู่ภายใน 2 ชั่วโมง</li>
                        <li>ต้องเป็นธุรกรรมเงินเข้า</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

    <script src="../assets/js/script.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
