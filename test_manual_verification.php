<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test Manual Verification System</h2>";

try {
    echo "<h3>🔍 Step 1: Check Schema</h3>";
    
    // Check if required columns exist
    $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'verified_at'");
    $hasVerifiedAt = $stmt->rowCount() > 0;
    
    $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'admin_notes'");
    $hasAdminNotes = $stmt->rowCount() > 0;
    
    echo "<p>verified_at column: " . ($hasVerifiedAt ? '✅' : '❌') . "</p>";
    echo "<p>admin_notes column: " . ($hasAdminNotes ? '✅' : '❌') . "</p>";
    
    if (!$hasVerifiedAt || !$hasAdminNotes) {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='fix_schema' value='1'>";
        echo "<button type='submit' style='background: red; color: white; padding: 10px; border: none; cursor: pointer;'>Fix Schema</button>";
        echo "</form>";
    }
    
    echo "<h3>🔍 Step 2: Check Manual Payments</h3>";
    
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $manualPayments = $stmt->fetchAll();
    
    echo "<p>Manual payments found: " . count($manualPayments) . "</p>";
    
    if ($manualPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Slip</th><th>File Exists</th><th>Created</th></tr>";
        
        foreach ($manualPayments as $payment) {
            // Check file path
            $slipImage = $payment['slip_image'];
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $slipImage = 'uploads/slips/' . $slipImage;
            }
            
            $fileExists = file_exists($slipImage);
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . basename($payment['slip_image']) . "</td>";
            echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='POST'>";
        echo "<input type='hidden' name='test_verification' value='1'>";
        echo "<button type='submit' style='background: blue; color: white; padding: 10px; border: none; cursor: pointer;'>Test Manual Verification</button>";
        echo "</form>";
    }
    
    echo "<h3>📊 Step 3: Verification Statistics</h3>";
    
    $stats = [];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'pending' AND payment_method = 'manual'");
    $stmt->execute();
    $stats['pending_manual'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'verified'");
    $stmt->execute();
    $stats['verified_total'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'verified' AND verified_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $stats['verified_today'] = $stmt->fetchColumn();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Pending Manual</td><td style='background: yellow;'>{$stats['pending_manual']}</td></tr>";
    echo "<tr><td>Verified Total</td><td style='background: lightgreen;'>{$stats['verified_total']}</td></tr>";
    echo "<tr><td>Verified Today</td><td style='background: lightblue;'>{$stats['verified_today']}</td></tr>";
    echo "</table>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['fix_schema'])) {
        echo "<h3>🔧 Fixing Schema:</h3>";
        try {
            // Add verified_at column
            $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'verified_at'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN verified_at TIMESTAMP NULL AFTER updated_at");
                echo "<p>✅ Added verified_at column</p>";
            }
            
            // Add admin_notes column
            $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'admin_notes'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE payment_transactions ADD COLUMN admin_notes TEXT NULL AFTER verified_at");
                echo "<p>✅ Added admin_notes column</p>";
            }
            
            // Update status enum
            $pdo->exec("ALTER TABLE payment_transactions MODIFY COLUMN status ENUM('pending', 'pending_verification', 'completed', 'failed', 'cancelled', 'manual_review', 'verified') DEFAULT 'pending'");
            echo "<p>✅ Updated status enum to include 'verified'</p>";
            
            echo "<p><strong>✅ Schema fixed successfully!</strong></p>";
            echo "<p><a href='?'>Refresh Page</a></p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Error fixing schema: " . $e->getMessage() . "</p>";
        }
    }
    
    if (isset($_POST['test_verification'])) {
        echo "<h3>🧪 Testing Manual Verification:</h3>";
        try {
            // Get one manual payment to test
            $stmt = $pdo->prepare("
                SELECT pt.*, u.username
                FROM payment_transactions pt
                JOIN users u ON pt.user_id = u.id
                WHERE pt.status = 'pending'
                AND pt.payment_method = 'manual'
                AND pt.slip_image IS NOT NULL
                AND pt.slip_image != ''
                ORDER BY pt.created_at ASC
                LIMIT 1
            ");
            $stmt->execute();
            $payment = $stmt->fetch();
            
            if ($payment) {
                echo "<p>Testing payment ID: {$payment['id']} for user: {$payment['username']}</p>";
                
                // Check slip file with path fix
                $slipImage = $payment['slip_image'];
                if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                    $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
                } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                    $slipImage = 'uploads/slips/' . $slipImage;
                }
                
                if (!file_exists($slipImage)) {
                    echo "<p>❌ Slip file not found: {$slipImage}</p>";
                } else {
                    $fileSize = filesize($slipImage);
                    echo "<p>✅ Slip file found: {$slipImage} ({$fileSize} bytes)</p>";
                    
                    // Check auto-verification criteria
                    $isValidSize = $fileSize > 10000 && $fileSize < 10000000;
                    $isSmallAmount = $payment['amount'] <= 100;
                    
                    echo "<p>Valid size: " . ($isValidSize ? '✅' : '❌') . "</p>";
                    echo "<p>Small amount (≤100 THB): " . ($isSmallAmount ? '✅' : '❌') . "</p>";
                    
                    if ($isSmallAmount && $isValidSize) {
                        echo "<p>✅ <strong>Auto-verification criteria met!</strong></p>";
                        
                        // Test the verification update
                        $pdo->beginTransaction();
                        
                        $updateStmt = $pdo->prepare("
                            UPDATE payment_transactions
                            SET status = 'verified',
                                verified_at = NOW()
                            WHERE id = ?
                        ");
                        $result = $updateStmt->execute([$payment['id']]);
                        
                        if ($result) {
                            echo "<p>✅ Payment verified successfully!</p>";
                            echo "<p>Affected rows: " . $updateStmt->rowCount() . "</p>";
                        } else {
                            echo "<p>❌ Failed to verify payment</p>";
                        }
                        
                        $pdo->commit();
                        
                    } else {
                        echo "<p>❌ Auto-verification criteria not met</p>";
                        echo "<p>This payment would need manual admin review</p>";
                    }
                }
            } else {
                echo "<p>❌ No manual payments found for testing</p>";
            }
            
        } catch (Exception $e) {
            if (isset($pdo)) $pdo->rollback();
            echo "<p>❌ Test error: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/check_payment_schema.php'>🔍 Check Payment Schema</a></p>";
?>
