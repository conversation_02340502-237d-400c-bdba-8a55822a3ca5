<?php

class ReferralSystem {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Generate unique referral code for user
     */
    public function generateReferralCode($username) {
        $attempts = 0;
        do {
            $clean = preg_replace('/[^a-zA-Z0-9]/', '', $username);
            $clean = strtoupper(substr($clean, 0, 4));
            $random = strtoupper(substr(md5(uniqid() . $attempts), 0, 4));
            $code = $clean . $random;
            
            // Check if code exists
            $stmt = $this->pdo->prepare("SELECT id FROM users WHERE referral_code = ?");
            $stmt->execute([$code]);
            $exists = $stmt->rowCount() > 0;
            
            $attempts++;
        } while ($exists && $attempts < 10);
        
        return $code;
    }
    
    /**
     * Set referral code for user
     */
    public function setReferralCode($userId, $referralCode) {
        $stmt = $this->pdo->prepare("UPDATE users SET referral_code = ? WHERE id = ?");
        return $stmt->execute([$referralCode, $userId]);
    }
    
    /**
     * Find user by referral code
     */
    public function findUserByReferralCode($referralCode) {
        $stmt = $this->pdo->prepare("SELECT id, username, email FROM users WHERE referral_code = ?");
        $stmt->execute([$referralCode]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Set who referred this user
     */
    public function setReferredBy($userId, $referrerId) {
        $stmt = $this->pdo->prepare("UPDATE users SET referred_by = ? WHERE id = ?");
        return $stmt->execute([$referrerId, $userId]);
    }
    
    /**
     * Award points to referrer when referred user makes a purchase
     */
    public function awardReferralPoints($referredUserId, $paymentAmount = null, $paymentId = null) {
        try {
            $this->pdo->beginTransaction();

            // Get referrer info
            $stmt = $this->pdo->prepare("
                SELECT u.referred_by, r.username as referrer_username
                FROM users u
                LEFT JOIN users r ON u.referred_by = r.id
                WHERE u.id = ? AND u.referred_by IS NOT NULL
            ");
            $stmt->execute([$referredUserId]);
            $referralInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$referralInfo || !$referralInfo['referred_by']) {
                $this->pdo->rollBack();
                return false; // No referrer
            }

            $referrerId = $referralInfo['referred_by'];
            $pointsToAward = 15.00; // 15 points = 15 THB

            // Check if points already awarded for this payment
            if ($paymentId) {
                $stmt = $this->pdo->prepare("
                    SELECT id FROM referral_transactions
                    WHERE related_payment_id = ? AND transaction_type = 'purchase'
                ");
                $stmt->execute([$paymentId]);
                if ($stmt->rowCount() > 0) {
                    $this->pdo->rollBack();
                    return false; // Already awarded
                }
            }

            // Add points to referrer
            $stmt = $this->pdo->prepare("UPDATE users SET points = points + ? WHERE id = ?");
            $stmt->execute([$pointsToAward, $referrerId]);

            // Log the transaction
            $description = "Referral bonus: Referred user made a purchase";
            if ($paymentAmount) {
                $description .= " (Amount: {$paymentAmount} THB)";
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO referral_transactions
                (referrer_id, referred_id, points_earned, transaction_type, related_payment_id, description)
                VALUES (?, ?, ?, 'purchase', ?, ?)
            ");
            $stmt->execute([$referrerId, $referredUserId, $pointsToAward, $paymentId, $description]);

            $this->pdo->commit();

            error_log("Awarded {$pointsToAward} points to user {$referrerId} for referral of user {$referredUserId}");
            return true;

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Failed to award referral points: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Award points to referrer when referred user makes a purchase (without transaction management)
     * Use this when already inside a transaction
     */
    public function awardReferralPointsInTransaction($referredUserId, $paymentAmount = null, $paymentId = null) {
        try {
            // Get referrer info
            $stmt = $this->pdo->prepare("
                SELECT u.referred_by, r.username as referrer_username
                FROM users u
                LEFT JOIN users r ON u.referred_by = r.id
                WHERE u.id = ? AND u.referred_by IS NOT NULL
            ");
            $stmt->execute([$referredUserId]);
            $referralInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$referralInfo || !$referralInfo['referred_by']) {
                return false; // No referrer
            }

            $referrerId = $referralInfo['referred_by'];
            $pointsToAward = 15.00; // 15 points = 15 THB

            // Check if points already awarded for this payment
            if ($paymentId) {
                $stmt = $this->pdo->prepare("
                    SELECT id FROM referral_transactions
                    WHERE related_payment_id = ? AND transaction_type = 'purchase'
                ");
                $stmt->execute([$paymentId]);
                if ($stmt->rowCount() > 0) {
                    return false; // Already awarded
                }
            }

            // Add points to referrer
            $stmt = $this->pdo->prepare("UPDATE users SET points = points + ? WHERE id = ?");
            $stmt->execute([$pointsToAward, $referrerId]);

            // Log the transaction
            $description = "Referral bonus: Referred user made a purchase";
            if ($paymentAmount) {
                $description .= " (Amount: {$paymentAmount} THB)";
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO referral_transactions
                (referrer_id, referred_id, points_earned, transaction_type, related_payment_id, description)
                VALUES (?, ?, ?, 'purchase', ?, ?)
            ");
            $stmt->execute([$referrerId, $referredUserId, $pointsToAward, $paymentId, $description]);

            error_log("Awarded {$pointsToAward} points to user {$referrerId} for referral of user {$referredUserId}");
            return true;

        } catch (Exception $e) {
            error_log("Failed to award referral points: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's referral statistics
     */
    public function getReferralStats($userId) {
        // Get total points
        $stmt = $this->pdo->prepare("SELECT points, referral_code FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get total referrals
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as total_referrals FROM users WHERE referred_by = ?");
        $stmt->execute([$userId]);
        $totalReferrals = $stmt->fetch(PDO::FETCH_ASSOC)['total_referrals'];
        
        // Get total points earned from referrals
        $stmt = $this->pdo->prepare("
            SELECT SUM(points_earned) as total_earned 
            FROM referral_transactions 
            WHERE referrer_id = ?
        ");
        $stmt->execute([$userId]);
        $totalEarned = $stmt->fetch(PDO::FETCH_ASSOC)['total_earned'] ?? 0;
        
        // Get recent referral transactions
        $stmt = $this->pdo->prepare("
            SELECT rt.*, u.username as referred_username 
            FROM referral_transactions rt 
            LEFT JOIN users u ON rt.referred_id = u.id 
            WHERE rt.referrer_id = ? 
            ORDER BY rt.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        $recentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'current_points' => $user['points'] ?? 0,
            'referral_code' => $user['referral_code'],
            'total_referrals' => $totalReferrals,
            'total_earned' => $totalEarned,
            'recent_transactions' => $recentTransactions
        ];
    }
    
    /**
     * Use points for payment (future feature)
     */
    public function usePoints($userId, $pointsToUse) {
        try {
            $this->pdo->beginTransaction();
            
            // Check if user has enough points
            $stmt = $this->pdo->prepare("SELECT points FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $currentPoints = $stmt->fetch(PDO::FETCH_ASSOC)['points'] ?? 0;
            
            if ($currentPoints < $pointsToUse) {
                $this->pdo->rollBack();
                return false; // Not enough points
            }
            
            // Deduct points
            $stmt = $this->pdo->prepare("UPDATE users SET points = points - ? WHERE id = ?");
            $stmt->execute([$pointsToUse, $userId]);
            
            // Log the transaction
            $stmt = $this->pdo->prepare("
                INSERT INTO referral_transactions 
                (referrer_id, referred_id, points_earned, transaction_type, description) 
                VALUES (?, ?, ?, 'usage', ?)
            ");
            $stmt->execute([$userId, $userId, -$pointsToUse, "Used {$pointsToUse} points for payment"]);
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Failed to use points: " . $e->getMessage());
            return false;
        }
    }
}
?>
