-- Create payments table for PayNoi transaction verification
-- Run this SQL in your jellyfin_registration database

USE jellyfin_registration;

CREATE TABLE IF NOT EXISTS `payments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `affiliate_user_id` int(11) DEFAULT NULL,
    `amount` decimal(10,2) NOT NULL,
    `currency` varchar(3) DEFAULT 'THB',
    `status` enum('pending','verified','failed','cancelled') DEFAULT 'pending',
    `payment_method` varchar(50) DEFAULT 'bank_transfer',
    `transaction_id` varchar(255) DEFAULT NULL,
    `paynoi_data` text DEFAULT NULL,
    `slip_image` varchar(255) DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `verified_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON>ARY KEY (`id`),
    <PERSON><PERSON>Y `idx_user_id` (`user_id`),
    KEY `idx_affiliate_user_id` (`affiliate_user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample pending payments for testing
INSERT INTO `payments` (`user_id`, `amount`, `status`, `created_at`) VALUES
(1, 100.00, 'pending', NOW() - INTERVAL 1 HOUR),
(2, 200.00, 'pending', NOW() - INTERVAL 30 MINUTE),
(3, 50.00, 'pending', NOW() - INTERVAL 15 MINUTE);

-- Show the created table structure
DESCRIBE payments;

-- Show sample data
SELECT * FROM payments WHERE status = 'pending' ORDER BY created_at DESC;
