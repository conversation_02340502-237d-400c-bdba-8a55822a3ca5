# AutoPaymentChecker v2.0 - Portable Edition

## 🎬 Jellyfin by <PERSON> - Auto Payment Checker

### ✨ Features:
- ✅ HTTPS API Integration with embyjames.xyz
- ✅ Real-time PayNoi transaction monitoring
- ✅ Manual payment slip verification
- ✅ User expiration checking
- ✅ Modern GUI with dark theme
- ✅ Comprehensive logging system

### 🚀 How to Use:
1. Double-click `AutoPaymentChecker.exe` to run
2. Click "Start Auto Check" to begin monitoring
3. Monitor logs in the application window
4. Use "Test Connection" to verify API connectivity

### 🔧 Configuration:
- Base URL: https://embyjames.xyz
- Check Interval: 5 seconds (configurable)
- Expiration Check: 60 minutes (configurable)

### 📝 System Requirements:
- Windows 10/11 (64-bit)
- Internet connection
- Access to embyjames.xyz

### 🆘 Troubleshooting:
- If connection fails, check internet connectivity
- Ensure embyjames.xyz is accessible
- Check Windows Firewall settings
- Run as Administrator if needed

### 📞 Support:
For support, contact the Jellyfin by <PERSON> team.

---
© 2024 Jellyfin by <PERSON> with Python & PyInstaller
