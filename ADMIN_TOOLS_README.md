# 🛠️ Jellyfin Admin Tools

## 📖 คำอธิบาย
Jellyfin Admin Tools เป็นแอปพลิเคชัน GUI สำหรับจัดการระบบ Jellyfin Registration System ที่ให้ admin สามารถจัดการผู้ใช้, การชำระเงิน, แพ็กเกจ และระบบ Jellyfin ได้อย่างสะดวก

## ✨ Features

### 🔐 Authentication System
- ระบบ login สำหรับ admin
- รองรับทั้ง online และ offline mode
- Hardcoded admin accounts สำหรับใช้งานแบบ offline

### 📊 Dashboard
- แสดงสถิติระบบแบบ real-time
- Quick action buttons
- Recent activity log
- System status monitoring

### 👥 Users Management
- ดูรายการผู้ใช้ทั้งหมด
- ค้นหาและกรองผู้ใช้
- เพิ่ม/แก้ไข/ลบผู้ใช้
- จัดการ package และ expiration date

### 💰 Payments Management
- ดูรายการการชำระเงินทั้งหมด
- ยืนยัน/ปฏิเสธการชำระเงิน
- Auto check payments ผ่าน PayNoi API
- ดูรายละเอียดการชำระเงินแบบละเอียด

### 📦 Packages Management
- จัดการแพ็กเกจต่างๆ
- เพิ่ม/แก้ไข/ลบแพ็กเกจ
- ตั้งค่าราคาและระยะเวลา
- เปิด/ปิดการใช้งานแพ็กเกจ

### 🎬 Jellyfin Management
- ตรวจสอบสถานะ Jellyfin server
- จัดการ Jellyfin users
- Sync users กับ Jellyfin
- ดู active sessions

### ⚙️ System Tools
- ดูข้อมูลระบบ
- Restart services
- Clear cache
- Backup database
- View system logs

## 🚀 การติดตั้งและใช้งาน

### วิธีที่ 1: ใช้ Executable (แนะนำ)
```bash
# รันไฟล์ .exe โดยตรง
JellyfinAdminTools.exe
```

### วิธีที่ 2: รันจาก Python Source
```bash
# ติดตั้ง dependencies
pip install -r requirements.txt

# รันแอปพลิเคชัน
python AdminTools.py

# หรือใช้ batch file
run_admin_tools.bat
```

### วิธีที่ 3: Build Executable เอง
```bash
# Build executable
build_simple.bat

# ไฟล์ .exe จะอยู่ใน JellyfinAdminTools.exe
```

## 🔑 การ Login

### Database Mode (แนะนำ - เชื่อมต่อฐานข้อมูลโดยตรง)
- ใช้ admin username และ password จากฐานข้อมูล `jellyfin_registration`
- ต้องมี `is_admin = 1` ในฐานข้อมูล
- รองรับ password ที่เข้ารหัสด้วย PHP password_hash()

### API Mode (เชื่อมต่อผ่าน Web API)
- ใช้ admin username และ password จากฐานข้อมูล
- เชื่อมต่อผ่าน `http://localhost/api/login.php`

### Demo Mode (สำหรับทดสอบ)
```
Username: admin
Password: admin123

หรือ

Username: jellyfin_admin
Password: jellyfin123

หรือ

Username: administrator
Password: admin123
```

### 🔧 สร้าง Admin User
หากยังไม่มี admin user ในระบบ:
1. เปิด `http://localhost/create_admin_user.php`
2. สร้าง admin user ใหม่
3. ใช้ username และ password ที่สร้างเพื่อ login

## 📁 โครงสร้างไฟล์

```
├── AdminTools.py              # Main application
├── JellyfinAdminTools.exe     # Compiled executable
├── requirements.txt           # Python dependencies
├── run_admin_tools.bat        # Launcher script
├── build_simple.bat           # Build script
├── api/
│   ├── login.php             # Login API
│   └── admin/
│       ├── users.php         # Users API
│       ├── payments.php      # Payments API
│       └── packages.php      # Packages API
└── ADMIN_TOOLS_README.md     # This file
```

## 🎯 การใช้งาน

### 1. เปิดแอปพลิเคชัน
- Double-click `JellyfinAdminTools.exe`
- หรือรัน `run_admin_tools.bat`

### 2. Login
- ใส่ username และ password
- คลิก "เข้าสู่ระบบ"

### 3. ใช้งาน Features
- เลือกแท็บที่ต้องการ
- ใช้งานเครื่องมือต่างๆ
- ดูข้อมูลและจัดการระบบ

## 🔧 Configuration

### Database Connection
แอปพลิเคชันจะเชื่อมต่อกับฐานข้อมูล MySQL:
```
Host: localhost
User: root
Password: (empty)
Database: jellyfin_registration
Charset: utf8mb4
```

### API Endpoints
แอปพลิเคชันจะเชื่อมต่อกับ:
```
http://localhost/api/login.php
http://localhost/api/admin/users.php
http://localhost/api/admin/payments.php
http://localhost/api/admin/packages.php
http://localhost/api/admin/test_connection.php
```

### Data Priority
ลำดับการดึงข้อมูล:
1. **Database Direct** - เชื่อมต่อฐานข้อมูลโดยตรง (เร็วที่สุด)
2. **Web API** - เชื่อมต่อผ่าน API endpoints
3. **Demo Data** - ข้อมูลตัวอย่างสำหรับทดสอบ

### Demo Data
หากไม่สามารถเชื่อมต่อฐานข้อมูลหรือ API ได้ แอปพลิเคชันจะแสดงข้อมูลตัวอย่าง:
- 5 ผู้ใช้ตัวอย่าง
- 5 การชำระเงินตัวอย่าง
- 5 แพ็กเกจตัวอย่าง

## 🛡️ Security Features

- Session-based authentication
- Admin role verification
- Input validation
- Error handling และ logging
- Secure password handling

## 📱 GUI Features

- Modern dark theme
- Responsive design
- Tabbed interface
- Dialog boxes สำหรับ forms
- Progress indicators
- Error และ success messages

## 🐛 Troubleshooting

### แอปพลิเคชันไม่เปิด
1. ตรวจสอบว่ามี Python ติดตั้งแล้ว (สำหรับ .py)
2. รัน `run_admin_tools.bat` เพื่อดู error messages
3. ตรวจสอบ Windows Defender หรือ Antivirus

### Login ไม่ได้
1. ลองใช้ offline credentials
2. ตรวจสอบการเชื่อมต่อเว็บไซต์
3. ตรวจสอบ admin permissions ในฐานข้อมูล

### ข้อมูลไม่แสดง
1. ตรวจสอบการเชื่อมต่อ API
2. ดูข้อมูล demo แทน
3. ตรวจสอบ server logs

## 📞 Support

หากมีปัญหาการใช้งาน:
1. ตรวจสอบ error messages
2. ดู logs ในแอปพลิเคชัน
3. ลองใช้งานแบบ offline mode

## 🔄 Updates

เพื่ออัปเดตแอปพลิเคชัน:
1. ดาวน์โหลด AdminTools.py ใหม่
2. รัน `build_simple.bat` ใหม่
3. ใช้ไฟล์ .exe ใหม่

---

**Version:** 1.0.0  
**Last Updated:** 2024-01-16  
**Compatible with:** Windows 10/11, Python 3.7+
