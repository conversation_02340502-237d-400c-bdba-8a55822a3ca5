<?php
// URL Test Page - Check if clean URLs are working
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Test - Jellyfin by <PERSON></title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 30px;
            border-radius: 8px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #444;
            border-radius: 4px;
        }
        .url-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .url-item {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .url-item a {
            color: #00c3ff;
            text-decoration: none;
            font-weight: bold;
        }
        .url-item a:hover {
            text-decoration: underline;
        }
        .url-description {
            color: #ccc;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .status-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Clean URL Test Page</h1>
        
        <div class="status status-info">
            <strong>การทดสอบ Clean URLs:</strong><br>
            หน้านี้ใช้สำหรับทดสอบว่า .htaccess ทำงานถูกต้องหรือไม่<br>
            Current URL: <?php echo $_SERVER['REQUEST_URI']; ?><br>
            Server: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
        </div>

        <div class="test-section">
            <h3>🏠 หน้าหลัก (Main Pages)</h3>
            <div class="url-list">
                <div class="url-item">
                    <a href="/home">หน้าแรก (/home)</a>
                    <div class="url-description">เหมือนกับ index.php</div>
                </div>
                <div class="url-item">
                    <a href="/login">เข้าสู่ระบบ (/login)</a>
                    <div class="url-description">หน้า login</div>
                </div>
                <div class="url-item">
                    <a href="/register">สมัครสมาชิก (/register)</a>
                    <div class="url-description">หน้า register</div>
                </div>
                <div class="url-item">
                    <a href="/dashboard">แดชบอร์ด (/dashboard)</a>
                    <div class="url-description">หน้า dashboard</div>
                </div>
                <div class="url-item">
                    <a href="/packages">แพ็คเกจ (/packages)</a>
                    <div class="url-description">หน้า packages</div>
                </div>
                <div class="url-item">
                    <a href="/profile">โปรไฟล์ (/profile)</a>
                    <div class="url-description">หน้า profile</div>
                </div>
                <div class="url-item">
                    <a href="/guide">คู่มือ (/guide)</a>
                    <div class="url-description">หน้า guide</div>
                </div>
                <div class="url-item">
                    <a href="/payment">ชำระเงิน (/payment)</a>
                    <div class="url-description">หน้า payment</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ หน้า Admin</h3>
            <div class="url-list">
                <div class="url-item">
                    <a href="/admin">Admin หลัก (/admin)</a>
                    <div class="url-description">หน้า admin หลัก</div>
                </div>
                <div class="url-item">
                    <a href="/admin/server">Server Control (/admin/server)</a>
                    <div class="url-description">ควบคุมเซิร์ฟเวอร์</div>
                </div>
                <div class="url-item">
                    <a href="/admin/users">จัดการผู้ใช้ (/admin/users)</a>
                    <div class="url-description">จัดการผู้ใช้</div>
                </div>
                <div class="url-item">
                    <a href="/admin/packages">จัดการแพ็คเกจ (/admin/packages)</a>
                    <div class="url-description">จัดการแพ็คเกจ</div>
                </div>
                <div class="url-item">
                    <a href="/admin/transactions">ธุรกรรม (/admin/transactions)</a>
                    <div class="url-description">ตรวจสอบธุรกรรม</div>
                </div>
                <div class="url-item">
                    <a href="/admin/paynoi">PayNoi (/admin/paynoi)</a>
                    <div class="url-description">PayNoi transactions</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔗 การทดสอบ Redirect</h3>
            <div class="url-list">
                <div class="url-item">
                    <a href="/login.php">login.php (ควร redirect ไป /login)</a>
                    <div class="url-description">ทดสอบ redirect จาก .php</div>
                </div>
                <div class="url-item">
                    <a href="/dashboard.php">dashboard.php (ควร redirect ไป /dashboard)</a>
                    <div class="url-description">ทดสอบ redirect จาก .php</div>
                </div>
                <div class="url-item">
                    <a href="/admin/server-control.php">server-control.php (ควร redirect ไป /admin/server)</a>
                    <div class="url-description">ทดสอบ redirect จาก .php</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>❌ การทดสอบ Error Pages</h3>
            <div class="url-list">
                <div class="url-item">
                    <a href="/nonexistent-page">หน้าที่ไม่มีอยู่ (404)</a>
                    <div class="url-description">ทดสอบหน้า 404</div>
                </div>
                <div class="url-item">
                    <a href="/admin/secret-page">หน้า Admin ที่ไม่มีสิทธิ์ (403)</a>
                    <div class="url-description">ทดสอบหน้า 403</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 วิธีการทดสอบ</h3>
            <div class="status status-warning">
                <strong>ขั้นตอนการทดสอบ:</strong><br>
                1. คลิกลิงก์แต่ละอันข้างต้น<br>
                2. ตรวจสอบว่า URL ใน address bar เป็น clean URL หรือไม่<br>
                3. ตรวจสอบว่าหน้าเว็บโหลดถูกต้องหรือไม่<br>
                4. ทดสอบ redirect จาก .php URLs<br>
                5. ทดสอบ error pages
            </div>
        </div>

        <div class="test-section">
            <h3>🔙 Navigation</h3>
            <div class="url-list">
                <div class="url-item">
                    <a href="/">กลับหน้าหลัก</a>
                    <div class="url-description">index.php</div>
                </div>
                <div class="url-item">
                    <a href="/dashboard">ไปแดชบอร์ด</a>
                    <div class="url-description">dashboard.php</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add click tracking
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.url-item a');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('Testing URL:', this.href);
                    
                    // Add visual feedback
                    this.style.color = '#ffc107';
                    setTimeout(() => {
                        this.style.color = '#00c3ff';
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html>
