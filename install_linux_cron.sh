#!/bin/bash

# Jellyfin by James - Linux Cron Installation Script
# Production environment: ************* / embyjames.xyz
# 
# This script installs and configures cron jobs for automated system management

set -e

echo "🎬 Jellyfin by James - Linux Cron Installation"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEB_ROOT="/var/www/html"
LOG_DIR="/var/log/jellyfin"
CRON_USER="www-data"
PHP_BIN="/usr/bin/php"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   echo "Usage: sudo ./install_linux_cron.sh"
   exit 1
fi

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  Web Root: $WEB_ROOT"
echo "  Log Directory: $LOG_DIR"
echo "  Cron User: $CRON_USER"
echo "  PHP Binary: $PHP_BIN"
echo ""

# Check if PHP CLI is available
if ! command -v php &> /dev/null; then
    echo -e "${RED}❌ PHP CLI not found. Please install PHP CLI first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ PHP CLI found: $(php --version | head -n1)${NC}"

# Create log directory
echo -e "${BLUE}📁 Creating log directory...${NC}"
mkdir -p "$LOG_DIR"
chown www-data:www-data "$LOG_DIR"
chmod 755 "$LOG_DIR"
echo -e "${GREEN}✅ Log directory created: $LOG_DIR${NC}"

# Copy cron scripts to web root
echo -e "${BLUE}📋 Installing cron scripts...${NC}"

if [ ! -d "$WEB_ROOT/cron" ]; then
    mkdir -p "$WEB_ROOT/cron"
fi

# Copy Linux-optimized cron scripts
cp cron/linux_payment_check.php "$WEB_ROOT/cron/"
cp cron/linux_expiration_check.php "$WEB_ROOT/cron/"
cp cron/linux_manual_check.php "$WEB_ROOT/cron/"
cp cron/linux_health_check.php "$WEB_ROOT/cron/"

# Set proper permissions
chown -R www-data:www-data "$WEB_ROOT/cron"
chmod 644 "$WEB_ROOT/cron"/*.php

# Make monitoring script executable
chmod +x monitor_linux_cron.sh

echo -e "${GREEN}✅ Cron scripts installed${NC}"

# Create crontab configuration
echo -e "${BLUE}⏰ Setting up cron jobs...${NC}"

CRONTAB_FILE="/tmp/jellyfin_crontab"

cat > "$CRONTAB_FILE" << EOF
# Jellyfin by James - Automated Cron Jobs
# Generated on $(date)

# PayNoi payment verification - every 5 minutes
*/5 * * * * $PHP_BIN $WEB_ROOT/cron/linux_payment_check.php >> $LOG_DIR/payment_cron.log 2>&1

# User expiration check - every hour
0 * * * * $PHP_BIN $WEB_ROOT/cron/linux_expiration_check.php >> $LOG_DIR/expiration_cron.log 2>&1

# Manual payment slip verification - every 10 minutes
*/10 * * * * $PHP_BIN $WEB_ROOT/cron/linux_manual_check.php >> $LOG_DIR/manual_cron.log 2>&1

# System health check - every 15 minutes
*/15 * * * * $PHP_BIN $WEB_ROOT/cron/linux_health_check.php >> $LOG_DIR/health_cron.log 2>&1

# Log cleanup - daily at 2 AM
0 2 * * * find $LOG_DIR -name "*.log" -size +100M -exec truncate -s 10M {} \; >> $LOG_DIR/cleanup.log 2>&1

# Database optimization - weekly on Sunday at 3 AM
0 3 * * 0 $PHP_BIN $WEB_ROOT/cron/linux_health_check.php >> $LOG_DIR/weekly_maintenance.log 2>&1

EOF

# Install crontab for www-data user
echo -e "${YELLOW}📝 Installing crontab for user: $CRON_USER${NC}"
crontab -u "$CRON_USER" "$CRONTAB_FILE"

# Clean up temporary file
rm "$CRONTAB_FILE"

echo -e "${GREEN}✅ Crontab installed successfully${NC}"

# Create logrotate configuration
echo -e "${BLUE}🔄 Setting up log rotation...${NC}"

cat > "/etc/logrotate.d/jellyfin" << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    copytruncate
    su www-data www-data
}
EOF

echo -e "${GREEN}✅ Log rotation configured${NC}"

# Create systemd service for monitoring (optional)
echo -e "${BLUE}🔧 Creating monitoring service...${NC}"

cat > "/etc/systemd/system/jellyfin-monitor.service" << EOF
[Unit]
Description=Jellyfin by James - System Monitor
After=network.target mysql.service

[Service]
Type=oneshot
User=www-data
Group=www-data
WorkingDirectory=$WEB_ROOT
ExecStart=$PHP_BIN $WEB_ROOT/cron/linux_health_check.php
StandardOutput=append:$LOG_DIR/monitor_service.log
StandardError=append:$LOG_DIR/monitor_service.log

[Install]
WantedBy=multi-user.target
EOF

# Create timer for the monitoring service
cat > "/etc/systemd/system/jellyfin-monitor.timer" << EOF
[Unit]
Description=Run Jellyfin Monitor every 15 minutes
Requires=jellyfin-monitor.service

[Timer]
OnCalendar=*:0/15
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Reload systemd and enable timer
systemctl daemon-reload
systemctl enable jellyfin-monitor.timer
systemctl start jellyfin-monitor.timer

echo -e "${GREEN}✅ Monitoring service created and enabled${NC}"

# Test cron scripts
echo -e "${BLUE}🧪 Testing cron scripts...${NC}"

echo -e "${YELLOW}Testing health check...${NC}"
sudo -u www-data $PHP_BIN "$WEB_ROOT/cron/linux_health_check.php"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Health check test passed${NC}"
else
    echo -e "${RED}❌ Health check test failed${NC}"
fi

# Display current crontab
echo -e "${BLUE}📋 Current crontab for $CRON_USER:${NC}"
crontab -u "$CRON_USER" -l

# Display log files
echo -e "${BLUE}📁 Log files will be created in: $LOG_DIR${NC}"
echo "  - payment_cron.log (PayNoi verification)"
echo "  - expiration_cron.log (User expiration)"
echo "  - manual_cron.log (Manual slip verification)"
echo "  - health_cron.log (System health)"
echo "  - cleanup.log (Log cleanup)"
echo "  - weekly_maintenance.log (Weekly maintenance)"

# Create initial log files with proper permissions
touch "$LOG_DIR/payment_cron.log"
touch "$LOG_DIR/expiration_cron.log"
touch "$LOG_DIR/manual_cron.log"
touch "$LOG_DIR/health_cron.log"
touch "$LOG_DIR/cleanup.log"
touch "$LOG_DIR/weekly_maintenance.log"

chown www-data:www-data "$LOG_DIR"/*.log
chmod 644 "$LOG_DIR"/*.log

echo ""
echo -e "${GREEN}🎉 Linux Cron Installation Completed Successfully!${NC}"
echo ""
echo -e "${BLUE}📊 Monitoring Commands:${NC}"
echo "  View cron jobs: crontab -u www-data -l"
echo "  Check logs: tail -f $LOG_DIR/payment_cron.log"
echo "  Service status: systemctl status jellyfin-monitor.timer"
echo "  Manual test: sudo -u www-data php $WEB_ROOT/cron/linux_health_check.php"
echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "  Restart cron: systemctl restart cron"
echo "  Check cron status: systemctl status cron"
echo "  View system logs: journalctl -u cron -f"
echo ""
echo -e "${YELLOW}⚠️  Important Notes:${NC}"
echo "  - Cron jobs will start running automatically"
echo "  - Check logs in $LOG_DIR for any issues"
echo "  - PayNoi API keys must be configured in config/database.php"
echo "  - Jellyfin API settings must be configured"
echo ""
echo -e "${GREEN}🎬 Jellyfin by James cron system is now active!${NC}"
