<svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Film strip background -->
  <rect x="0" y="0" width="120" height="40" fill="#1a1a1a" rx="4"/>

  <!-- Film perforations -->
  <rect x="2" y="4" width="3" height="2" fill="#333"/>
  <rect x="2" y="8" width="3" height="2" fill="#333"/>
  <rect x="2" y="12" width="3" height="2" fill="#333"/>
  <rect x="2" y="16" width="3" height="2" fill="#333"/>
  <rect x="2" y="20" width="3" height="2" fill="#333"/>
  <rect x="2" y="24" width="3" height="2" fill="#333"/>
  <rect x="2" y="28" width="3" height="2" fill="#333"/>
  <rect x="2" y="32" width="3" height="2" fill="#333"/>

  <rect x="115" y="4" width="3" height="2" fill="#333"/>
  <rect x="115" y="8" width="3" height="2" fill="#333"/>
  <rect x="115" y="12" width="3" height="2" fill="#333"/>
  <rect x="115" y="16" width="3" height="2" fill="#333"/>
  <rect x="115" y="20" width="3" height="2" fill="#333"/>
  <rect x="115" y="24" width="3" height="2" fill="#333"/>
  <rect x="115" y="28" width="3" height="2" fill="#333"/>
  <rect x="115" y="32" width="3" height="2" fill="#333"/>

  <!-- Main content area -->
  <rect x="8" y="6" width="104" height="28" fill="#0a0a0a" rx="2"/>

  <!-- Play button icon -->
  <polygon points="15,12 15,28 28,20" fill="url(#goldGradient)" filter="url(#glow)"/>
  <polygon points="16,14 16,26 25,20" fill="#000" opacity="0.3"/>

  <!-- Text "J" for Jellyfin -->
  <text x="35" y="26" font-family="serif" font-size="18" font-weight="bold" fill="url(#goldGradient)" filter="url(#glow)">J</text>

  <!-- Film reel circles -->
  <circle cx="50" cy="20" r="8" fill="none" stroke="url(#goldGradient)" stroke-width="1.5" opacity="0.8"/>
  <circle cx="50" cy="20" r="5" fill="none" stroke="url(#goldGradient)" stroke-width="1" opacity="0.6"/>
  <circle cx="50" cy="20" r="2" fill="url(#goldGradient)" opacity="0.8"/>

  <circle cx="65" cy="20" r="8" fill="none" stroke="url(#goldGradient)" stroke-width="1.5" opacity="0.8"/>
  <circle cx="65" cy="20" r="5" fill="none" stroke="url(#goldGradient)" stroke-width="1" opacity="0.6"/>
  <circle cx="65" cy="20" r="2" fill="url(#goldGradient)" opacity="0.8"/>

  <!-- Connecting film strip -->
  <rect x="58" y="18" width="7" height="4" fill="url(#goldGradient)" opacity="0.6"/>

  <!-- Cinema text -->
  <text x="75" y="16" font-family="serif" font-size="7" font-weight="bold" fill="url(#goldGradient)" opacity="0.8">CINEMA</text>
  <text x="75" y="26" font-family="serif" font-size="5" font-weight="normal" fill="url(#goldGradient)" opacity="0.6">by James</text>

  <!-- Border highlight -->
  <rect x="0" y="0" width="120" height="40" fill="none" stroke="url(#goldGradient)" stroke-width="0.5" opacity="0.3" rx="4"/>
</svg>
