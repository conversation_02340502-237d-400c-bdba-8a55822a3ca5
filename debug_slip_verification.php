<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🔍 Debug Slip Verification System</h2>";
echo "<p>User ID: {$_SESSION['user_id']}</p>";

try {
    echo "<h3>📊 Manual Payment Statistics:</h3>";
    
    // Get manual payment statistics
    $stats = [];
    
    // Total manual payments
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM payment_transactions 
        WHERE payment_method = 'manual' OR slip_image IS NOT NULL
    ");
    $stmt->execute();
    $stats['total_manual'] = $stmt->fetchColumn();
    
    // Pending manual payments
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM payment_transactions 
        WHERE status = 'pending' AND slip_image IS NOT NULL
    ");
    $stmt->execute();
    $stats['pending_manual'] = $stmt->fetchColumn();
    
    // Verified manual payments
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM payment_transactions 
        WHERE status IN ('verified', 'completed') AND slip_image IS NOT NULL
    ");
    $stmt->execute();
    $stats['verified_manual'] = $stmt->fetchColumn();
    
    // Recent manual payments (last 24 hours)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count FROM payment_transactions 
        WHERE slip_image IS NOT NULL AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $stats['recent_manual'] = $stmt->fetchColumn();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Total Manual Payments</td><td>{$stats['total_manual']}</td></tr>";
    echo "<tr><td>Pending Manual Payments</td><td style='background: yellow;'>{$stats['pending_manual']}</td></tr>";
    echo "<tr><td>Verified Manual Payments</td><td style='background: lightgreen;'>{$stats['verified_manual']}</td></tr>";
    echo "<tr><td>Recent Manual Payments (24h)</td><td>{$stats['recent_manual']}</td></tr>";
    echo "</table>";

    echo "<h3>🔍 Pending Manual Payments (with slips):</h3>";
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll();

    if ($pendingPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Package</th><th>Slip Image</th><th>Created</th><th>Age (hours)</th></tr>";
        foreach ($pendingPayments as $payment) {
            $age = (time() - strtotime($payment['created_at'])) / 3600;
            $ageColor = $age > 1 ? 'background: orange;' : '';
            echo "<tr style='{$ageColor}'>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['package_name'] ?? 'N/A') . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅ ' . basename($payment['slip_image']) : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "<td>" . round($age, 1) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>✅ ไม่มี pending manual payments</p>";
    }

    echo "<h3>🔧 Manual Verification Test:</h3>";
    if ($pendingPayments) {
        $testPayment = $pendingPayments[0];
        echo "<p>Testing payment ID: {$testPayment['id']}</p>";
        
        // Check slip file exists
        $slipPath = "uploads/slips/" . $testPayment['slip_image'];
        echo "<p>Slip path: {$slipPath}</p>";
        echo "<p>File exists: " . (file_exists($slipPath) ? '✅ YES' : '❌ NO') . "</p>";
        
        if (file_exists($slipPath)) {
            $fileSize = filesize($slipPath);
            $fileTime = filemtime($slipPath);
            $paymentTime = strtotime($testPayment['created_at']);
            
            echo "<p>File size: " . number_format($fileSize) . " bytes</p>";
            echo "<p>File time: " . date('Y-m-d H:i:s', $fileTime) . "</p>";
            echo "<p>Payment time: " . date('Y-m-d H:i:s', $paymentTime) . "</p>";
            echo "<p>Time difference: " . abs($fileTime - $paymentTime) . " seconds</p>";
            
            // Check auto-verification criteria
            $isValidSize = $fileSize > 10000 && $fileSize < 10000000;
            $isRecent = abs($fileTime - $paymentTime) < 3600;
            $isSmallAmount = $testPayment['amount'] <= 100;
            
            echo "<p>Valid size (10KB-10MB): " . ($isValidSize ? '✅ YES' : '❌ NO') . "</p>";
            echo "<p>Recent file (within 1h): " . ($isRecent ? '✅ YES' : '❌ NO') . "</p>";
            echo "<p>Small amount (≤100 THB): " . ($isSmallAmount ? '✅ YES' : '❌ NO') . "</p>";
            
            // Check user history
            $historyStmt = $pdo->prepare("
                SELECT COUNT(*) as verified_count FROM payment_transactions
                WHERE user_id = ? AND status IN ('verified', 'completed')
            ");
            $historyStmt->execute([$testPayment['user_id']]);
            $verifiedCount = $historyStmt->fetch()['verified_count'];
            $isTrustedUser = $verifiedCount >= 2;
            
            echo "<p>User verified payments: {$verifiedCount}</p>";
            echo "<p>Trusted user (≥2 payments): " . ($isTrustedUser ? '✅ YES' : '❌ NO') . "</p>";
            
            $shouldAutoVerify = ($isSmallAmount || $isTrustedUser) && $isRecent && $isValidSize;
            echo "<p><strong>Should auto-verify: " . ($shouldAutoVerify ? '✅ YES' : '❌ NO') . "</strong></p>";
        }
    }

    echo "<h3>🔄 Cron Job Status Check:</h3>";
    
    // Check if we can simulate the cron job logic
    echo "<h4>Simulating Manual Check Cron Logic:</h4>";
    
    // Get the same query as cron job
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
        ORDER BY pt.created_at ASC
        LIMIT 30
    ");
    $stmt->execute();
    $cronPayments = $stmt->fetchAll();
    
    echo "<p>Payments found by cron query: " . count($cronPayments) . "</p>";
    
    if ($cronPayments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Method</th><th>Auto-Verify?</th></tr>";
        foreach ($cronPayments as $payment) {
            $isSmallAmount = $payment['amount'] <= 100;
            
            $historyStmt = $pdo->prepare("
                SELECT COUNT(*) as verified_count FROM payment_transactions
                WHERE user_id = ? AND status IN ('verified', 'completed')
            ");
            $historyStmt->execute([$payment['user_id']]);
            $verifiedCount = $historyStmt->fetch()['verified_count'];
            $isTrustedUser = $verifiedCount >= 2;
            
            $shouldAutoVerify = $isSmallAmount || $isTrustedUser;
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['payment_method'] . "</td>";
            echo "<td>" . ($shouldAutoVerify ? '✅ YES' : '❌ NO') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<h3>🛠️ Manual Actions:</h3>";
    if ($pendingPayments) {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='test_verification' value='1'>";
        echo "<button type='submit' style='background: green; color: white; padding: 10px; border: none; cursor: pointer;'>Run Manual Verification Test</button>";
        echo "</form>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle manual verification test
if ($_POST && isset($_POST['test_verification'])) {
    try {
        echo "<h3>🧪 Running Manual Verification Test:</h3>";
        
        // Include JellyfinAPI
        require_once 'includes/JellyfinAPI.php';
        $jellyfin = new JellyfinAPI();
        
        $stmt = $pdo->prepare("
            SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
            FROM payment_transactions pt
            JOIN users u ON pt.user_id = u.id
            LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
            LEFT JOIN packages p ON us.package_id = p.id
            WHERE pt.status = 'pending'
            AND pt.slip_image IS NOT NULL
            AND pt.slip_image != ''
            ORDER BY pt.created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $payment = $stmt->fetch();
        
        if ($payment) {
            echo "<p>Testing payment ID: {$payment['id']}</p>";
            
            // Check auto-verification criteria
            $isSmallAmount = $payment['amount'] <= 100;
            
            $historyStmt = $pdo->prepare("
                SELECT COUNT(*) as verified_count FROM payment_transactions
                WHERE user_id = ? AND status IN ('verified', 'completed')
            ");
            $historyStmt->execute([$payment['user_id']]);
            $verifiedCount = $historyStmt->fetch()['verified_count'];
            $isTrustedUser = $verifiedCount >= 2;
            
            $shouldAutoVerify = $isSmallAmount || $isTrustedUser;
            
            if ($shouldAutoVerify) {
                echo "<p>✅ Auto-verification criteria met. Attempting verification...</p>";
                
                $pdo->beginTransaction();
                
                // Update payment status
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'verified',
                        verified_at = NOW(),
                        admin_notes = 'Test verification from debug tool'
                    WHERE id = ?
                ");
                $updateStmt->execute([$payment['id']]);
                
                echo "<p>✅ Payment status updated to verified</p>";
                
                $pdo->commit();
                echo "<p>✅ Test verification completed successfully!</p>";
                
            } else {
                echo "<p>❌ Auto-verification criteria not met</p>";
                echo "<p>Small amount (≤100): " . ($isSmallAmount ? 'YES' : 'NO') . "</p>";
                echo "<p>Trusted user (≥2 payments): " . ($isTrustedUser ? 'YES' : 'NO') . "</p>";
            }
        } else {
            echo "<p>❌ No pending payments with slips found</p>";
        }
        
    } catch (Exception $e) {
        if (isset($pdo)) $pdo->rollback();
        echo "<p>❌ Test verification error: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='/admin'>← กลับไปหน้า Admin</a></p>";
?>
