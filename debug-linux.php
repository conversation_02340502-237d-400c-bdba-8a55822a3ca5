<?php
// Debug Linux production issues
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Linux Production - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        .info { color: #17a2b8; }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .code-block {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="color: #FFD700; text-align: center;">🐧 Debug Linux Production Issues</h1>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">📋 System Information</h2>
            <?php
            echo "<p><strong>Operating System:</strong> " . PHP_OS . "</p>";
            echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
            echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Script Filename:</strong> " . ($_SERVER['SCRIPT_FILENAME'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
            echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";
            
            // Check if running on Linux
            if (stripos(PHP_OS, 'linux') !== false) {
                echo "<p class='success'>✅ Running on Linux</p>";
            } else {
                echo "<p class='warning'>⚠️ Not running on Linux: " . PHP_OS . "</p>";
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🔧 Apache/Web Server Check</h2>
            <?php
            // Check Apache modules
            if (function_exists('apache_get_modules')) {
                $modules = apache_get_modules();
                echo "<p class='success'>✅ Apache functions available</p>";
                
                $important_modules = ['mod_rewrite', 'mod_dir', 'mod_mime'];
                foreach ($important_modules as $module) {
                    if (in_array($module, $modules)) {
                        echo "<p class='success'>✅ $module is loaded</p>";
                    } else {
                        echo "<p class='error'>❌ $module is NOT loaded</p>";
                    }
                }
            } else {
                echo "<p class='warning'>⚠️ Apache functions not available (might be Nginx or other server)</p>";
            }
            
            // Check if .htaccess exists and is readable
            if (file_exists('.htaccess')) {
                echo "<p class='success'>✅ .htaccess file exists</p>";
                if (is_readable('.htaccess')) {
                    echo "<p class='success'>✅ .htaccess is readable</p>";
                    $perms = substr(sprintf('%o', fileperms('.htaccess')), -4);
                    echo "<p><strong>Permissions:</strong> $perms</p>";
                } else {
                    echo "<p class='error'>❌ .htaccess is not readable</p>";
                }
            } else {
                echo "<p class='error'>❌ .htaccess file does not exist</p>";
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">📁 File Permissions Check</h2>
            <?php
            $files_to_check = [
                '.' => 'Root directory',
                'index.php' => 'Homepage',
                'login.php' => 'Login page',
                'dashboard.php' => 'Dashboard page',
                'admin/' => 'Admin directory',
                'admin/index.php' => 'Admin homepage',
                'includes/' => 'Includes directory',
                'includes/functions.php' => 'Functions file',
                'config/' => 'Config directory',
                'assets/' => 'Assets directory'
            ];
            
            foreach ($files_to_check as $file => $description) {
                if (file_exists($file)) {
                    $perms = substr(sprintf('%o', fileperms($file)), -4);
                    $is_readable = is_readable($file) ? '✅' : '❌';
                    $is_writable = is_writable($file) ? '✅' : '❌';
                    
                    echo "<p><strong>$description ($file):</strong> Permissions: $perms, Readable: $is_readable, Writable: $is_writable</p>";
                } else {
                    echo "<p class='error'>❌ $description ($file) does not exist</p>";
                }
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🧪 Clean URL Tests</h2>
            <p style="color: #ccc;">คลิกลิงก์เหล่านี้เพื่อทดสอบ Clean URLs:</p>
            
            <h3 style="color: #FFD700;">Main Pages:</h3>
            <a href="/login" class="test-link" target="_blank">Login (/login)</a>
            <a href="/register" class="test-link" target="_blank">Register (/register)</a>
            <a href="/dashboard" class="test-link" target="_blank">Dashboard (/dashboard)</a>
            <a href="/packages" class="test-link" target="_blank">Packages (/packages)</a>
            <a href="/guide" class="test-link" target="_blank">Guide (/guide)</a>
            
            <h3 style="color: #FFD700;">Admin Pages:</h3>
            <a href="/admin" class="test-link" target="_blank">Admin Console (/admin)</a>
            <a href="/admin/users" class="test-link" target="_blank">Admin Users (/admin/users)</a>
            <a href="/admin/packages" class="test-link" target="_blank">Admin Packages (/admin/packages)</a>
            
            <h3 style="color: #FFD700;">Direct PHP Files (for comparison):</h3>
            <a href="/login.php" class="test-link" target="_blank">login.php (direct)</a>
            <a href="/dashboard.php" class="test-link" target="_blank">dashboard.php (direct)</a>
            <a href="/admin/index.php" class="test-link" target="_blank">admin/index.php (direct)</a>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🔍 Environment Variables</h2>
            <div class="code-block">
                <?php
                $env_vars = [
                    'REQUEST_METHOD',
                    'REQUEST_URI',
                    'SCRIPT_NAME',
                    'SCRIPT_FILENAME',
                    'QUERY_STRING',
                    'HTTP_HOST',
                    'SERVER_NAME',
                    'DOCUMENT_ROOT',
                    'PATH_INFO',
                    'REDIRECT_STATUS',
                    'REDIRECT_URL',
                    'REDIRECT_QUERY_STRING'
                ];
                
                foreach ($env_vars as $var) {
                    $value = $_SERVER[$var] ?? 'Not set';
                    echo "<strong style='color: #FFD700;'>$var:</strong> <span style='color: #ccc;'>$value</span><br>";
                }
                ?>
            </div>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">🛠️ Common Linux Production Fixes</h2>
            
            <h3 style="color: #FFD700;">1. Enable mod_rewrite (Ubuntu/Debian):</h3>
            <div class="code-block">
sudo a2enmod rewrite
sudo systemctl restart apache2
            </div>
            
            <h3 style="color: #FFD700;">2. Configure Virtual Host:</h3>
            <div class="code-block">
# Edit: /etc/apache2/sites-available/000-default.conf
# Add inside &lt;VirtualHost&gt; block:

&lt;Directory /var/www/html&gt;
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
&lt;/Directory&gt;
            </div>
            
            <h3 style="color: #FFD700;">3. Fix Permissions:</h3>
            <div class="code-block">
# Set correct permissions
sudo chmod 644 .htaccess
sudo chmod 644 *.php
sudo chmod 755 admin/
sudo chmod 644 admin/*.php
sudo chown -R www-data:www-data /var/www/html/
            </div>
            
            <h3 style="color: #FFD700;">4. Check Apache Error Log:</h3>
            <div class="code-block">
# View errors in real-time
sudo tail -f /var/log/apache2/error.log

# Check access log
sudo tail -f /var/log/apache2/access.log
            </div>
            
            <h3 style="color: #FFD700;">5. Test Apache Configuration:</h3>
            <div class="code-block">
# Test configuration
sudo apache2ctl configtest

# Reload if OK
sudo systemctl reload apache2
            </div>
        </div>
        
        <div class="debug-section">
            <h2 style="color: #00c300;">📝 Troubleshooting Checklist</h2>
            <ol style="color: #ccc; line-height: 1.8;">
                <li><strong>mod_rewrite enabled?</strong> Run: <code>apache2ctl -M | grep rewrite</code></li>
                <li><strong>AllowOverride All?</strong> Check virtual host configuration</li>
                <li><strong>File permissions correct?</strong> .htaccess should be 644</li>
                <li><strong>Directory permissions correct?</strong> Directories should be 755</li>
                <li><strong>Owner correct?</strong> Files should be owned by web server user (www-data)</li>
                <li><strong>Case sensitivity?</strong> Linux is case-sensitive, Windows is not</li>
                <li><strong>Path separators?</strong> Use forward slashes (/) not backslashes (\)</li>
                <li><strong>Apache error log?</strong> Check for rewrite errors</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/test-htaccess.php" style="color: #00c300; text-decoration: none; padding: 10px 20px; border: 1px solid #00c300; border-radius: 5px; margin-right: 10px;">
                🧪 .htaccess Test
            </a>
            <a href="/debug-apache.php" style="color: #FFA500; text-decoration: none; padding: 10px 20px; border: 1px solid #FFA500; border-radius: 5px; margin-right: 10px;">
                🔧 Apache Debug
            </a>
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                🏠 หน้าแรก
            </a>
        </div>
    </div>
    
    <script>
        console.log('Debug Linux page loaded');
        console.log('User Agent:', navigator.userAgent);
        console.log('Current URL:', window.location.href);
        
        // Auto-refresh every 60 seconds for testing
        // setTimeout(() => location.reload(), 60000);
    </script>
</body>
</html>
