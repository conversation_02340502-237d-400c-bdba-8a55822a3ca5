#!/bin/bash
# Installation script for <PERSON><PERSON><PERSON> by James Production Environment
# Target: Linux server ************* / embyjames.xyz

set -e

echo "🎬 Jellyfin by James - Production Installation"
echo "=============================================="
echo "Target: ************* / embyjames.xyz"
echo "=============================================="

# Check if running as root - allow root for production deployment
if [[ $EUID -eq 0 ]]; then
   echo "⚠️  Running as root - this is acceptable for production deployment"
   SUDO_CMD=""
else
   echo "ℹ️  Running as regular user - will use sudo for privileged operations"
   SUDO_CMD="sudo"
fi

# Check if we're on the right server
echo "🔍 Checking server environment..."
if [[ $(hostname -I | grep -c "*************") -eq 0 ]]; then
    echo "⚠️  Warning: Not running on expected IP *************"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Install required packages
echo "📦 Installing required packages..."
${SUDO_CMD} apt update
${SUDO_CMD} apt install -y python3 python3-pip python3-venv curl mysql-client

# Install Python packages
echo "🐍 Installing Python packages..."
if [[ $EUID -eq 0 ]]; then
    pip3 install requests
else
    pip3 install --user requests
fi

# Create directories
echo "📁 Creating directories..."
${SUDO_CMD} mkdir -p /opt/jellyfin
${SUDO_CMD} mkdir -p /var/log/jellyfin
${SUDO_CMD} chown www-data:www-data /var/log/jellyfin

# Copy files
echo "📋 Copying files..."
${SUDO_CMD} cp production_cron.py /opt/jellyfin/
${SUDO_CMD} cp continuous_payment_checker.py /opt/jellyfin/
${SUDO_CMD} chmod +x /opt/jellyfin/*.py
${SUDO_CMD} chown www-data:www-data /opt/jellyfin/*.py

# Install systemd service
echo "⚙️  Installing systemd service..."
${SUDO_CMD} cp jellyfin-payment-checker.service /etc/systemd/system/
${SUDO_CMD} systemctl daemon-reload

# Install crontab
echo "⏰ Installing crontab..."
${SUDO_CMD} -u www-data crontab jellyfin_crontab.txt

# Test API endpoints
echo "🧪 Testing API endpoints..."
if curl -s -f "https://embyjames.xyz/api/health.php" > /dev/null; then
    echo "✅ API endpoints accessible"
else
    echo "❌ API endpoints not accessible"
    echo "Please check your web server configuration"
fi

# Test database connection
echo "🗄️  Testing database connection..."
if mysql -h ************* -u root -p'Wxmujwsofu@1234' jellyfin_registration -e "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    echo "Please check database credentials and network access"
fi

# Start and enable service
echo "🚀 Starting services..."
${SUDO_CMD} systemctl enable jellyfin-payment-checker
${SUDO_CMD} systemctl start jellyfin-payment-checker

# Check service status
sleep 2
if ${SUDO_CMD} systemctl is-active --quiet jellyfin-payment-checker; then
    echo "✅ Payment checker service is running"
else
    echo "❌ Payment checker service failed to start"
    echo "Check logs: ${SUDO_CMD} journalctl -u jellyfin-payment-checker -f"
fi

echo ""
echo "🎉 Installation completed!"
echo ""
echo "📊 Monitoring commands:"
echo "  Service status: ${SUDO_CMD} systemctl status jellyfin-payment-checker"
echo "  Service logs:   ${SUDO_CMD} journalctl -u jellyfin-payment-checker -f"
echo "  Payment logs:   tail -f /var/log/jellyfin/continuous_payment.log"
echo "  Cron logs:      tail -f /var/log/jellyfin/payment_cron.log"
echo ""
echo "🔧 Management commands:"
echo "  Start service:  ${SUDO_CMD} systemctl start jellyfin-payment-checker"
echo "  Stop service:   ${SUDO_CMD} systemctl stop jellyfin-payment-checker"
echo "  Restart:        ${SUDO_CMD} systemctl restart jellyfin-payment-checker"
echo "  View crontab:   ${SUDO_CMD} -u www-data crontab -l"
echo ""
echo "📈 The system will now:"
echo "  ✓ Check payments every 5 seconds (continuous service)"
echo "  ✓ Run full system checks every 5 minutes (cron)"
echo "  ✓ Perform health checks every minute (cron)"
echo "  ✓ Clean up logs weekly"
echo ""
echo "🌐 Access your system at: https://embyjames.xyz"
