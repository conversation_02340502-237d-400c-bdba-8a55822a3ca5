<?php
// Test LINE QR Code loading
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test LINE QR Code - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        .info { color: #17a2b8; }
        .qr-test {
            text-align: center;
            padding: 20px;
            background: rgba(0, 195, 0, 0.1);
            border: 1px solid #00c300;
            border-radius: 8px;
            margin: 20px 0;
        }
        .qr-container {
            display: inline-block;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #FFD700; text-align: center;">🧪 Test LINE QR Code</h1>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📋 File System Check</h2>
            <?php
            $lineDir = __DIR__ . '/line/';
            $lineFile = $lineDir . 'line.jpg';
            
            echo "<p><strong>LINE directory:</strong> " . $lineDir . "</p>";
            echo "<p><strong>Current directory:</strong> " . __DIR__ . "</p>";
            echo "<p><strong>Document root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
            
            if (is_dir($lineDir)) {
                echo "<p class='success'>✅ LINE directory exists</p>";
                
                $files = scandir($lineDir);
                echo "<p><strong>Files in line directory:</strong></p><ul>";
                foreach ($files as $file) {
                    if ($file != '.' && $file != '..') {
                        $fullPath = $lineDir . $file;
                        $size = filesize($fullPath);
                        $readable = is_readable($fullPath) ? '✅' : '❌';
                        $perms = substr(sprintf('%o', fileperms($fullPath)), -4);
                        echo "<li>$readable $file (" . number_format($size) . " bytes, permissions: $perms)</li>";
                    }
                }
                echo "</ul>";
                
                if (file_exists($lineFile)) {
                    echo "<p class='success'>✅ line.jpg exists</p>";
                    echo "<p><strong>File size:</strong> " . filesize($lineFile) . " bytes</p>";
                    echo "<p><strong>MIME type:</strong> " . mime_content_type($lineFile) . "</p>";
                    echo "<p><strong>Readable:</strong> " . (is_readable($lineFile) ? 'Yes' : 'No') . "</p>";
                    echo "<p><strong>Full path:</strong> " . realpath($lineFile) . "</p>";
                } else {
                    echo "<p class='error'>❌ line.jpg does not exist</p>";
                }
            } else {
                echo "<p class='error'>❌ LINE directory does not exist</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🖼️ Image Loading Test</h2>
            
            <div class="qr-test">
                <h3 style="color: #00c300;">Current LINE QR Code</h3>
                <div class="qr-container">
                    <img id="line-qr-current" 
                         src="line/line.jpg" 
                         alt="LINE QR Code" 
                         style="width: 200px; height: 200px; object-fit: contain; border: 1px solid #ddd;"
                         onload="document.getElementById('status-current').innerHTML='<span class=success>✅ Loaded successfully</span>'; console.log('QR Code loaded from:', this.src);"
                         onerror="document.getElementById('status-current').innerHTML='<span class=error>❌ Failed to load</span>'; console.error('Failed to load QR Code from:', this.src);">
                </div>
                <div id="status-current" class="info">🔄 Loading...</div>
            </div>
            
            <div class="qr-test">
                <h3 style="color: #00c300;">Alternative Paths Test</h3>
                
                <div class="qr-container">
                    <h4>./line/line.jpg</h4>
                    <img src="./line/line.jpg" 
                         alt="LINE QR Code" 
                         style="width: 150px; height: 150px; object-fit: contain; border: 1px solid #ddd;"
                         onload="this.nextElementSibling.innerHTML='<span class=success>✅ OK</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'">
                    <div class="info">🔄 Testing...</div>
                </div>
                
                <div class="qr-container">
                    <h4>/line/line.jpg</h4>
                    <img src="/line/line.jpg" 
                         alt="LINE QR Code" 
                         style="width: 150px; height: 150px; object-fit: contain; border: 1px solid #ddd;"
                         onload="this.nextElementSibling.innerHTML='<span class=success>✅ OK</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'">
                    <div class="info">🔄 Testing...</div>
                </div>
                
                <div class="qr-container">
                    <h4>Full URL</h4>
                    <img id="full-url-test"
                         alt="LINE QR Code" 
                         style="width: 150px; height: 150px; object-fit: contain; border: 1px solid #ddd;"
                         onload="this.nextElementSibling.innerHTML='<span class=success>✅ OK</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'">
                    <div class="info">🔄 Testing...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🔧 Browser Console</h2>
            <p style="color: #ccc;">เปิด Developer Tools (F12) และดู Console เพื่อดูข้อความ debug</p>
            
            <div style="background: #000; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                <span style="color: #4CAF50;">// คำสั่งที่ใช้ใน Console:</span><br>
                console.log('Current page URL:', window.location.href);<br>
                console.log('QR Image src:', document.getElementById('line-qr-current').src);<br>
                console.log('QR Image complete:', document.getElementById('line-qr-current').complete);<br>
                console.log('QR Image naturalWidth:', document.getElementById('line-qr-current').naturalWidth);
            </div>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📝 Troubleshooting</h2>
            
            <h3 style="color: #FFD700;">หากรูปไม่แสดง:</h3>
            <ol style="color: #ccc; line-height: 1.8;">
                <li><strong>ตรวจสอบไฟล์:</strong>
                    <ul>
                        <li>ไฟล์ line.jpg อยู่ในโฟลเดอร์ line/</li>
                        <li>ไฟล์ไม่เสียหาย (เปิดได้ด้วยโปรแกรมดูรูป)</li>
                        <li>ขนาดไฟล์ไม่เกิน 5MB</li>
                    </ul>
                </li>
                <li><strong>ตรวจสอบ Permissions:</strong>
                    <ul>
                        <li>โฟลเดอร์ line/ ควรเป็น 755</li>
                        <li>ไฟล์ line.jpg ควรเป็น 644</li>
                        <li>Web server อ่านไฟล์ได้</li>
                    </ul>
                </li>
                <li><strong>ตรวจสอบ Path:</strong>
                    <ul>
                        <li>Path ถูกต้องตาม server structure</li>
                        <li>ไม่มี .htaccess block การเข้าถึง</li>
                        <li>Case sensitivity (Linux แยกตัวพิมพ์ใหญ่-เล็ก)</li>
                    </ul>
                </li>
            </ol>
            
            <h3 style="color: #FFD700;">คำสั่ง Linux:</h3>
            <div style="background: #000; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                <span style="color: #4CAF50;"># ตรวจสอบไฟล์</span><br>
                ls -la line/<br><br>
                
                <span style="color: #4CAF50;"># ตั้งค่า permissions</span><br>
                chmod 755 line/<br>
                chmod 644 line/line.jpg<br><br>
                
                <span style="color: #4CAF50;"># ตรวจสอบ MIME type</span><br>
                file line/line.jpg<br>
                file --mime-type line/line.jpg
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/guide#line-group" style="color: #00c300; text-decoration: none; padding: 10px 20px; border: 1px solid #00c300; border-radius: 5px; margin-right: 10px;">
                📖 ไปหน้าคู่มือ
            </a>
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                🏠 หน้าแรก
            </a>
        </div>
    </div>
    
    <script>
        // Set full URL for testing
        document.addEventListener('DOMContentLoaded', function() {
            const fullUrlImg = document.getElementById('full-url-test');
            const currentUrl = window.location.origin + '/line/line.jpg';
            fullUrlImg.src = currentUrl;
            
            console.log('Test page loaded');
            console.log('Current URL:', window.location.href);
            console.log('Testing full URL:', currentUrl);
            
            // Log all image loading attempts
            const images = document.querySelectorAll('img[src*="line.jpg"]');
            images.forEach((img, index) => {
                img.addEventListener('load', function() {
                    console.log(`Image ${index + 1} loaded successfully:`, this.src);
                });
                img.addEventListener('error', function() {
                    console.error(`Image ${index + 1} failed to load:`, this.src);
                });
            });
        });
    </script>
</body>
</html>
