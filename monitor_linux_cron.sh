#!/bin/bash

# Jellyfin by James - Linux Cron Monitoring Script
# Production environment: ************* / embyjames.xyz
# 
# This script monitors the health and status of all cron jobs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
LOG_DIR="/var/log/jellyfin"
WEB_ROOT="/var/www/html"
CRON_USER="www-data"

# Function to check if a service/process is running
check_service() {
    local service_name=$1
    if systemctl is-active --quiet "$service_name"; then
        echo -e "${GREEN}✅ $service_name: RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name: STOPPED${NC}"
        return 1
    fi
}

# Function to check log file status
check_log_file() {
    local log_file=$1
    local description=$2
    
    if [ -f "$log_file" ]; then
        local size=$(du -h "$log_file" | cut -f1)
        local modified=$(stat -c %Y "$log_file")
        local current=$(date +%s)
        local age=$((current - modified))
        local age_minutes=$((age / 60))
        
        if [ $age_minutes -lt 60 ]; then
            echo -e "${GREEN}✅ $description: ${size}, updated ${age_minutes}min ago${NC}"
        elif [ $age_minutes -lt 1440 ]; then
            local age_hours=$((age_minutes / 60))
            echo -e "${YELLOW}⚠️  $description: ${size}, updated ${age_hours}h ago${NC}"
        else
            local age_days=$((age_minutes / 1440))
            echo -e "${RED}❌ $description: ${size}, updated ${age_days}d ago${NC}"
        fi
        
        # Show last few lines if recent
        if [ $age_minutes -lt 30 ]; then
            echo -e "${CYAN}   Last activity:${NC}"
            tail -n 2 "$log_file" | sed 's/^/   /'
        fi
    else
        echo -e "${RED}❌ $description: LOG FILE NOT FOUND${NC}"
    fi
}

# Function to check cron job status
check_cron_jobs() {
    echo -e "${BLUE}📋 Checking cron jobs for user: $CRON_USER${NC}"
    
    if crontab -u "$CRON_USER" -l >/dev/null 2>&1; then
        local cron_count=$(crontab -u "$CRON_USER" -l | grep -v '^#' | grep -v '^$' | wc -l)
        echo -e "${GREEN}✅ Crontab exists: $cron_count active jobs${NC}"
        
        echo -e "${CYAN}   Active cron jobs:${NC}"
        crontab -u "$CRON_USER" -l | grep -v '^#' | grep -v '^$' | while read line; do
            echo "   $line"
        done
    else
        echo -e "${RED}❌ No crontab found for user: $CRON_USER${NC}"
    fi
}

# Function to test database connection
test_database() {
    echo -e "${BLUE}🗄️  Testing database connection...${NC}"
    
    if sudo -u www-data php -r "
        require_once '$WEB_ROOT/config/database.php';
        try {
            \$pdo->query('SELECT 1');
            echo 'Database connection successful\n';
            exit(0);
        } catch (Exception \$e) {
            echo 'Database connection failed: ' . \$e->getMessage() . '\n';
            exit(1);
        }
    " 2>/dev/null; then
        echo -e "${GREEN}✅ Database: CONNECTED${NC}"
    else
        echo -e "${RED}❌ Database: CONNECTION FAILED${NC}"
    fi
}

# Function to test Jellyfin API
test_jellyfin_api() {
    echo -e "${BLUE}🎬 Testing Jellyfin API...${NC}"
    
    if sudo -u www-data php -r "
        require_once '$WEB_ROOT/includes/JellyfinAPI.php';
        try {
            \$jellyfin = new JellyfinAPI();
            \$users = \$jellyfin->getUsers();
            if (\$users && is_array(\$users)) {
                echo 'Jellyfin API connection successful - ' . count(\$users) . ' users found\n';
                exit(0);
            } else {
                echo 'Jellyfin API returned invalid response\n';
                exit(1);
            }
        } catch (Exception \$e) {
            echo 'Jellyfin API connection failed: ' . \$e->getMessage() . '\n';
            exit(1);
        }
    " 2>/dev/null; then
        echo -e "${GREEN}✅ Jellyfin API: CONNECTED${NC}"
    else
        echo -e "${RED}❌ Jellyfin API: CONNECTION FAILED${NC}"
    fi
}

# Function to check disk space
check_disk_space() {
    echo -e "${BLUE}💾 Checking disk space...${NC}"
    
    local disk_usage=$(df -h /var/www/html | awk 'NR==2 {print $5}' | sed 's/%//')
    local disk_available=$(df -h /var/www/html | awk 'NR==2 {print $4}')
    
    if [ "$disk_usage" -lt 80 ]; then
        echo -e "${GREEN}✅ Disk space: ${disk_usage}% used, ${disk_available} available${NC}"
    elif [ "$disk_usage" -lt 90 ]; then
        echo -e "${YELLOW}⚠️  Disk space: ${disk_usage}% used, ${disk_available} available${NC}"
    else
        echo -e "${RED}❌ Disk space: ${disk_usage}% used, ${disk_available} available - CRITICAL${NC}"
    fi
}

# Function to show recent activity
show_recent_activity() {
    echo -e "${BLUE}📊 Recent system activity (last 30 minutes):${NC}"
    
    # Check for recent payments
    local recent_payments=$(sudo -u www-data php -r "
        require_once '$WEB_ROOT/config/database.php';
        try {
            \$stmt = \$pdo->query('SELECT COUNT(*) as count FROM payment_transaction WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)');
            echo \$stmt->fetch()['count'];
        } catch (Exception \$e) {
            echo '0';
        }
    " 2>/dev/null)
    
    # Check for recent verifications
    local recent_verifications=$(sudo -u www-data php -r "
        require_once '$WEB_ROOT/config/database.php';
        try {
            \$stmt = \$pdo->query('SELECT COUNT(*) as count FROM payment_transaction WHERE verified_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)');
            echo \$stmt->fetch()['count'];
        } catch (Exception \$e) {
            echo '0';
        }
    " 2>/dev/null)
    
    echo -e "   💰 New payments: $recent_payments"
    echo -e "   ✅ Verified payments: $recent_verifications"
    
    # Show system load
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    echo -e "   📊 System load:$load_avg"
}

# Function to run manual test
run_manual_test() {
    echo -e "${BLUE}🧪 Running manual health check test...${NC}"
    
    if sudo -u www-data php "$WEB_ROOT/cron/linux_health_check.php"; then
        echo -e "${GREEN}✅ Manual health check completed successfully${NC}"
    else
        echo -e "${RED}❌ Manual health check failed${NC}"
    fi
}

# Main monitoring function
main() {
    clear
    echo -e "${CYAN}🎬 Jellyfin by James - Linux Cron Monitor${NC}"
    echo -e "${CYAN}===========================================${NC}"
    echo -e "Production Server: ************* / embyjames.xyz"
    echo -e "Monitoring Time: $(date)"
    echo ""
    
    # Check system services
    echo -e "${BLUE}🔧 System Services${NC}"
    echo "----------------------------------------"
    check_service "cron"
    check_service "mysql" || check_service "mariadb"
    check_service "apache2" || check_service "nginx"
    echo ""
    
    # Check cron jobs
    echo -e "${BLUE}⏰ Cron Jobs Status${NC}"
    echo "----------------------------------------"
    check_cron_jobs
    echo ""
    
    # Check log files
    echo -e "${BLUE}📋 Log Files Status${NC}"
    echo "----------------------------------------"
    check_log_file "$LOG_DIR/payment_cron.log" "PayNoi Payment Check"
    check_log_file "$LOG_DIR/expiration_cron.log" "User Expiration Check"
    check_log_file "$LOG_DIR/manual_cron.log" "Manual Payment Check"
    check_log_file "$LOG_DIR/health_cron.log" "System Health Check"
    echo ""
    
    # Test connections
    echo -e "${BLUE}🔗 Connection Tests${NC}"
    echo "----------------------------------------"
    test_database
    test_jellyfin_api
    echo ""
    
    # Check system resources
    echo -e "${BLUE}💻 System Resources${NC}"
    echo "----------------------------------------"
    check_disk_space
    echo ""
    
    # Show recent activity
    show_recent_activity
    echo ""
    
    # Handle command line arguments
    case "${1:-}" in
        "test")
            echo -e "${BLUE}🧪 Manual Testing${NC}"
            echo "----------------------------------------"
            run_manual_test
            ;;
        "logs")
            echo -e "${BLUE}📋 Recent Log Entries${NC}"
            echo "----------------------------------------"
            echo -e "${CYAN}Payment Check (last 10 lines):${NC}"
            tail -n 10 "$LOG_DIR/payment_cron.log" 2>/dev/null || echo "No payment log found"
            echo ""
            echo -e "${CYAN}Health Check (last 10 lines):${NC}"
            tail -n 10 "$LOG_DIR/health_cron.log" 2>/dev/null || echo "No health log found"
            ;;
        "watch")
            echo -e "${YELLOW}👀 Watching logs in real-time (Ctrl+C to exit)...${NC}"
            tail -f "$LOG_DIR"/*.log 2>/dev/null
            ;;
        *)
            echo -e "${BLUE}💡 Available Commands${NC}"
            echo "----------------------------------------"
            echo "  $0           - Show status overview"
            echo "  $0 test      - Run manual health check"
            echo "  $0 logs      - Show recent log entries"
            echo "  $0 watch     - Watch logs in real-time"
            echo ""
            echo -e "${BLUE}🔧 Management Commands${NC}"
            echo "----------------------------------------"
            echo "  sudo systemctl restart cron"
            echo "  sudo crontab -u www-data -l"
            echo "  sudo tail -f $LOG_DIR/payment_cron.log"
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}🎬 Monitoring completed at $(date)${NC}"
}

# Run main function with all arguments
main "$@"
