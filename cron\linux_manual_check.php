<?php
// Linux Cron Job: Manual Payment Slip Verification
// Optimized for production Linux environment (*************)
//
// Usage: php /var/www/html/cron/linux_manual_check.php
// Cron: every 5 seconds - /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory to web root
$webRoot = '/var/www/html';
chdir($webRoot);

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Prevent concurrent execution using PID file
$pidFile = '/tmp/manual_check.pid';
if (file_exists($pidFile)) {
    $pid = trim(file_get_contents($pidFile));
    // Check if process is still running
    if ($pid && posix_kill($pid, 0)) {
        // Process is still running, exit silently
        exit(0);
    }
    // Process is dead, remove stale PID file
    unlink($pidFile);
}

// Write current PID
file_put_contents($pidFile, getmypid());

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Setup logging
$logFile = '/var/log/jellyfin/manual_cron.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

// Cleanup function
function cleanup() {
    global $pidFile;
    if (isset($pidFile) && file_exists($pidFile)) {
        unlink($pidFile);
    }
}

// Register cleanup
register_shutdown_function('cleanup');

writeLog("=== Manual Payment Check Started ===");

try {
    // Test database connection
    $pdo->query("SELECT 1");
    writeLog("✅ Database connection successful");
    
    // Get pending manual payments with uploaded slips (using PHP timezone)
    $cutoffTime = date('Y-m-d H:i:s', strtotime('-48 hours')); // 48 hours ago in Asia/Bangkok
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= ?
        ORDER BY pt.created_at ASC
        LIMIT 30
    ");
    $stmt->execute([$cutoffTime]);
    $pendingPayments = $stmt->fetchAll();
    
    writeLog("Found " . count($pendingPayments) . " pending manual payments");
    
    if (empty($pendingPayments)) {
        writeLog("No pending manual payments to process");
        exit(0);
    }
    
    $checkedCount = 0;
    $autoVerifiedCount = 0;
    $duplicateCount = 0;
    $errorCount = 0;
    $jellyfin = new JellyfinAPI();
    
    foreach ($pendingPayments as $payment) {
        $checkedCount++;
        writeLog("Checking payment ID {$payment['id']} - User: {$payment['username']}, Amount: {$payment['amount']} THB");
        
        try {
            // Check if slip image exists - handle duplicate path issue
            $slipImage = $payment['slip_image'];

            // Fix duplicate uploads/slips/ prefix
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $slipImage = 'uploads/slips/' . $slipImage;
            }

            $slipPath = $slipImage;

            if (!file_exists($slipPath)) {
                writeLog("❌ Slip image not found: {$slipPath}");
                continue;
            }
            
            // Get file info
            $fileSize = filesize($slipPath);
            $fileTime = filemtime($slipPath);
            $paymentTime = strtotime($payment['created_at']);
            
            // Basic validation
            $isValidSize = $fileSize > 10000 && $fileSize < 10000000; // 10KB - 10MB

            if (!$isValidSize) {
                writeLog("❌ Invalid file size for payment ID {$payment['id']}: {$fileSize} bytes");
                continue;
            }
            
            // Check for duplicate slips
            if (!empty($payment['slip_hash'])) {
                $dupStmt = $pdo->prepare("
                    SELECT COUNT(*) as count FROM payment_transactions
                    WHERE slip_hash = ? AND status = 'completed' AND id != ?
                ");
                $dupStmt->execute([$payment['slip_hash'], $payment['id']]);
                $duplicateCount_check = $dupStmt->fetch()['count'];
                
                if ($duplicateCount_check > 0) {
                    writeLog("❌ Duplicate slip detected for payment ID {$payment['id']}");
                    
                    // Mark as rejected
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'failed'
                        WHERE id = ?
                    ");
                    $updateStmt->execute([$payment['id']]);
                    $duplicateCount++;
                    continue;
                }
            }
            
            // Auto-verification criteria - ONLY for exact amount matches
            $autoVerify = false;

            // Get expected package amount for verification
            $expectedAmount = null;
            if ($payment['package_id']) {
                $packageStmt = $pdo->prepare("SELECT price FROM packages WHERE id = ?");
                $packageStmt->execute([$payment['package_id']]);
                $package = $packageStmt->fetch();
                if ($package) {
                    $expectedAmount = $package['price'];
                }
            }

            // STRICT amount verification - NO auto-verification for mismatched amounts
            if ($expectedAmount) {
                $amountDiff = abs($payment['amount'] - $expectedAmount);

                // Only auto-verify if amount matches EXACTLY within tolerance for random decimal (0.01-0.99)
                if ($amountDiff <= 1.00) {
                    // Additional strict checks for auto-verification
                    if ($payment['amount'] <= 100) {
                        // Small amounts - require EXACT match within 0.10 THB
                        if ($amountDiff <= 0.10) {
                            $autoVerify = true;
                            writeLog("✅ Auto-verifying small amount (exact match): {$payment['amount']} THB (expected: {$expectedAmount} THB, diff: {$amountDiff} THB)");
                        } else {
                            writeLog("⚠️  Small amount but difference too large: {$payment['amount']} THB vs {$expectedAmount} THB (diff: {$amountDiff} THB) - Manual review required");
                        }
                    } else {
                        // Larger amounts - require trusted user AND exact match
                        $historyStmt = $pdo->prepare("
                            SELECT COUNT(*) as verified_count FROM payment_transactions
                            WHERE user_id = ? AND status = 'completed'
                        ");
                        $historyStmt->execute([$payment['user_id']]);
                        $verifiedCount = $historyStmt->fetch()['verified_count'];

                        if ($verifiedCount >= 3 && $amountDiff <= 1.00) {
                            $autoVerify = true;
                            writeLog("✅ Auto-verifying trusted user (exact match): {$payment['username']} ({$verifiedCount} previous payments, amount: {$payment['amount']} THB, expected: {$expectedAmount} THB)");
                        } else {
                            writeLog("⚠️  Large amount or untrusted user: {$payment['username']} ({$verifiedCount} previous payments) - Manual review required");
                        }
                    }
                } else {
                    writeLog("❌ Amount mismatch - Payment: {$payment['amount']} THB, Expected: {$expectedAmount} THB, Difference: {$amountDiff} THB - REJECTED for auto-verification");
                }
            } else {
                writeLog("❌ No expected amount found for package - Manual review required");
            }

            if ($autoVerify) {
                try {
                    $pdo->beginTransaction();
                    
                    // Update payment status
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'completed',
                            paid_at = NOW()
                        WHERE id = ?
                    ");
                    $updateStmt->execute([$payment['id']]);
                    
                    // Find Jellyfin user
                    $jellyfinUsers = $jellyfin->getUsers();
                    $jellyfinUser = null;
                    
                    foreach ($jellyfinUsers as $ju) {
                        if ($ju['Name'] === $payment['username']) {
                            $jellyfinUser = $ju;
                            break;
                        }
                    }
                    
                    if ($jellyfinUser) {
                        // Enable user and grant access to all libraries with session limit
                        $maxSessions = $payment['max_simultaneous_sessions'] ?? 1;
                        $enableResult = $jellyfin->enableAllLibraries($jellyfinUser['Id'], $maxSessions);

                        if ($enableResult) {
                            
                            // Calculate expiration date using PHP timezone
                            $durationDays = $payment['duration_days'] ?? 30; // Default 30 days if not found
                            $startDate = date('Y-m-d H:i:s'); // Current time in Asia/Bangkok
                            $expiryDate = date('Y-m-d H:i:s', strtotime("+{$durationDays} days"));

                            // Update subscription status if package_id exists
                            if ($payment['package_id']) {
                                $subStmt = $pdo->prepare("
                                    UPDATE user_subscriptions
                                    SET start_date = COALESCE(start_date, ?),
                                        end_date = GREATEST(COALESCE(end_date, ?), ?),
                                        status = 'active',
                                        updated_at = ?
                                    WHERE id = ?
                                ");
                                $subStmt->execute([$startDate, $startDate, $expiryDate, $startDate, $payment['subscription_id']]);
                            }
                            
                            writeLog("✅ Auto-verified and enabled user: {$payment['username']}, Expires: {$expiryDate}");
                            $autoVerifiedCount++;
                            
                            // Log activity
                            $activityStmt = $pdo->prepare("
                                INSERT INTO activity_logs (user_id, action, details, created_at)
                                VALUES (?, 'payment_verified', ?, NOW())
                            ");
                            $activityStmt->execute([
                                $payment['user_id'],
                                "Manual payment auto-verified - Amount: {$payment['amount']} THB, Package: {$payment['package_name']}"
                            ]);
                            
                            $pdo->commit();
                        } else {
                            writeLog("❌ Failed to enable Jellyfin user: {$payment['username']}");
                            $pdo->rollback();
                            $errorCount++;
                        }
                    } else {
                        writeLog("❌ Jellyfin user not found: {$payment['username']}");
                        $pdo->rollback();
                        $errorCount++;
                    }
                    
                } catch (Exception $e) {
                    $pdo->rollback();
                    writeLog("❌ Error auto-verifying payment ID {$payment['id']}: " . $e->getMessage());
                    $errorCount++;
                }
            } else {
                // Mark for manual review if old enough
                $paymentAge = time() - strtotime($payment['created_at']);
                if ($paymentAge > 3600) { // 1 hour
                    writeLog("⚠️  Payment ID {$payment['id']} needs manual review (age: " . round($paymentAge/3600, 1) . " hours)");
                }
            }
            
        } catch (Exception $e) {
            writeLog("❌ Error processing payment ID {$payment['id']}: " . $e->getMessage());
            $errorCount++;
        }
    }
    
    writeLog("=== Manual Payment Check Completed ===");
    writeLog("Checked: {$checkedCount}, Auto-verified: {$autoVerifiedCount}, Duplicates: {$duplicateCount}, Errors: {$errorCount}");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
