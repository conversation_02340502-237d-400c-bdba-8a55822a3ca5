<?php
/**
 * Linux Cron Job: Manual Payment Slip Verification
 * Optimized for production Linux environment (*************)
 * 
 * Usage: php /var/www/html/cron/linux_manual_check.php
 * Cron: every 10 minutes - /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory to web root
$webRoot = '/var/www/html';
chdir($webRoot);

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Setup logging
$logFile = '/var/log/jellyfin/manual_cron.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

writeLog("=== Manual Payment Check Started ===");

try {
    // Test database connection
    $pdo->query("SELECT 1");
    writeLog("✅ Database connection successful");
    
    // Get pending manual payments with uploaded slips
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, p.name as package_name, p.duration_days, p.max_sessions
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        JOIN packages p ON pt.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)
        ORDER BY pt.created_at ASC
        LIMIT 30
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll();
    
    writeLog("Found " . count($pendingPayments) . " pending manual payments");
    
    if (empty($pendingPayments)) {
        writeLog("No pending manual payments to process");
        exit(0);
    }
    
    $checkedCount = 0;
    $autoVerifiedCount = 0;
    $duplicateCount = 0;
    $errorCount = 0;
    $jellyfin = new JellyfinAPI();
    
    foreach ($pendingPayments as $payment) {
        $checkedCount++;
        writeLog("Checking payment ID {$payment['id']} - User: {$payment['username']}, Amount: {$payment['amount']} THB");
        
        try {
            // Check if slip image exists
            $slipPath = "uploads/slips/" . $payment['slip_image'];
            
            if (!file_exists($slipPath)) {
                writeLog("❌ Slip image not found: {$slipPath}");
                continue;
            }
            
            // Get file info
            $fileSize = filesize($slipPath);
            $fileTime = filemtime($slipPath);
            $paymentTime = strtotime($payment['created_at']);
            
            // Basic validation
            $isValidSize = $fileSize > 10000 && $fileSize < 10000000; // 10KB - 10MB
            $isRecent = abs($fileTime - $paymentTime) < 3600; // Within 1 hour
            
            if (!$isValidSize) {
                writeLog("❌ Invalid file size for payment ID {$payment['id']}: {$fileSize} bytes");
                continue;
            }
            
            // Check for duplicate slips
            if (!empty($payment['slip_hash'])) {
                $dupStmt = $pdo->prepare("
                    SELECT COUNT(*) as count FROM payment_transactions
                    WHERE slip_hash = ? AND status = 'verified' AND id != ?
                ");
                $dupStmt->execute([$payment['slip_hash'], $payment['id']]);
                $duplicateCount_check = $dupStmt->fetch()['count'];
                
                if ($duplicateCount_check > 0) {
                    writeLog("❌ Duplicate slip detected for payment ID {$payment['id']}");
                    
                    // Mark as rejected
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'rejected',
                            admin_notes = 'Duplicate slip detected by auto-check'
                        WHERE id = ?
                    ");
                    $updateStmt->execute([$payment['id']]);
                    $duplicateCount++;
                    continue;
                }
            }
            
            // Auto-verification criteria
            $autoVerify = false;
            
            // Small amounts (≤ 100 THB) - auto verify
            if ($payment['amount'] <= 100) {
                $autoVerify = true;
                writeLog("✅ Auto-verifying small amount: {$payment['amount']} THB");
            } else {
                // Check user history for trusted users
                $historyStmt = $pdo->prepare("
                    SELECT COUNT(*) as verified_count FROM payment_transactions
                    WHERE user_id = ? AND status = 'verified'
                ");
                $historyStmt->execute([$payment['user_id']]);
                $verifiedCount = $historyStmt->fetch()['verified_count'];
                
                if ($verifiedCount >= 2) {
                    $autoVerify = true;
                    writeLog("✅ Auto-verifying trusted user: {$payment['username']} ({$verifiedCount} previous payments)");
                }
            }
            
            if ($autoVerify && $isRecent) {
                try {
                    $pdo->beginTransaction();
                    
                    // Update payment status
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET status = 'verified',
                            verified_at = NOW(),
                            admin_notes = 'Auto-verified by cron (manual slip)'
                        WHERE id = ?
                    ");
                    $updateStmt->execute([$payment['id']]);
                    
                    // Find Jellyfin user
                    $jellyfinUsers = $jellyfin->getUsers();
                    $jellyfinUser = null;
                    
                    foreach ($jellyfinUsers as $ju) {
                        if ($ju['Name'] === $payment['username']) {
                            $jellyfinUser = $ju;
                            break;
                        }
                    }
                    
                    if ($jellyfinUser) {
                        // Enable user
                        $enableResult = $jellyfin->setUserEnabled($jellyfinUser['Id'], true);
                        
                        if ($enableResult) {
                            // Set session limit
                            $maxSessions = $payment['max_sessions'] ?? 1;
                            $jellyfin->setUserSessionLimit($jellyfinUser['Id'], $maxSessions);
                            
                            // Calculate expiration date
                            $expiryDate = date('Y-m-d H:i:s', strtotime("+{$payment['duration_days']} days"));
                            
                            // Update or create subscription
                            $subStmt = $pdo->prepare("
                                INSERT INTO user_subscriptions (user_id, package_id, start_date, end_date, status)
                                VALUES (?, ?, NOW(), ?, 'active')
                                ON DUPLICATE KEY UPDATE
                                end_date = GREATEST(end_date, VALUES(end_date)),
                                status = 'active',
                                updated_at = NOW()
                            ");
                            $subStmt->execute([$payment['user_id'], $payment['package_id'], $expiryDate]);
                            
                            writeLog("✅ Auto-verified and enabled user: {$payment['username']}, Expires: {$expiryDate}");
                            $autoVerifiedCount++;
                            
                            // Log activity
                            $activityStmt = $pdo->prepare("
                                INSERT INTO activity_logs (user_id, action, details, created_at)
                                VALUES (?, 'payment_verified', ?, NOW())
                            ");
                            $activityStmt->execute([
                                $payment['user_id'],
                                "Manual payment auto-verified - Amount: {$payment['amount']} THB, Package: {$payment['package_name']}"
                            ]);
                            
                            $pdo->commit();
                        } else {
                            writeLog("❌ Failed to enable Jellyfin user: {$payment['username']}");
                            $pdo->rollback();
                            $errorCount++;
                        }
                    } else {
                        writeLog("❌ Jellyfin user not found: {$payment['username']}");
                        $pdo->rollback();
                        $errorCount++;
                    }
                    
                } catch (Exception $e) {
                    $pdo->rollback();
                    writeLog("❌ Error auto-verifying payment ID {$payment['id']}: " . $e->getMessage());
                    $errorCount++;
                }
            } else {
                // Mark for manual review if old enough
                $paymentAge = time() - strtotime($payment['created_at']);
                if ($paymentAge > 3600) { // 1 hour
                    $updateStmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET admin_notes = CONCAT(COALESCE(admin_notes, ''), ' | Needs manual review - Auto-verification criteria not met')
                        WHERE id = ?
                    ");
                    $updateStmt->execute([$payment['id']]);
                    writeLog("⚠️  Payment ID {$payment['id']} marked for manual review");
                }
            }
            
        } catch (Exception $e) {
            writeLog("❌ Error processing payment ID {$payment['id']}: " . $e->getMessage());
            $errorCount++;
        }
    }
    
    writeLog("=== Manual Payment Check Completed ===");
    writeLog("Checked: {$checkedCount}, Auto-verified: {$autoVerifiedCount}, Duplicates: {$duplicateCount}, Errors: {$errorCount}");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
