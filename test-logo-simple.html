<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logo - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .logo-test {
            background: #1a1a1a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .logo-test h3 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        .logo-inline {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
            color: #fff;
        }
        .error-msg {
            color: #ff6b6b;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #FFD700;">🎬 Logo Test Page</h1>
        
        <div class="logo-test">
            <h3>1. SVG Logo Test</h3>
            <img src="assets/images/logo.svg" alt="Cinema Logo SVG" style="height: 50px; width: auto;" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error-msg" style="display: none;">❌ SVG Logo ไม่สามารถโหลดได้</div>
        </div>
        
        <div class="logo-test">
            <h3>2. JPG Logo Test (Fallback)</h3>
            <img src="assets/images/logo.jpg" alt="Cinema Logo JPG" style="height: 50px; width: auto;" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error-msg" style="display: none;">❌ JPG Logo ไม่สามารถโหลดได้</div>
        </div>
        
        <div class="logo-test">
            <h3>3. Text-Only Logo (Always Works)</h3>
            <div class="logo-inline">
                <span style="background: linear-gradient(45deg, #FFD700, #FFA500); 
                           -webkit-background-clip: text; 
                           -webkit-text-fill-color: transparent; 
                           background-clip: text;
                           font-size: 2rem;">🎬</span>
                <span>Jellyfin by James</span>
                <small style="color: #FFD700; font-size: 0.7em;">CINEMA</small>
            </div>
        </div>
        
        <div class="logo-test">
            <h3>4. CSS-Only Cinema Logo</h3>
            <div style="display: inline-flex; align-items: center; gap: 15px;">
                <!-- Film Strip -->
                <div style="width: 60px; height: 30px; background: #1a1a1a; border: 2px solid #FFD700; border-radius: 4px; position: relative;">
                    <!-- Perforations -->
                    <div style="position: absolute; left: 2px; top: 4px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; left: 2px; top: 8px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; left: 2px; top: 12px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; left: 2px; top: 16px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; left: 2px; top: 20px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    
                    <div style="position: absolute; right: 2px; top: 4px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; right: 2px; top: 8px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; right: 2px; top: 12px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; right: 2px; top: 16px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    <div style="position: absolute; right: 2px; top: 20px; width: 3px; height: 2px; background: #333; border-radius: 1px;"></div>
                    
                    <!-- Play Button -->
                    <div style="position: absolute; left: 12px; top: 8px; width: 0; height: 0; 
                               border-left: 8px solid #FFD700; 
                               border-top: 6px solid transparent; 
                               border-bottom: 6px solid transparent;"></div>
                    
                    <!-- J Letter -->
                    <div style="position: absolute; right: 12px; top: 6px; color: #FFD700; font-weight: bold; font-size: 14px;">J</div>
                </div>
                
                <div style="display: flex; flex-direction: column; align-items: flex-start;">
                    <span style="font-size: 1.2rem; font-weight: bold;">Jellyfin by James</span>
                    <small style="color: #FFD700; font-size: 0.7em; letter-spacing: 0.1em;">CINEMA</small>
                </div>
            </div>
        </div>
        
        <div class="logo-test">
            <h3>5. Path Test</h3>
            <p>Current page URL: <span id="currentUrl"></span></p>
            <p>Logo SVG path: <code>assets/images/logo.svg</code></p>
            <p>Logo JPG path: <code>assets/images/logo.jpg</code></p>
            
            <div style="margin-top: 15px;">
                <button onclick="testPaths()" style="background: #FFD700; color: #000; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    Test Image Paths
                </button>
            </div>
            <div id="pathResults" style="margin-top: 10px;"></div>
        </div>
        
        <div style="margin-top: 40px;">
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                ← กลับไปหน้าแรก
            </a>
        </div>
    </div>
    
    <script>
        document.getElementById('currentUrl').textContent = window.location.href;
        
        function testPaths() {
            const results = document.getElementById('pathResults');
            results.innerHTML = '<p>กำลังทดสอบ...</p>';
            
            const paths = [
                'assets/images/logo.svg',
                'assets/images/logo.jpg',
                '/assets/images/logo.svg',
                '/assets/images/logo.jpg'
            ];
            
            let resultHtml = '';
            let completed = 0;
            
            paths.forEach(path => {
                const img = new Image();
                img.onload = function() {
                    resultHtml += `<p style="color: #4CAF50;">✅ ${path} - OK (${this.width}x${this.height})</p>`;
                    completed++;
                    if (completed === paths.length) {
                        results.innerHTML = resultHtml;
                    }
                };
                img.onerror = function() {
                    resultHtml += `<p style="color: #ff6b6b;">❌ ${path} - ไม่พบไฟล์</p>`;
                    completed++;
                    if (completed === paths.length) {
                        results.innerHTML = resultHtml;
                    }
                };
                img.src = path;
            });
        }
    </script>
</body>
</html>
