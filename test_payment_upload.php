<?php
// Test payment slip upload exactly like the real system
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
}

$message = '';
$message_type = 'info';

// Create a test subscription and payment transaction if needed
if (isset($_GET['create_test'])) {
    try {
        $pdo->beginTransaction();
        
        // Create test subscription
        $stmt = $pdo->prepare("
            INSERT INTO user_subscriptions (user_id, package_id, payment_amount, status, created_at)
            VALUES (?, 1, 100.00, 'pending', NOW())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $subscription_id = $pdo->lastInsertId();
        
        // Create test payment transaction
        $transaction_ref = 'TEST' . date('YmdHis') . str_pad($subscription_id, 4, '0', STR_PAD_LEFT);
        $stmt = $pdo->prepare("
            INSERT INTO payment_transactions (subscription_id, user_id, amount, transaction_ref, status, created_at)
            VALUES (?, ?, 100.00, ?, 'pending', NOW())
        ");
        $stmt->execute([$subscription_id, $_SESSION['user_id'], $transaction_ref]);
        
        $pdo->commit();
        $message = "สร้างรายการทดสอบสำเร็จ! Subscription ID: {$subscription_id}";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $message = "เกิดข้อผิดพลาด: " . $e->getMessage();
        $message_type = 'error';
    }
}

// Handle slip upload (same logic as payment.php)
if ($_POST && isset($_POST['upload_slip']) && isset($_FILES['slip_image'])) {
    try {
        $file = $_FILES['slip_image'];
        
        // Check upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในระบบ (upload_max_filesize)',
                UPLOAD_ERR_FORM_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในฟอร์ม (MAX_FILE_SIZE)',
                UPLOAD_ERR_PARTIAL => 'ไฟล์อัปโหลดไม่สมบูรณ์ กรุณาลองใหม่อีกครั้ง',
                UPLOAD_ERR_NO_FILE => 'กรุณาเลือกไฟล์สลิปการโอนเงิน',
                UPLOAD_ERR_NO_TMP_DIR => 'ไม่พบโฟลเดอร์ชั่วคราวในระบบ',
                UPLOAD_ERR_CANT_WRITE => 'ไม่สามารถเขียนไฟล์ลงดิสก์ได้',
                UPLOAD_ERR_EXTENSION => 'การอัปโหลดถูกหยุดโดย PHP extension'
            ];
            throw new Exception($upload_errors[$file['error']] ?? 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์ (Error code: ' . $file['error'] . ')');
        }

        // Validate file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $file_type = $file['type'];
        if (!in_array($file_type, $allowed_types)) {
            throw new Exception('ประเภทไฟล์ไม่ถูกต้อง กรุณาอัปโหลดไฟล์รูปภาพ (JPG, PNG, GIF) เท่านั้น<br>ประเภทไฟล์ที่ได้รับ: ' . htmlspecialchars($file_type));
        }

        // Validate file size (5MB max)
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('ไฟล์มีขนาดใหญ่เกินไป กรุณาอัปโหลดไฟล์ที่มีขนาดไม่เกิน 5MB<br>ขนาดไฟล์ปัจจุบัน: ' . number_format($file['size'] / 1024 / 1024, 2) . ' MB');
        }

        // Validate file extension
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        if (!in_array($file_extension, $allowed_extensions)) {
            throw new Exception('นามสกุลไฟล์ไม่ถูกต้อง กรุณาใช้ไฟล์ .jpg, .jpeg, .png หรือ .gif เท่านั้น');
        }

        // Check if file is actually an image
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            throw new Exception('ไฟล์ที่อัปโหลดไม่ใช่รูปภาพที่ถูกต้อง กรุณาตรวจสอบไฟล์และลองใหม่อีกครั้ง');
        }

        // Create upload directory if it doesn't exist
        $upload_dir = 'uploads/slips/';
        if (!is_dir($upload_dir)) {
            if (!mkdir($upload_dir, 0755, true)) {
                throw new Exception('ไม่สามารถสร้างโฟลเดอร์สำหรับเก็บไฟล์ได้ กรุณาติดต่อผู้ดูแลระบบ');
            }
        }

        // Check directory permissions
        if (!is_writable($upload_dir)) {
            throw new Exception('ไม่สามารถเขียนไฟล์ในโฟลเดอร์ได้ กรุณาติดต่อผู้ดูแลระบบ');
        }

        // Generate unique filename
        $new_filename = 'slip_' . $_SESSION['user_id'] . '_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        // Calculate file hash for duplicate detection
        $file_hash = hash_file('sha256', $file['tmp_name']);

        // Check if this slip hash has been used before
        $stmt = $pdo->prepare("
            SELECT pt.id, pt.slip_image, u.username
            FROM payment_transactions pt
            JOIN users u ON pt.user_id = u.id
            WHERE pt.slip_hash = ? AND pt.status IN ('completed', 'verified')
            LIMIT 1
        ");
        $stmt->execute([$file_hash]);
        $duplicate_slip = $stmt->fetch();

        if ($duplicate_slip) {
            throw new Exception('สลิปนี้เคยถูกใช้แล้วโดย: ' . htmlspecialchars($duplicate_slip['username']) . '<br>กรุณาแนบสลิปใหม่อีกรอบ');
        }

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
            throw new Exception('ไม่สามารถบันทึกไฟล์ได้ กรุณาลองใหม่อีกครั้ง หรือติดต่อผู้ดูแลระบบ');
        }

        // Find pending payment transaction
        $stmt = $pdo->prepare("
            SELECT id FROM payment_transactions 
            WHERE user_id = ? AND status = 'pending'
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $payment_transaction = $stmt->fetch();

        if (!$payment_transaction) {
            unlink($upload_path);
            throw new Exception('ไม่พบรายการชำระเงินที่รอดำเนินการ');
        }

        // Update payment transaction
        $stmt = $pdo->prepare("
            UPDATE payment_transactions
            SET slip_image = ?, slip_hash = ?, status = 'pending_verification', updated_at = NOW()
            WHERE id = ?
        ");
        $update_result = $stmt->execute([$upload_path, $file_hash, $payment_transaction['id']]);

        if (!$update_result) {
            unlink($upload_path);
            throw new Exception('ไม่สามารถอัปเดตข้อมูลการชำระเงินได้');
        }

        $message = 'อัปโหลดสลิปเรียบร้อยแล้ว กรุณารอการอนุมัติจาก Admin';
        $message_type = 'success';

        log_activity($_SESSION['user_id'], 'slip_uploaded', "Uploaded payment slip: {$new_filename}");

    } catch (Exception $e) {
        $message = $e->getMessage();
        $message_type = 'error';
        error_log("Test slip upload error for user {$_SESSION['user_id']}: " . $e->getMessage());
    }
}

// Get current pending payments
$stmt = $pdo->prepare("
    SELECT pt.*, us.id as subscription_id, p.name as package_name
    FROM payment_transactions pt
    LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
    LEFT JOIN packages p ON us.package_id = p.id
    WHERE pt.user_id = ?
    ORDER BY pt.created_at DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$payments = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการอัปโหลดสลิป</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .flash-message {
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            border-radius: 6px;
            font-weight: 500;
            border-left: 4px solid;
        }
        .flash-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border-left-color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }
        .flash-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-left-color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        .flash-info {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
            border-left-color: #17a2b8;
            border: 1px solid rgba(23, 162, 184, 0.3);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
        .status-pending_verification {
            color: #17a2b8;
            font-weight: bold;
        }
        .status-completed {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ทดสอบการอัปโหลดสลิป</h1>
        
        <?php if ($message): ?>
            <div class="flash-message flash-<?php echo $message_type; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div style="margin: 20px 0;">
            <a href="?create_test=1">
                <button type="button" class="btn-success">สร้างรายการทดสอบ</button>
            </a>
        </div>
        
        <h3>📋 รายการชำระเงินปัจจุบัน</h3>
        <?php if ($payments): ?>
            <table>
                <tr>
                    <th>ID</th>
                    <th>Package</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Slip</th>
                    <th>Created</th>
                </tr>
                <?php foreach ($payments as $payment): ?>
                <tr>
                    <td><?php echo $payment['id']; ?></td>
                    <td><?php echo htmlspecialchars($payment['package_name'] ?? 'N/A'); ?></td>
                    <td><?php echo number_format($payment['amount'], 2); ?> บาท</td>
                    <td class="status-<?php echo $payment['status']; ?>">
                        <?php echo $payment['status']; ?>
                    </td>
                    <td><?php echo $payment['slip_image'] ? '✅' : '❌'; ?></td>
                    <td><?php echo $payment['created_at']; ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        <?php else: ?>
            <p>ไม่มีรายการชำระเงิน</p>
        <?php endif; ?>
        
        <hr style="margin: 30px 0;">
        
        <h3>📤 ทดสอบอัปโหลดสลิป</h3>
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="slip_image">เลือกไฟล์สลิปการโอนเงิน:</label>
                <input type="file" id="slip_image" name="slip_image" accept="image/*" required>
                <small style="color: #666;">รองรับไฟล์ JPG, PNG, GIF ขนาดไม่เกิน 5MB</small>
            </div>
            <button type="submit" name="upload_slip">อัปโหลดสลิป</button>
        </form>
    </div>
</body>
</html>
