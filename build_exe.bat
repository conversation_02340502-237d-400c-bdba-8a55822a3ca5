@echo off
title Build Jellyfin Admin Tools Executable
echo.
echo ========================================
echo    Building Admin Tools Executable
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Install PyInstaller if not installed
echo Installing PyInstaller...
pip install pyinstaller

REM Install required packages
echo Installing required packages...
pip install -r requirements.txt

REM Create icon if not exists
if not exist "icon.ico" (
    echo Creating default icon...
    echo. > icon.ico
)

REM Build executable
echo Building executable...
pyinstaller --onefile --windowed --name "JellyfinAdminTools" --distpath=. AdminTools.py

if errorlevel 1 (
    echo.
    echo Error: Failed to build executable
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build completed successfully!
echo ========================================
echo.
echo Executable location: dist\JellyfinAdminTools.exe
echo.
echo You can now run the executable without Python installed.
echo.
pause
