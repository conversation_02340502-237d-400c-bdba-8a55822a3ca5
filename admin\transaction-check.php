<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle manual transaction check via external script
if ($_POST && isset($_POST['run_check'])) {
    $output = [];
    $return_var = 0;

    // Auto-detect PHP path
    $php_exe = null;
    $php_search_paths = [
        'C:\\laragon\\bin\\php\\php-*\\php.exe'
    ];

    // Find PHP in Laragon
    foreach (glob('C:\\laragon\\bin\\php\\php-*\\php.exe') as $path) {
        if (file_exists($path)) {
            $php_exe = $path;
            break;
        }
    }

    // Fallback to other common locations
    if (!$php_exe) {
        $fallback_paths = [
            'C:\\xampp\\php\\php.exe',
            'C:\\wamp\\bin\\php\\php8.3.0\\php.exe',
            PHP_BINARY
        ];

        foreach ($fallback_paths as $path) {
            if (file_exists($path)) {
                $php_exe = $path;
                break;
            }
        }
    }

    if (!$php_exe) {
        $message = 'ไม่พบ PHP executable กรุณาใช้ปุ่ม "รันแบบ Inline" แทน';
        $message_type = 'error';
    } else {
        // Run the PayNoi sync script
        $root_dir = dirname(__DIR__);
        $script_path = $root_dir . DIRECTORY_SEPARATOR . 'paynoi-transactions.php';
        $command = '"' . $php_exe . '" "' . $script_path . '" 2>&1';

        // Change to root directory before running
        $old_cwd = getcwd();
        chdir($root_dir);

        exec($command, $output, $return_var);

        // Change back to original directory
        chdir($old_cwd);

        if ($return_var === 0) {
            $message = 'ตรวจสอบธุรกรรม PayNoi เสร็จสิ้น (External): ' . implode('<br>', $output);
            $message_type = 'success';
        } else {
            $message = 'เกิดข้อผิดพลาดในการตรวจสอบธุรกรรม (External): ' . implode('<br>', $output);
            $message_type = 'error';
        }
    }
}

// Handle inline transaction check using PayNoi API
if ($_POST && isset($_POST['run_check_inline'])) {
    try {
        require_once '../includes/PayNoiAPIClass.php';

        // Initialize PayNoi API with config values
        $paynoiAPI = new PayNoiAPI(PAYNOI_API_KEY, PAYNOI_RECORD_KEY);

        // Check if payments table exists
        $tableCheck = $pdo->query("SHOW TABLES LIKE 'payments'")->rowCount();
        if ($tableCheck == 0) {
            $message = 'ไม่พบตาราง payments กรุณาสร้างตารางก่อนใช้งาน (ใช้ไฟล์ create_payments_table.sql)';
            $message_type = 'error';
        } else {
            // Get pending payments from last 24 hours
            $stmt = $pdo->prepare("
                SELECT * FROM payments
                WHERE status = 'pending'
                AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            $pendingPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $processedCount = 0;
            $verifiedCount = 0;

            if (empty($pendingPayments)) {
                $message = 'ไม่มีการชำระเงินที่รอตรวจสอบ';
                $message_type = 'info';
            } else {
                // Get transactions from PayNoi API
                $transactions = $paynoiAPI->getTransactions();

                if (!$transactions || !isset($transactions['data'])) {
                    $message = 'ไม่สามารถดึงข้อมูลจาก PayNoi API ได้';
                    $message_type = 'error';
                } else {
                    foreach ($pendingPayments as $payment) {
                        $paymentAmount = floatval($payment['amount']);
                        $paymentTime = strtotime($payment['created_at']);

                        // Look for matching transaction
                        foreach ($transactions['data'] as $transaction) {
                            $transactionAmount = floatval($transaction['amount'] ?? 0);
                            $transactionDate = $transaction['date'] ?? '';
                            $transactionTime = strtotime($transactionDate);

                            // Skip if not incoming transaction
                            if ($transactionAmount <= 0) {
                                continue;
                            }

                            // Check amount match (within 1.00 tolerance for random decimal 0.01-0.99)
                            $amountDiff = abs($transactionAmount - $paymentAmount);
                            if ($amountDiff > 1.00) {
                                continue;
                            }

                            // Check time match (within 2 hours)
                            $timeDiff = abs($transactionTime - $paymentTime);
                            if ($timeDiff > 7200) { // 2 hours
                                continue;
                            }

                            // Transaction matches! Update payment status
                            try {
                                $pdo->beginTransaction();

                                $updateStmt = $pdo->prepare("
                                    UPDATE payments
                                    SET status = 'verified',
                                        transaction_id = ?,
                                        verified_at = NOW(),
                                        paynoi_data = ?
                                    WHERE id = ?
                                ");

                                $paynoiData = json_encode($transaction);
                                $updateStmt->execute([
                                    $transaction['trans_id'] ?? '',
                                    $paynoiData,
                                    $payment['id']
                                ]);

                                $pdo->commit();
                                $verifiedCount++;

                                // TODO: Enable user in Jellyfin if needed
                                // TODO: Add subscription days to user

                                break; // Found match, stop looking for this payment

                            } catch (Exception $e) {
                                $pdo->rollBack();
                                error_log('Failed to verify payment: ' . $e->getMessage());
                            }
                        }
                        $processedCount++;
                    }

                    $message = "ตรวจสอบเสร็จสิ้น: ตรวจสอบ {$processedCount} รายการ, ยืนยัน {$verifiedCount} รายการ";
                    $message_type = $verifiedCount > 0 ? 'success' : 'info';
                }
            }
        }

    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาดในการตรวจสอบธุรกรรม: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get recent PayNoi sync logs
$logFile = '../logs/php_errors.log';
$logContent = '';
if (file_exists($logFile)) {
    $logLines = file($logFile);
    // Filter for PayNoi related logs
    $paynoiLogs = array_filter($logLines, function($line) {
        return strpos($line, 'PayNoi') !== false;
    });
    $logContent = implode('', array_slice($paynoiLogs, -20)); // Last 20 PayNoi logs
}

// Get pending payments from new payments table
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'payments'")->rowCount();
    if ($tableCheck > 0) {
        $stmt = $pdo->prepare("
            SELECT p.*, u.username
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.id
            WHERE p.status IN ('pending', 'verified')
            ORDER BY p.created_at DESC
            LIMIT 50
        ");
        $stmt->execute();
        $pendingPayments = $stmt->fetchAll();
    } else {
        $pendingPayments = [];
        if (!isset($message)) {
            $message = 'ไม่พบตาราง payments กรุณาสร้างตารางก่อนใช้งาน';
            $message_type = 'warning';
        }
    }
} catch (Exception $e) {
    if (!isset($message)) {
        $message = 'เกิดข้อผิดพลาดในการดึงข้อมูลการชำระเงิน: ' . $e->getMessage();
        $message_type = 'error';
    }
    $pendingPayments = [];
}

// Get recent verified payments
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'payments'")->rowCount();
    if ($tableCheck > 0) {
        $stmt = $pdo->prepare("
            SELECT p.*, u.username
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.id
            WHERE p.status = 'verified'
            AND p.verified_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY p.verified_at DESC
            LIMIT 20
        ");
        $stmt->execute();
        $recentApproved = $stmt->fetchAll();
    } else {
        $recentApproved = [];
    }
} catch (Exception $e) {
    if (!isset($message)) {
        $message = 'เกิดข้อผิดพลาดในการดึงข้อมูลการชำระเงินที่ยืนยัน: ' . $e->getMessage();
        $message_type = 'error';
    }
    $recentApproved = [];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบธุรกรรม - Jellyfin by James</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .log-container {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid #333;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-pending { background: #ffc107; color: #000; }
        .status-pending_verification { background: #17a2b8; color: white; }
        .status-completed { background: #28a745; color: white; }
        .status-manual_review { background: #dc3545; color: white; }
        .status-cancelled { background: #6c757d; color: white; }
        .payments-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 1rem 0;
        }
        .payments-table th,
        .payments-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #333;
            font-size: 0.9rem;
        }
        .payments-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .control-panel {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #333;
            margin: 2rem 0;
        }
        .btn-secondary {
            background: rgba(108, 117, 125, 0.8);
            border: 1px solid #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: rgba(108, 117, 125, 1);
            border-color: #545b62;
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">ตรวจสอบธุรกรรม</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">ตรวจสอบธุรกรรม PayNoi</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">ตรวจสอบและยืนยันการชำระเงินผ่าน PayNoi API อัตโนมัติ</p>
            </div>

            <?php if ($message): ?>
                <div class="flash-message flash-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Control Panel -->
            <div class="control-panel fade-in-scroll">
                <h3>การควบคุม</h3>
                <p style="color: #ccc; margin-bottom: 1rem;">
                    ระบบจะตรวจสอบธุรกรรม PayNoi อัตโนมัติทุก 5 วินาที หรือคุณสามารถรันการตรวจสอบด้วยตนเองได้<br>
                    <strong>แนะนำ:</strong> ใช้ปุ่ม "รันแบบ Inline" เพื่อความเสถียร หากไม่ทำงานให้ลองปุ่ม "External"
                </p>
                
                <form method="POST" style="margin-bottom: 1rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <button type="submit" name="run_check_inline" class="btn btn-primary noir-button">
                            🔄 รันการตรวจสอบ PayNoi ทันที (แนะนำ)
                        </button>
                        <button type="submit" name="run_check" class="btn btn-secondary noir-button">
                            🔄 รันการตรวจสอบ PayNoi (External)
                        </button>
                    </div>
                    <small style="color: #888; display: block; margin-top: 0.5rem;">
                        หากปุ่มแรกไม่ทำงาน ให้ลองปุ่มที่สอง หรือตรวจสอบ logs ด้านล่าง
                    </small>
                </form>
                
                <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid #ffc107; padding: 1rem; border-radius: 4px;">
                    <h4 style="color: #ffc107; margin-bottom: 0.5rem;">วิธีการทำงาน PayNoi:</h4>
                    <ol style="color: #ccc; font-size: 0.9rem;">
                        <li>ผู้ใช้ทำการชำระเงินและบันทึกข้อมูลในระบบ</li>
                        <li>ระบบดึงข้อมูลธุรกรรมจาก PayNoi API ทุก 5 วินาที</li>
                        <li>จับคู่จำนวนเงินและเวลาที่ตรงกัน (ความคลาดเคลื่อน ±2 ชั่วโมง)</li>
                        <li>หากตรงกัน จะยืนยันการชำระเงินอัตโนมัติ</li>
                        <li>ระบบจะเปิดใช้งาน Jellyfin และเพิ่มวันใช้งาน</li>
                    </ol>
                    <div style="margin-top: 1rem; text-align: center;">
                        <a href="../paynoi-transactions.php" style="color: #ffc107; text-decoration: underline;">
                            📊 ดู PayNoi Script
                        </a> |
                        <a href="../run_paynoi_cron.bat" style="color: #28a745; text-decoration: underline;" download>
                            ⬇️ ดาวน์โหลด Cron Script
                        </a> |
                        <a href="../view_paynoi_logs.bat" style="color: #17a2b8; text-decoration: underline;" download>
                            📋 ดาวน์โหลด Log Viewer
                        </a>
                    </div>
                </div>
            </div>

            <!-- Pending Payments -->
            <div class="fade-in-scroll">
                <h3>การชำระเงินที่รอตรวจสอบ (<?php echo count($pendingPayments); ?>)</h3>
                <?php if (!empty($pendingPayments)): ?>
                    <div style="overflow-x: auto;">
                        <table class="payments-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>ผู้ใช้</th>
                                    <th>จำนวนเงิน</th>
                                    <th>สถานะ</th>
                                    <th>Transaction ID</th>
                                    <th>วันที่สร้าง</th>
                                    <th>วันที่ยืนยัน</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingPayments as $payment): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($payment['id']); ?></td>
                                        <td><?php echo htmlspecialchars($payment['username'] ?? 'N/A'); ?></td>
                                        <td><?php echo number_format($payment['amount'] ?? 0, 2); ?> บาท</td>
                                        <td>
                                            <span class="status-badge status-<?php echo $payment['status']; ?>">
                                                <?php
                                                switch($payment['status']) {
                                                    case 'pending': echo 'รอตรวจสอบ'; break;
                                                    case 'verified': echo 'ยืนยันแล้ว'; break;
                                                    case 'failed': echo 'ล้มเหลว'; break;
                                                    case 'cancelled': echo 'ยกเลิก'; break;
                                                    default: echo $payment['status']; break;
                                                }
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($payment['transaction_id']): ?>
                                                <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                            <?php else: ?>
                                                <span style="color: #888;">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($payment['created_at'])); ?></td>
                                        <td>
                                            <?php if ($payment['verified_at']): ?>
                                                <?php echo date('d/m/Y H:i', strtotime($payment['verified_at'])); ?>
                                            <?php else: ?>
                                                <span style="color: #888;">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p style="color: #ccc;">ไม่มีการชำระเงินที่รอตรวจสอบ</p>
                <?php endif; ?>
            </div>

            <!-- Recent Verified -->
            <div class="fade-in-scroll">
                <h3>การชำระเงินที่ยืนยันล่าสุด (24 ชั่วโมง)</h3>
                <?php if (!empty($recentApproved)): ?>
                    <div style="overflow-x: auto;">
                        <table class="payments-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>ผู้ใช้</th>
                                    <th>จำนวนเงิน</th>
                                    <th>Transaction ID</th>
                                    <th>วันที่ยืนยัน</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentApproved as $payment): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($payment['id']); ?></td>
                                        <td><?php echo htmlspecialchars($payment['username'] ?? 'N/A'); ?></td>
                                        <td><?php echo number_format($payment['amount'] ?? 0, 2); ?> บาท</td>
                                        <td>
                                            <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($payment['transaction_id'] ?? '-'); ?></code>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($payment['verified_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p style="color: #ccc;">ไม่มีการชำระเงินที่ยืนยันในช่วง 24 ชั่วโมงที่ผ่านมา</p>
                <?php endif; ?>
            </div>

            <!-- PayNoi Sync Logs -->
            <div class="fade-in-scroll">
                <h3>PayNoi Sync Logs ล่าสุด</h3>
                <div class="log-container">
                    <?php echo $logContent ? htmlspecialchars($logContent) : 'ยังไม่มี PayNoi logs'; ?>
                </div>
                <p style="color: #888; font-size: 0.9rem; margin-top: 0.5rem;">
                    💡 หากต้องการดู logs แบบ real-time ให้รัน <code>view_paynoi_logs.bat</code>
                </p>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
