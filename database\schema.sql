-- Jellyfin Registration System Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS jellyfin_registration CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE jellyfin_registration;

-- Users table for registration system
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_email_verification_token (email_verification_token),
    INDEX idx_password_reset_token (password_reset_token)
);

-- Jellyfin users table to track Jellyfin account details
CREATE TABLE jellyfin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    jellyfin_user_id VARCHAR(255) NOT NULL UNIQUE,
    jellyfin_username VARCHAR(50) NOT NULL,
    jellyfin_password VARCHAR(255),
    is_jellyfin_admin BOOLEAN DEFAULT FALSE,
    jellyfin_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    jellyfin_last_activity TIMESTAMP NULL,
    max_simultaneous_sessions INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_jellyfin_user_id (jellyfin_user_id),
    INDEX idx_jellyfin_username (jellyfin_username)
);

-- Server status and configuration
CREATE TABLE server_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);

-- Server activity logs
CREATE TABLE server_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action VARCHAR(50) NOT NULL,
    status ENUM('success', 'error', 'warning', 'info') DEFAULT 'info',
    message TEXT,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_action (action),
    INDEX idx_status (status),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- User activity logs
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- User sessions for tracking active sessions
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
);

-- Invite codes for user registration
CREATE TABLE invite_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    created_by INT,
    used_by INT NULL,
    max_uses INT DEFAULT 1,
    current_uses INT DEFAULT 0,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_code (code),
    INDEX idx_created_by (created_by),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
);

-- API keys for Jellyfin integration
CREATE TABLE api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_key_name (key_name),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
);

-- Packages table
CREATE TABLE packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    duration_days INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    max_simultaneous_sessions INT DEFAULT 1,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_is_active (is_active)
);

-- User subscriptions
CREATE TABLE user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    status ENUM('pending', 'active', 'expired', 'cancelled') DEFAULT 'pending',
    payment_amount DECIMAL(10,2) NOT NULL,
    payment_reference VARCHAR(255),
    qr_code_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_package_id (package_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date)
);

-- Payment transactions
CREATE TABLE payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subscription_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'promptpay',
    transaction_ref VARCHAR(255),
    qr_code_url VARCHAR(500),
    slip_image VARCHAR(500),
    slip_hash VARCHAR(64) NULL,
    status ENUM('pending', 'pending_verification', 'completed', 'failed', 'cancelled', 'manual_review') DEFAULT 'pending',
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_transaction_ref (transaction_ref),
    INDEX idx_slip_hash (slip_hash)
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, first_name, last_name, is_admin, email_verified) 
VALUES ('admin', 'admin@localhost', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', TRUE, TRUE);

-- Insert default server configuration
INSERT INTO server_config (config_key, config_value, description) VALUES
('jellyfin_server_url', 'http://localhost:8096', 'Jellyfin server URL'),
('jellyfin_api_key', '', 'Jellyfin API key for server management'),
('max_users', '100', 'Maximum number of users allowed'),
('registration_enabled', '1', 'Whether new user registration is enabled'),
('email_verification_required', '0', 'Whether email verification is required for new users'),
('default_user_policy', '{"EnabledDevices":[],"EnableAllDevices":true,"EnabledChannels":[],"EnableAllChannels":false,"EnabledFolders":[],"EnableAllFolders":false,"InvalidLoginAttemptCount":3,"LoginAttemptsBeforeLockout":3,"MaxActiveSessions":1,"EnablePublicSharing":false,"BlockedMediaFolders":[],"BlockedChannels":[],"RemoteClientBitrateLimit":0,"AuthenticationProviderId":"Jellyfin.Server.Implementations.Users.DefaultAuthenticationProvider","PasswordResetProviderId":"Jellyfin.Server.Implementations.Users.DefaultPasswordResetProvider","InvalidLoginAttemptCount":3,"LoginAttemptsBeforeLockout":3,"MaxActiveSessions":1,"EnablePlaybackRemuxing":true,"EnableVideoPlaybackTranscoding":true,"EnableAudioPlaybackTranscoding":true,"EnableLiveTvManagement":false,"EnableLiveTvAccess":false,"EnableMediaPlayback":true,"EnableMediaConversion":false,"EnabledDevices":[],"EnableAllDevices":true,"EnabledChannels":[],"EnableAllChannels":false,"EnabledFolders":[],"EnableAllFolders":false,"EnableRemoteControlOfOtherUsers":false,"EnableSharedDeviceControl":false,"EnableRemoteAccess":true,"EnableLiveTvManagement":false,"EnableLiveTvAccess":false,"EnableMediaPlayback":true,"EnableAudioPlaybackTranscoding":true,"EnableVideoPlaybackTranscoding":true,"EnablePlaybackRemuxing":true,"ForceRemoteSourceTranscoding":false,"EnableContentDeletion":false,"EnableContentDeletionFromFolders":[],"EnableContentDownloading":false,"EnableSyncTranscoding":false,"EnableMediaConversion":false,"EnabledDevices":[],"EnableAllDevices":true,"EnabledChannels":[],"EnableAllChannels":false,"EnabledFolders":[],"EnableAllFolders":false,"BlockedTags":[],"AllowedTags":[],"EnableUserPreferenceAccess":true,"AccessSchedules":[],"BlockUnratedItems":[],"EnableRemoteControlOfOtherUsers":false,"EnableSharedDeviceControl":false,"EnableRemoteAccess":true,"EnableLiveTvManagement":false,"EnableLiveTvAccess":false,"EnableMediaPlayback":true,"EnableAudioPlaybackTranscoding":true,"EnableVideoPlaybackTranscoding":true,"EnablePlaybackRemuxing":true,"ForceRemoteSourceTranscoding":false,"EnableContentDeletion":false,"EnableContentDownloading":false,"EnableSubtitleDownloading":false,"EnableSubtitleManagement":false,"EnableSyncTranscoding":false,"EnableMediaConversion":false,"EnableAllDevices":true,"EnableAllChannels":false,"EnableAllFolders":false,"EnableUserPreferenceAccess":true}', 'Default user policy JSON for new Jellyfin users'),
('promptpay_phone', '0820722972', 'PromptPay phone number for payments');

-- Insert default packages
INSERT INTO packages (name, duration_days, price, max_simultaneous_sessions, description, is_active) VALUES
('15 วัน', 15, 60.00, 1, 'แพ็คเกจ 15 วัน', TRUE),
('30 วัน', 30, 100.00, 1, 'แพ็คเกจ 30 วัน', TRUE);

-- Create indexes for better performance
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_admin ON users(is_admin);
CREATE INDEX idx_jellyfin_users_active ON jellyfin_users(user_id, jellyfin_user_id);
CREATE INDEX idx_server_logs_recent ON server_logs(created_at DESC);
CREATE INDEX idx_activity_logs_recent ON activity_logs(created_at DESC);
CREATE INDEX idx_sessions_active ON user_sessions(user_id, expires_at);

-- Create views for common queries
CREATE VIEW active_users AS
SELECT u.*, ju.jellyfin_user_id, ju.jellyfin_username, ju.max_simultaneous_sessions
FROM users u
LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
WHERE u.is_active = TRUE;

CREATE VIEW recent_activity AS
SELECT 
    al.id,
    al.action,
    al.details,
    al.created_at,
    u.username,
    u.email
FROM activity_logs al
LEFT JOIN users u ON al.user_id = u.id
ORDER BY al.created_at DESC
LIMIT 100;
