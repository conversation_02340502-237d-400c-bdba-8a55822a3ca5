<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/JellyfinAPI.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';

// Handle user actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'toggle_active':
                $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$user_id]);
                
                $stmt = $pdo->prepare("SELECT username, is_active FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                $status = $user['is_active'] ? 'activated' : 'deactivated';
                $message = "User '{$user['username']}' has been {$status}.";
                $message_type = 'success';
                
                log_activity($_SESSION['user_id'], 'user_' . ($user['is_active'] ? 'activated' : 'deactivated'), "User {$user['username']} {$status}");
                break;
                
            case 'toggle_admin':
                $stmt = $pdo->prepare("UPDATE users SET is_admin = NOT is_admin WHERE id = ?");
                $stmt->execute([$user_id]);
                
                $stmt = $pdo->prepare("SELECT username, is_admin FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                $status = $user['is_admin'] ? 'promoted to admin' : 'demoted from admin';
                $message = "User '{$user['username']}' has been {$status}.";
                $message_type = 'success';
                
                log_activity($_SESSION['user_id'], 'user_admin_changed', "User {$user['username']} {$status}");
                break;
                
            case 'delete_user':
                // Get user info before deletion
                $stmt = $pdo->prepare("SELECT username, jellyfin_user_id FROM users u LEFT JOIN jellyfin_users ju ON u.id = ju.user_id WHERE u.id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
                
                if ($user) {
                    // Delete from Jellyfin if exists
                    if ($user['jellyfin_user_id']) {
                        try {
                            $jellyfin = new JellyfinAPI();
                            $jellyfin->deleteUser($user['jellyfin_user_id']);
                        } catch (Exception $e) {
                            error_log('Failed to delete Jellyfin user: ' . $e->getMessage());
                        }
                    }
                    
                    // Delete from database (cascade will handle related records)
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    
                    $message = "User '{$user['username']}' has been deleted.";
                    $message_type = 'success';
                    
                    log_activity($_SESSION['user_id'], 'user_deleted', "User {$user['username']} deleted");
                }
                break;
                
            case 'reset_password':
                $new_password = generate_random_string(12);
                $password_hash = hash_password($new_password);

                // Get user info before update
                $stmt = $pdo->prepare("SELECT u.username, ju.jellyfin_user_id FROM users u LEFT JOIN jellyfin_users ju ON u.id = ju.user_id WHERE u.id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();

                // Update password in local database
                $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
                $stmt->execute([$password_hash, $user_id]);

                // Update password in Jellyfin
                if ($user['jellyfin_user_id']) {
                    try {
                        $jellyfin = new JellyfinAPI();
                        $jellyfin->resetUserPassword($user['jellyfin_user_id'], $new_password);

                        // Update stored password in database
                        $stmt = $pdo->prepare("UPDATE jellyfin_users SET jellyfin_password = ? WHERE user_id = ?");
                        $stmt->execute([$new_password, $user_id]);
                    } catch (Exception $e) {
                        error_log('Failed to reset Jellyfin password: ' . $e->getMessage());
                    }
                }

                $message = "รีเซ็ตรหัสผ่านสำหรับผู้ใช้ '{$user['username']}' เรียบร้อยแล้ว รหัสผ่านใหม่: {$new_password}";
                $message_type = 'warning';

                log_activity($_SESSION['user_id'], 'password_reset', "Password reset for user {$user['username']}");
                break;
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
        error_log('User management error: ' . $e->getMessage());
    }
}

// Get all users with Jellyfin info
$stmt = $pdo->prepare("
    SELECT u.*, ju.jellyfin_user_id, ju.jellyfin_username, ju.max_simultaneous_sessions, ju.jellyfin_last_activity,
           (SELECT COUNT(*) FROM activity_logs WHERE user_id = u.id) as activity_count
    FROM users u 
    LEFT JOIN jellyfin_users ju ON u.id = ju.user_id 
    ORDER BY u.created_at DESC
");
$stmt->execute();
$users = $stmt->fetchAll();

// Get user statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN is_admin = 1 THEN 1 ELSE 0 END) as admin_users,
        SUM(CASE WHEN email_verified = 1 THEN 1 ELSE 0 END) as verified_users
    FROM users
");
$stmt->execute();
$stats = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Jellyfin by James Registration System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #333;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }
        .users-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
        }
        .users-table th,
        .users-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .users-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
        }
        .users-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        .user-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-toggle { background: #17a2b8; color: white; }
        .btn-admin { background: #6f42c1; color: white; }
        .btn-reset { background: #ffc107; color: #000; }
        .btn-delete { background: #dc3545; color: white; }
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #dc3545; color: white; }
        .status-admin { background: #6f42c1; color: white; }
        .status-verified { background: #17a2b8; color: white; }
        .status-unverified { background: #6c757d; color: white; }
        @media (max-width: 768px) {
            .users-table {
                font-size: 0.8rem;
            }
            .users-table th,
            .users-table td {
                padding: 0.5rem;
            }
            .user-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="add-package.php" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">User Management</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">Manage registered users and their Jellyfin accounts</p>
            </div>

            <?php if ($message): ?>
                <div class="flash-message flash-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- User Statistics -->
            <div class="stats-grid fade-in-scroll">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_users']; ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['admin_users']; ?></div>
                    <div class="stat-label">Administrators</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['verified_users']; ?></div>
                    <div class="stat-label">Verified Users</div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="fade-in-scroll" style="overflow-x: auto;">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>เบอร์โทรศัพท์</th>
                            <th>Status</th>
                            <th>Jellyfin Account</th>
                            <th>Registered</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div><strong><?php echo htmlspecialchars($user['username'] ?? ''); ?></strong></div>
                                    <div style="font-size: 0.8rem; color: #ccc;">
                                        <?php echo htmlspecialchars($user['phone'] ?? 'ไม่ระบุ'); ?>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($user['phone'] ?? 'ไม่ระบุ'); ?></td>
                                <td>
                                    <div>
                                        <span class="status-badge status-<?php echo $user['is_active'] ? 'active' : 'inactive'; ?>">
                                            <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                    <?php if ($user['is_admin']): ?>
                                        <div style="margin-top: 0.25rem;">
                                            <span class="status-badge status-admin">Admin</span>
                                        </div>
                                    <?php endif; ?>
                                    <div style="margin-top: 0.25rem;">
                                        <span class="status-badge status-<?php echo $user['email_verified'] ? 'verified' : 'unverified'; ?>">
                                            <?php echo $user['email_verified'] ? 'Verified' : 'Unverified'; ?>
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($user['jellyfin_user_id']): ?>
                                        <div><strong><?php echo htmlspecialchars($user['jellyfin_username'] ?? ''); ?></strong></div>
                                        <div style="font-size: 0.8rem; color: #ccc;">
                                            Max Sessions: <?php echo $user['max_simultaneous_sessions'] ?? 1; ?>
                                        </div>
                                        <?php if ($user['jellyfin_last_activity']): ?>
                                            <div style="font-size: 0.8rem; color: #ccc;">
                                                Last Activity: <?php echo date('M j, Y', strtotime($user['jellyfin_last_activity'])); ?>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span style="color: #ffc107;">Not Created</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div><?php echo date('M j, Y', strtotime($user['created_at'])); ?></div>
                                    <div style="font-size: 0.8rem; color: #ccc;">
                                        <?php echo $user['activity_count']; ?> activities
                                    </div>
                                </td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php echo date('M j, Y g:i A', strtotime($user['last_login'])); ?>
                                    <?php else: ?>
                                        <span style="color: #888;">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['id'] != $_SESSION['user_id']): // Don't allow actions on self ?>
                                        <div class="user-actions">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_active">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="action-btn btn-toggle" 
                                                        onclick="return confirm('Are you sure you want to <?php echo $user['is_active'] ? 'deactivate' : 'activate'; ?> this user?')">
                                                    <?php echo $user['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                                </button>
                                            </form>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_admin">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="action-btn btn-admin"
                                                        onclick="return confirm('Are you sure you want to <?php echo $user['is_admin'] ? 'remove admin rights from' : 'make admin'; ?> this user?')">
                                                    <?php echo $user['is_admin'] ? 'Remove Admin' : 'Make Admin'; ?>
                                                </button>
                                            </form>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="reset_password">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="action-btn btn-reset"
                                                        onclick="return confirm('Are you sure you want to reset this user\'s password?')">
                                                    Reset Password
                                                </button>
                                            </form>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete_user">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="action-btn btn-delete"
                                                        onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone!')">
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    <?php else: ?>
                                        <span style="color: #888;">Current User</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
