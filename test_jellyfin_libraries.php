<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Require login
require_login();

echo "<h2>🧪 Test Jellyfin Libraries Access</h2>";

try {
    $jellyfin = new JellyfinAPI();
    
    echo "<h3>🔍 Step 1: Get All Jellyfin Users</h3>";
    
    $jellyfinUsers = $jellyfin->getUsers();
    
    if ($jellyfinUsers) {
        echo "<p>Found " . count($jellyfinUsers) . " Jellyfin users</p>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Name</th><th>ID</th><th>Disabled</th><th>Max Sessions</th><th>All Folders</th><th>Actions</th></tr>";
        
        foreach ($jellyfinUsers as $user) {
            $isDisabled = $user['Policy']['IsDisabled'] ?? false;
            $maxSessions = $user['Policy']['MaxActiveSessions'] ?? 'N/A';
            $enableAllFolders = $user['Policy']['EnableAllFolders'] ?? false;
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['Name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['Id']) . "</td>";
            echo "<td style='background: " . ($isDisabled ? 'red' : 'lightgreen') . ";'>" . ($isDisabled ? '❌ Yes' : '✅ No') . "</td>";
            echo "<td>{$maxSessions}</td>";
            echo "<td style='background: " . ($enableAllFolders ? 'lightgreen' : 'yellow') . ";'>" . ($enableAllFolders ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td>";
            
            if ($isDisabled) {
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='enable_user' value='{$user['Id']}'>";
                echo "<input type='hidden' name='username' value='{$user['Name']}'>";
                echo "<button type='submit' style='background: green; color: white; padding: 5px; border: none; cursor: pointer;'>Enable All Libraries</button>";
                echo "</form>";
            } else {
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='check_user' value='{$user['Id']}'>";
                echo "<input type='hidden' name='username' value='{$user['Name']}'>";
                echo "<button type='submit' style='background: blue; color: white; padding: 5px; border: none; cursor: pointer;'>Check Details</button>";
                echo "</form>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ Failed to get Jellyfin users</p>";
    }
    
    echo "<h3>🔍 Step 2: Check Recent Payments</h3>";
    
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'completed'
        AND pt.paid_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.paid_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recentPayments = $stmt->fetchAll();
    
    if ($recentPayments) {
        echo "<p>Found " . count($recentPayments) . " recent completed payments</p>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Paid At</th><th>Jellyfin Status</th><th>Actions</th></tr>";
        
        foreach ($recentPayments as $payment) {
            // Find corresponding Jellyfin user
            $jellyfinUser = null;
            foreach ($jellyfinUsers as $ju) {
                if ($ju['Name'] === $payment['username']) {
                    $jellyfinUser = $ju;
                    break;
                }
            }
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['paid_at'] . "</td>";
            
            if ($jellyfinUser) {
                $isDisabled = $jellyfinUser['Policy']['IsDisabled'] ?? false;
                $enableAllFolders = $jellyfinUser['Policy']['EnableAllFolders'] ?? false;
                
                if (!$isDisabled && $enableAllFolders) {
                    echo "<td style='background: lightgreen;'>✅ Enabled + All Libraries</td>";
                } elseif (!$isDisabled) {
                    echo "<td style='background: yellow;'>⚠️ Enabled but Limited Libraries</td>";
                } else {
                    echo "<td style='background: red;'>❌ Disabled</td>";
                }
                
                echo "<td>";
                if ($isDisabled || !$enableAllFolders) {
                    echo "<form method='POST' style='display: inline;'>";
                    echo "<input type='hidden' name='fix_user' value='{$jellyfinUser['Id']}'>";
                    echo "<input type='hidden' name='username' value='{$payment['username']}'>";
                    echo "<input type='hidden' name='max_sessions' value='" . ($payment['max_simultaneous_sessions'] ?? 1) . "'>";
                    echo "<button type='submit' style='background: orange; color: white; padding: 5px; border: none; cursor: pointer;'>Fix Access</button>";
                    echo "</form>";
                }
                echo "</td>";
            } else {
                echo "<td style='background: red;'>❌ User Not Found</td>";
                echo "<td>-</td>";
            }
            
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No recent completed payments found</p>";
    }
    
    // Handle form submissions
    if ($_POST) {
        echo "<h3>🔧 Action Results</h3>";
        
        if (isset($_POST['enable_user'])) {
            $userId = $_POST['enable_user'];
            $username = $_POST['username'];
            
            echo "<h4>Enabling all libraries for: {$username}</h4>";
            
            try {
                $result = $jellyfin->enableAllLibraries($userId, 1);
                
                if ($result) {
                    echo "<p>✅ <strong>Successfully enabled all libraries access!</strong></p>";
                    
                    // Get updated user info
                    $updatedUser = $jellyfin->getUser($userId);
                    if ($updatedUser) {
                        $policy = $updatedUser['Policy'];
                        echo "<div style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>";
                        echo "<h5>Updated Policy:</h5>";
                        echo "<ul>";
                        echo "<li>IsDisabled: " . ($policy['IsDisabled'] ? 'true' : 'false') . "</li>";
                        echo "<li>EnableAllFolders: " . ($policy['EnableAllFolders'] ? 'true' : 'false') . "</li>";
                        echo "<li>MaxActiveSessions: " . ($policy['MaxActiveSessions'] ?? 'N/A') . "</li>";
                        echo "<li>EnableAllChannels: " . ($policy['EnableAllChannels'] ? 'true' : 'false') . "</li>";
                        echo "</ul>";
                        echo "</div>";
                    }
                } else {
                    echo "<p>❌ Failed to enable all libraries access</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Error: " . $e->getMessage() . "</p>";
            }
        }
        
        if (isset($_POST['check_user'])) {
            $userId = $_POST['check_user'];
            $username = $_POST['username'];
            
            echo "<h4>Detailed info for: {$username}</h4>";
            
            try {
                $user = $jellyfin->getUser($userId);
                if ($user) {
                    $policy = $user['Policy'];
                    
                    echo "<div style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>";
                    echo "<h5>User Policy Details:</h5>";
                    echo "<ul>";
                    echo "<li><strong>IsDisabled:</strong> " . ($policy['IsDisabled'] ? 'true' : 'false') . "</li>";
                    echo "<li><strong>EnableAllFolders:</strong> " . ($policy['EnableAllFolders'] ? 'true' : 'false') . "</li>";
                    echo "<li><strong>MaxActiveSessions:</strong> " . ($policy['MaxActiveSessions'] ?? 'N/A') . "</li>";
                    echo "<li><strong>EnableAllChannels:</strong> " . ($policy['EnableAllChannels'] ? 'true' : 'false') . "</li>";
                    echo "<li><strong>EnabledFolders:</strong> " . (empty($policy['EnabledFolders']) ? 'All' : count($policy['EnabledFolders']) . ' folders') . "</li>";
                    echo "<li><strong>BlockedMediaFolders:</strong> " . (empty($policy['BlockedMediaFolders']) ? 'None' : count($policy['BlockedMediaFolders']) . ' blocked') . "</li>";
                    echo "</ul>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Error: " . $e->getMessage() . "</p>";
            }
        }
        
        if (isset($_POST['fix_user'])) {
            $userId = $_POST['fix_user'];
            $username = $_POST['username'];
            $maxSessions = (int)$_POST['max_sessions'];
            
            echo "<h4>Fixing access for: {$username} (Max Sessions: {$maxSessions})</h4>";
            
            try {
                $result = $jellyfin->enableAllLibraries($userId, $maxSessions);
                
                if ($result) {
                    echo "<p>✅ <strong>Successfully fixed user access!</strong></p>";
                } else {
                    echo "<p>❌ Failed to fix user access</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Error: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p><a href='?'>🔄 Refresh Page</a></p>";
    }
    
    echo "<h3>📊 Summary</h3>";
    
    $enabledCount = 0;
    $disabledCount = 0;
    $allLibrariesCount = 0;
    $limitedLibrariesCount = 0;
    
    foreach ($jellyfinUsers as $user) {
        $isDisabled = $user['Policy']['IsDisabled'] ?? false;
        $enableAllFolders = $user['Policy']['EnableAllFolders'] ?? false;
        
        if ($isDisabled) {
            $disabledCount++;
        } else {
            $enabledCount++;
            if ($enableAllFolders) {
                $allLibrariesCount++;
            } else {
                $limitedLibrariesCount++;
            }
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Status</th><th>Count</th></tr>";
    echo "<tr><td>Total Users</td><td>" . count($jellyfinUsers) . "</td></tr>";
    echo "<tr><td>Enabled Users</td><td style='background: lightgreen;'>{$enabledCount}</td></tr>";
    echo "<tr><td>Disabled Users</td><td style='background: red;'>{$disabledCount}</td></tr>";
    echo "<tr><td>All Libraries Access</td><td style='background: lightgreen;'>{$allLibrariesCount}</td></tr>";
    echo "<tr><td>Limited Libraries</td><td style='background: yellow;'>{$limitedLibrariesCount}</td></tr>";
    echo "</table>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/test_cron_fixed.php'>🧪 Test Fixed Cron</a></p>";
?>
