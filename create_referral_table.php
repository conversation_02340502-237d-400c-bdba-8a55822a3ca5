<?php
require_once 'includes/config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Create Referral Transactions Table</h2>\n";
    
    // Check if referral_transactions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'referral_transactions'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: blue;'>ℹ referral_transactions table already exists</p>\n";
    } else {
        // Create referral_transactions table
        $sql = "
        CREATE TABLE referral_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            referrer_id INT NOT NULL,
            referred_id INT NOT NULL,
            points_earned DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            transaction_type ENUM('purchase', 'signup', 'bonus') NOT NULL DEFAULT 'purchase',
            related_payment_id INT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (related_payment_id) REFERENCES payment_transactions(id) ON DELETE SET NULL,
            INDEX idx_referrer_id (referrer_id),
            INDEX idx_referred_id (referred_id),
            INDEX idx_related_payment_id (related_payment_id),
            INDEX idx_transaction_type (transaction_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ Created referral_transactions table</p>\n";
    }
    
    // Check if affiliate_points column exists in users table
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'affiliate_points'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: blue;'>ℹ affiliate_points column already exists in users table</p>\n";
    } else {
        // Add affiliate_points column
        $pdo->exec("ALTER TABLE users ADD COLUMN affiliate_points DECIMAL(10,2) DEFAULT 0.00");
        echo "<p style='color: green;'>✓ Added affiliate_points column to users table</p>\n";
    }
    
    // Check if referred_by column exists in users table
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'referred_by'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: blue;'>ℹ referred_by column already exists in users table</p>\n";
    } else {
        // Add referred_by column
        $pdo->exec("ALTER TABLE users ADD COLUMN referred_by INT NULL");
        $pdo->exec("ALTER TABLE users ADD CONSTRAINT fk_users_referred_by FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✓ Added referred_by column to users table</p>\n";
    }
    
    // Set up test referral relationship (test1 referred by james)
    echo "<h3>Setting up test referral relationship:</h3>\n";
    
    // Get james user ID
    $stmt = $pdo->prepare("SELECT id, username, affiliate_points FROM users WHERE username = 'james'");
    $stmt->execute();
    $james = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get test1 user ID
    $stmt = $pdo->prepare("SELECT id, username, referred_by FROM users WHERE username = 'test1'");
    $stmt->execute();
    $test1 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($james && $test1) {
        // Set test1 as referred by james
        $stmt = $pdo->prepare("UPDATE users SET referred_by = ? WHERE id = ?");
        $stmt->execute([$james['id'], $test1['id']]);
        
        echo "<p style='color: green;'>✓ Set test1 as referred by james</p>\n";
        echo "<p>James ID: {$james['id']}, Current Points: {$james['affiliate_points']}</p>\n";
        echo "<p>Test1 ID: {$test1['id']}, Referred By: " . ($test1['referred_by'] ? $test1['referred_by'] : 'None') . " → {$james['id']}</p>\n";
    } else {
        if (!$james) echo "<p style='color: red;'>✗ James user not found</p>\n";
        if (!$test1) echo "<p style='color: red;'>✗ Test1 user not found</p>\n";
    }
    
    // Show current referral transactions
    echo "<h3>Current Referral Transactions:</h3>\n";
    $stmt = $pdo->query("
        SELECT rt.*, 
               r.username as referrer_username, 
               u.username as referred_username
        FROM referral_transactions rt
        LEFT JOIN users r ON rt.referrer_id = r.id
        LEFT JOIN users u ON rt.referred_id = u.id
        ORDER BY rt.created_at DESC
        LIMIT 10
    ");
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($transactions)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Referrer</th><th>Referred</th><th>Points</th><th>Type</th><th>Payment ID</th><th>Description</th><th>Created</th></tr>\n";
        
        foreach ($transactions as $transaction) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($transaction['id']) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['referrer_username']) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['referred_username']) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['points_earned']) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['transaction_type']) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['related_payment_id'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($transaction['description']) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['created_at']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>No referral transactions found.</p>\n";
    }
    
    // Show users with affiliate points
    echo "<h3>Users with Affiliate Points:</h3>\n";
    $stmt = $pdo->query("
        SELECT username, affiliate_points, 
               (SELECT COUNT(*) FROM users u2 WHERE u2.referred_by = users.id) as referrals_count
        FROM users 
        WHERE affiliate_points > 0 OR (SELECT COUNT(*) FROM users u2 WHERE u2.referred_by = users.id) > 0
        ORDER BY affiliate_points DESC
    ");
    $usersWithPoints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($usersWithPoints)) {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Username</th><th>Affiliate Points</th><th>Referrals Count</th></tr>\n";
        
        foreach ($usersWithPoints as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['affiliate_points']) . "</td>";
            echo "<td>" . htmlspecialchars($user['referrals_count']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>No users with affiliate points found.</p>\n";
    }
    
    echo "<h3>Next Steps:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='manual_check_payments.php'>Run Manual Payment Check</a></li>\n";
    echo "<li><a href='admin/auto-verification.php'>Auto Verification System</a></li>\n";
    echo "<li>Make sure test1 has completed payments to test affiliate points</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
