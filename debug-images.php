<?php
// Debug image loading issues
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Images - Jellyfin by <PERSON></title>
    <style>
        body { 
            background: #0a0a0a; 
            color: #fff; 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            margin: 0; 
        }
        .debug-section { 
            background: #1a1a1a; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
            border: 1px solid #333; 
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        pre { 
            background: #000; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
        }
        .test-img { 
            margin: 10px; 
            border: 2px solid #333; 
            border-radius: 5px; 
        }
    </style>
</head>
<body>
    <h1 style="color: #FFD700; text-align: center;">🔍 Debug Images</h1>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">1. File System Check</h2>
        <?php
        $imagePath = __DIR__ . '/assets/images/';
        echo "<p><strong>Image directory:</strong> " . $imagePath . "</p>";
        
        if (is_dir($imagePath)) {
            echo "<p class='success'>✅ Directory exists</p>";
            
            $files = scandir($imagePath);
            echo "<p><strong>Files in directory:</strong></p><ul>";
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $fullPath = $imagePath . $file;
                    $size = filesize($fullPath);
                    $readable = is_readable($fullPath) ? '✅' : '❌';
                    echo "<li>$readable $file (" . number_format($size) . " bytes)</li>";
                }
            }
            echo "</ul>";
        } else {
            echo "<p class='error'>❌ Directory does not exist</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">2. SVG Content Check</h2>
        <?php
        $svgPath = $imagePath . 'logo.svg';
        if (file_exists($svgPath)) {
            echo "<p class='success'>✅ logo.svg exists</p>";
            echo "<p><strong>File size:</strong> " . filesize($svgPath) . " bytes</p>";
            echo "<p><strong>MIME type:</strong> " . mime_content_type($svgPath) . "</p>";
            
            $svgContent = file_get_contents($svgPath);
            echo "<p><strong>First 200 characters:</strong></p>";
            echo "<pre>" . htmlspecialchars(substr($svgContent, 0, 200)) . "...</pre>";
            
            // Check if it's valid XML
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($svgContent);
            if ($xml !== false) {
                echo "<p class='success'>✅ Valid XML/SVG structure</p>";
            } else {
                echo "<p class='error'>❌ Invalid XML/SVG structure</p>";
                $errors = libxml_get_errors();
                foreach ($errors as $error) {
                    echo "<p class='error'>Error: " . trim($error->message) . "</p>";
                }
            }
        } else {
            echo "<p class='error'>❌ logo.svg does not exist</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">3. Image Loading Test</h2>
        
        <h3>SVG Logo:</h3>
        <img src="assets/images/logo.svg" alt="SVG Logo" class="test-img" style="height: 50px;" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ SVG loaded successfully</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ SVG failed to load</span>'">
        <div></div>
        
        <h3>JPG Logo:</h3>
        <img src="assets/images/logo.jpg" alt="JPG Logo" class="test-img" style="height: 50px;" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ JPG loaded successfully</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ JPG failed to load</span>'">
        <div></div>
        
        <h3>Favicon:</h3>
        <img src="assets/images/favicon.svg" alt="Favicon" class="test-img" style="height: 30px;" 
             onload="this.nextElementSibling.innerHTML='<span class=success>✅ Favicon loaded successfully</span>'"
             onerror="this.nextElementSibling.innerHTML='<span class=error>❌ Favicon failed to load</span>'">
        <div></div>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">4. URL Path Test</h2>
        <p><strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
        <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
        <p><strong>Script Path:</strong> <?php echo __FILE__; ?></p>
        
        <h3>Test Different Paths:</h3>
        <div id="pathTest">
            <button onclick="testImagePaths()" style="background: #FFD700; color: #000; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                Test Image Paths
            </button>
            <div id="pathResults" style="margin-top: 15px;"></div>
        </div>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">5. Server Configuration</h2>
        <p><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></p>
        
        <?php
        // Check if mod_rewrite is enabled
        if (function_exists('apache_get_modules')) {
            $modules = apache_get_modules();
            if (in_array('mod_rewrite', $modules)) {
                echo "<p class='success'>✅ mod_rewrite is enabled</p>";
            } else {
                echo "<p class='error'>❌ mod_rewrite is not enabled</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Cannot check Apache modules</p>";
        }
        ?>
    </div>
    
    <div style="text-align: center; margin-top: 40px;">
        <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
            ← กลับไปหน้าแรก
        </a>
        <a href="/test-logo-simple.html" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px; margin-left: 10px;">
            ทดสอบโลโก้
        </a>
    </div>
    
    <script>
        function testImagePaths() {
            const results = document.getElementById('pathResults');
            results.innerHTML = '<p>กำลังทดสอบ...</p>';
            
            const paths = [
                'assets/images/logo.svg',
                '/assets/images/logo.svg',
                './assets/images/logo.svg',
                '../assets/images/logo.svg',
                'assets/images/logo.jpg',
                '/assets/images/logo.jpg'
            ];
            
            let resultHtml = '<table style="width: 100%; border-collapse: collapse;">';
            resultHtml += '<tr style="background: #333;"><th style="padding: 8px; border: 1px solid #555;">Path</th><th style="padding: 8px; border: 1px solid #555;">Status</th><th style="padding: 8px; border: 1px solid #555;">Size</th></tr>';
            
            let completed = 0;
            
            paths.forEach(path => {
                const img = new Image();
                img.onload = function() {
                    resultHtml += `<tr><td style="padding: 8px; border: 1px solid #555;">${path}</td><td style="padding: 8px; border: 1px solid #555; color: #4CAF50;">✅ OK</td><td style="padding: 8px; border: 1px solid #555;">${this.width}x${this.height}</td></tr>`;
                    completed++;
                    if (completed === paths.length) {
                        resultHtml += '</table>';
                        results.innerHTML = resultHtml;
                    }
                };
                img.onerror = function() {
                    resultHtml += `<tr><td style="padding: 8px; border: 1px solid #555;">${path}</td><td style="padding: 8px; border: 1px solid #555; color: #ff6b6b;">❌ Failed</td><td style="padding: 8px; border: 1px solid #555;">-</td></tr>`;
                    completed++;
                    if (completed === paths.length) {
                        resultHtml += '</table>';
                        results.innerHTML = resultHtml;
                    }
                };
                img.src = path;
            });
        }
    </script>
</body>
</html>
