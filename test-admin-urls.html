<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin URLs - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .url-test {
            background: #1a1a1a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .url-link {
            color: #FFD700;
            text-decoration: none;
            font-weight: bold;
        }
        .url-link:hover {
            color: #FFA500;
        }
        .test-btn {
            background: #FFD700;
            color: #000;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .test-btn:hover {
            background: #FFA500;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        .status.success {
            background: #28a745;
            color: white;
        }
        .status.error {
            background: #dc3545;
            color: white;
        }
        .status.testing {
            background: #ffc107;
            color: black;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #FFD700; text-align: center;">🔧 Test Admin Clean URLs</h1>
        <p style="text-align: center; color: #ccc; margin-bottom: 30px;">
            ทดสอบ Clean URLs สำหรับหน้า Admin ทั้งหมด
        </p>

        <div class="url-test">
            <a href="/admin" class="url-link" target="_blank">/admin</a>
            <button class="test-btn" onclick="testUrl('/admin', this)">Test</button>
            <span class="status" id="status-admin">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/server" class="url-link" target="_blank">/admin/server</a>
            <button class="test-btn" onclick="testUrl('/admin/server', this)">Test</button>
            <span class="status" id="status-server">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/server-control" class="url-link" target="_blank">/admin/server-control</a>
            <button class="test-btn" onclick="testUrl('/admin/server-control', this)">Test</button>
            <span class="status" id="status-server-control">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/users" class="url-link" target="_blank">/admin/users</a>
            <button class="test-btn" onclick="testUrl('/admin/users', this)">Test</button>
            <span class="status" id="status-users">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/packages" class="url-link" target="_blank">/admin/packages</a>
            <button class="test-btn" onclick="testUrl('/admin/packages', this)">Test</button>
            <span class="status" id="status-packages">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/manage-packages" class="url-link" target="_blank">/admin/manage-packages</a>
            <button class="test-btn" onclick="testUrl('/admin/manage-packages', this)">Test</button>
            <span class="status" id="status-manage-packages">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/add-package" class="url-link" target="_blank">/admin/add-package</a>
            <button class="test-btn" onclick="testUrl('/admin/add-package', this)">Test</button>
            <span class="status" id="status-add-package">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/transactions" class="url-link" target="_blank">/admin/transactions</a>
            <button class="test-btn" onclick="testUrl('/admin/transactions', this)">Test</button>
            <span class="status" id="status-transactions">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/transaction-check" class="url-link" target="_blank">/admin/transaction-check</a>
            <button class="test-btn" onclick="testUrl('/admin/transaction-check', this)">Test</button>
            <span class="status" id="status-transaction-check">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/paynoi" class="url-link" target="_blank">/admin/paynoi</a>
            <button class="test-btn" onclick="testUrl('/admin/paynoi', this)">Test</button>
            <span class="status" id="status-paynoi">รอทดสอบ</span>
        </div>

        <div class="url-test">
            <a href="/admin/paynoi-transactions" class="url-link" target="_blank">/admin/paynoi-transactions</a>
            <button class="test-btn" onclick="testUrl('/admin/paynoi-transactions', this)">Test</button>
            <span class="status" id="status-paynoi-transactions">รอทดสอบ</span>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="testAllUrls()" style="background: #28a745; color: white; border: none; padding: 12px 25px; border-radius: 5px; cursor: pointer; font-weight: bold; font-size: 1.1rem;">
                🚀 ทดสอบทั้งหมด
            </button>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                ← กลับไปหน้าแรก
            </a>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 10px;">
            <h3 style="color: #FFD700;">📝 หมายเหตุ:</h3>
            <ul style="color: #ccc; line-height: 1.6;">
                <li><strong>✅ สีเขียว:</strong> URL ทำงานปกติ (Status 200)</li>
                <li><strong>❌ สีแดง:</strong> URL ไม่ทำงาน (404 หรือ Error)</li>
                <li><strong>⚠️ สีเหลือง:</strong> กำลังทดสอบ</li>
                <li>หากมี URL ไม่ทำงาน ให้ตรวจสอบ .htaccess และ mod_rewrite</li>
                <li>บางหน้าอาจต้องการสิทธิ์ Admin ในการเข้าถึง</li>
            </ul>
        </div>
    </div>

    <script>
        function testUrl(url, button) {
            const statusId = 'status-' + url.replace(/[\/\-]/g, '').replace('admin', '');
            const statusElement = document.getElementById(statusId) || 
                                document.getElementById('status-' + url.split('/').pop().replace('-', ''));
            
            if (statusElement) {
                statusElement.textContent = 'กำลังทดสอบ...';
                statusElement.className = 'status testing';
            }
            
            button.disabled = true;
            button.textContent = 'Testing...';
            
            fetch(url, { method: 'HEAD' })
                .then(response => {
                    if (statusElement) {
                        if (response.ok) {
                            statusElement.textContent = '✅ ทำงาน';
                            statusElement.className = 'status success';
                        } else {
                            statusElement.textContent = `❌ Error ${response.status}`;
                            statusElement.className = 'status error';
                        }
                    }
                })
                .catch(error => {
                    if (statusElement) {
                        statusElement.textContent = '❌ ไม่ทำงาน';
                        statusElement.className = 'status error';
                    }
                })
                .finally(() => {
                    button.disabled = false;
                    button.textContent = 'Test';
                });
        }
        
        function testAllUrls() {
            const urls = [
                '/admin',
                '/admin/server',
                '/admin/server-control',
                '/admin/users',
                '/admin/packages',
                '/admin/manage-packages',
                '/admin/add-package',
                '/admin/transactions',
                '/admin/transaction-check',
                '/admin/paynoi',
                '/admin/paynoi-transactions'
            ];
            
            urls.forEach((url, index) => {
                setTimeout(() => {
                    const button = document.querySelector(`a[href="${url}"]`).parentElement.querySelector('.test-btn');
                    testUrl(url, button);
                }, index * 200); // Delay each test by 200ms
            });
        }
    </script>
</body>
</html>
