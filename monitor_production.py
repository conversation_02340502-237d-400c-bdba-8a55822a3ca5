#!/usr/bin/env python3
"""
Production Monitoring Script for Jellyfin by James
Monitor system status, logs, and performance
Target: ************* / embyjames.xyz
"""

import requests
import subprocess
import json
import time
import os
import sys
from datetime import datetime, timedelta

class ProductionMonitor:
    def __init__(self):
        self.base_url = "https://embyjames.xyz"
        self.api_endpoints = {
            'health': f"{self.base_url}/api/health.php",
            'payment_check': f"{self.base_url}/api/payment-check.php",
            'manual_check': f"{self.base_url}/api/manual-check.php",
            'expiration_check': f"{self.base_url}/api/expiration-check.php"
        }
        
    def check_api_health(self):
        """Check API endpoint health"""
        print("🏥 API Health Check")
        print("-" * 40)
        
        for name, url in self.api_endpoints.items():
            try:
                start_time = time.time()
                response = requests.get(url, timeout=10)
                duration = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    status = "✅ OK"
                    color = "\033[92m"  # Green
                else:
                    status = f"❌ HTTP {response.status_code}"
                    color = "\033[91m"  # Red
                    
                print(f"{color}{name:15} {status:15} {duration:6.1f}ms\033[0m")
                
            except requests.exceptions.Timeout:
                print(f"\033[91m{name:15} ❌ TIMEOUT      >10000ms\033[0m")
            except requests.exceptions.ConnectionError:
                print(f"\033[91m{name:15} ❌ CONN_ERROR        -\033[0m")
            except Exception as e:
                print(f"\033[91m{name:15} ❌ ERROR           -\033[0m")
        
        print()
    
    def check_service_status(self):
        """Check systemd service status"""
        print("⚙️  Service Status")
        print("-" * 40)
        
        try:
            result = subprocess.run(['systemctl', 'is-active', 'jellyfin-payment-checker'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip() == 'active':
                print("✅ jellyfin-payment-checker: \033[92mRUNNING\033[0m")
            else:
                print("❌ jellyfin-payment-checker: \033[91mSTOPPED\033[0m")
                
            # Get service uptime
            result = subprocess.run(['systemctl', 'show', 'jellyfin-payment-checker', 
                                   '--property=ActiveEnterTimestamp'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                timestamp_line = result.stdout.strip()
                if 'ActiveEnterTimestamp=' in timestamp_line:
                    timestamp_str = timestamp_line.split('=')[1]
                    if timestamp_str and timestamp_str != 'n/a':
                        print(f"   Started: {timestamp_str}")
                        
        except Exception as e:
            print(f"❌ Error checking service: {e}")
        
        print()
    
    def check_log_files(self):
        """Check log file status"""
        print("📋 Log Files")
        print("-" * 40)
        
        log_files = [
            '/var/log/jellyfin/continuous_payment.log',
            '/var/log/jellyfin/payment_cron.log',
            '/var/log/jellyfin/cron_health.log',
            '/var/log/jellyfin/cron_full.log'
        ]
        
        for log_file in log_files:
            try:
                if os.path.exists(log_file):
                    stat = os.stat(log_file)
                    size = stat.st_size
                    modified = datetime.fromtimestamp(stat.st_mtime)
                    age = datetime.now() - modified
                    
                    # Format size
                    if size > 1024*1024:
                        size_str = f"{size/(1024*1024):.1f}MB"
                    elif size > 1024:
                        size_str = f"{size/1024:.1f}KB"
                    else:
                        size_str = f"{size}B"
                    
                    # Color based on age
                    if age.total_seconds() < 300:  # 5 minutes
                        color = "\033[92m"  # Green
                        status = "RECENT"
                    elif age.total_seconds() < 3600:  # 1 hour
                        color = "\033[93m"  # Yellow
                        status = "OLD"
                    else:
                        color = "\033[91m"  # Red
                        status = "STALE"
                    
                    filename = os.path.basename(log_file)
                    print(f"{color}{filename:25} {size_str:8} {status:8} {age}\033[0m")
                else:
                    filename = os.path.basename(log_file)
                    print(f"\033[91m{filename:25} MISSING\033[0m")
                    
            except Exception as e:
                filename = os.path.basename(log_file)
                print(f"\033[91m{filename:25} ERROR: {e}\033[0m")
        
        print()
    
    def check_recent_activity(self):
        """Check recent activity in logs"""
        print("📊 Recent Activity (Last 10 minutes)")
        print("-" * 40)
        
        log_file = '/var/log/jellyfin/continuous_payment.log'
        
        try:
            if os.path.exists(log_file):
                # Get last 50 lines and filter for recent activity
                result = subprocess.run(['tail', '-50', log_file], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    recent_lines = []
                    
                    cutoff_time = datetime.now() - timedelta(minutes=10)
                    
                    for line in lines:
                        if line.strip():
                            try:
                                # Extract timestamp from log line
                                timestamp_str = line.split(' - ')[0]
                                log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                                
                                if log_time > cutoff_time:
                                    recent_lines.append(line)
                            except:
                                continue
                    
                    if recent_lines:
                        for line in recent_lines[-10:]:  # Show last 10
                            # Color code based on log level
                            if 'ERROR' in line:
                                print(f"\033[91m{line}\033[0m")
                            elif 'WARNING' in line:
                                print(f"\033[93m{line}\033[0m")
                            elif 'SUCCESS' in line or '✅' in line:
                                print(f"\033[92m{line}\033[0m")
                            else:
                                print(line)
                    else:
                        print("No recent activity found")
                else:
                    print("Error reading log file")
            else:
                print("Log file not found")
                
        except Exception as e:
            print(f"Error checking recent activity: {e}")
        
        print()
    
    def check_database_status(self):
        """Check database connectivity"""
        print("🗄️  Database Status")
        print("-" * 40)
        
        try:
            result = subprocess.run([
                'mysql', '-h', '*************', '-u', 'root', 
                '-p' + 'Wxmujwsofu@1234', 'jellyfin_registration',
                '-e', 'SELECT COUNT(*) as user_count FROM users; SELECT COUNT(*) as payment_count FROM payment_transaction WHERE status = "pending";'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ Database: \033[92mCONNECTED\033[0m")
                
                # Parse output for counts
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip() and not line.startswith('+') and not line.startswith('|'):
                        if line.isdigit():
                            print(f"   Users: {line}")
                        elif 'user_count' not in line and 'payment_count' not in line:
                            print(f"   Pending payments: {line}")
            else:
                print("❌ Database: \033[91mFAILED\033[0m")
                if result.stderr:
                    print(f"   Error: {result.stderr.strip()}")
                    
        except subprocess.TimeoutExpired:
            print("❌ Database: \033[91mTIMEOUT\033[0m")
        except Exception as e:
            print(f"❌ Database: \033[91mERROR - {e}\033[0m")
        
        print()
    
    def run_full_check(self):
        """Run complete system check"""
        print(f"🎬 Jellyfin by James - Production Monitor")
        print(f"Target: {self.base_url}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        print()
        
        self.check_service_status()
        self.check_api_health()
        self.check_database_status()
        self.check_log_files()
        self.check_recent_activity()
        
        print("=" * 50)
        print("Monitor completed")

def main():
    """Main function"""
    monitor = ProductionMonitor()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "api":
            monitor.check_api_health()
        elif command == "service":
            monitor.check_service_status()
        elif command == "logs":
            monitor.check_log_files()
        elif command == "activity":
            monitor.check_recent_activity()
        elif command == "database":
            monitor.check_database_status()
        else:
            print("Usage: python3 monitor_production.py [api|service|logs|activity|database]")
            sys.exit(1)
    else:
        monitor.run_full_check()

if __name__ == "__main__":
    main()
