<?php
// Linux Cron Job: PayNoi Payment Verification
// Optimized for production Linux environment (*************)
//
// Usage: php /var/www/html/cron/linux_payment_check.php
// Cron: every 5 seconds - /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory to web root
$webRoot = '/var/www/html';
if (is_dir($webRoot)) {
    chdir($webRoot);
} else {
    // For localhost development
    chdir(__DIR__ . '/..');
}

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/PayNoiAPIClass.php';
require_once 'includes/JellyfinAPI.php';

// Setup logging
$logFile = '/var/log/jellyfin/payment_cron.log';
$logDir = dirname($logFile);

// For localhost development, use local logs directory
if (!is_dir($logDir)) {
    $logFile = 'logs/payment_cron.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

writeLog("=== PayNoi Payment Check Started ===");

try {
    // Create database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Test database connection
    $pdo->query("SELECT 1");
    writeLog("✅ Database connection successful");
    
    // Get pending PayNoi payments from last 24 hours (using PHP timezone)
    $cutoffTime = date('Y-m-d H:i:s', strtotime('-24 hours')); // 24 hours ago in Asia/Bangkok
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'paynoi'
        AND pt.created_at >= ?
        ORDER BY pt.created_at ASC
        LIMIT 50
    ");
    $stmt->execute([$cutoffTime]);
    $pendingPayments = $stmt->fetchAll();
    
    writeLog("Found " . count($pendingPayments) . " pending PayNoi payments");
    
    if (empty($pendingPayments)) {
        writeLog("No pending payments to process");
        exit(0);
    }
    
    // Initialize PayNoi API
    if (!defined('PAYNOI_API_KEY') || !defined('PAYNOI_RECORD_KEY')) {
        writeLog("❌ PayNoi API keys not configured");
        exit(1);
    }

    writeLog("🔄 Fetching transactions from PayNoi API...");
    writeLog("API Key: " . substr(PAYNOI_API_KEY, 0, 10) . "...");
    writeLog("Record Key: " . PAYNOI_RECORD_KEY);

    // Fetch transactions directly from PayNoi API (like AutoPaymentChecker)
    $apiUrl = PAYNOI_API_URL . '?api_key=' . urlencode(PAYNOI_API_KEY) . '&record_key=' . urlencode(PAYNOI_RECORD_KEY);
    writeLog("API URL: " . $apiUrl);

    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => [
            'User-Agent: Mozilla/5.0 (compatible; JellyfinCron/1.0)'
        ]
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        writeLog("❌ PayNoi API cURL Error: {$error}");
        exit(1);
    }

    if ($httpCode !== 200) {
        writeLog("❌ PayNoi API HTTP Error: {$httpCode}");
        writeLog("Response: " . substr($response, 0, 500));
        exit(1);
    }

    writeLog("✅ PayNoi API responded with HTTP {$httpCode}");
    writeLog("Response length: " . strlen($response) . " bytes");

    $transactions = json_decode($response, true);
    if (!$transactions || !is_array($transactions)) {
        writeLog("❌ Failed to decode PayNoi transactions");
        writeLog("Raw response: " . substr($response, 0, 500));
        writeLog("JSON error: " . json_last_error_msg());
        exit(1);
    }

    writeLog("✅ Fetched " . count($transactions) . " PayNoi transactions");

    // Debug: Show sample transaction structure (only if no transactions processed yet)
    if (!empty($transactions) && count($pendingPayments) > 0) {
        $sample = $transactions[0];
        writeLog("Sample transaction keys: " . implode(', ', array_keys($sample)));

        // Show recent transactions (within 1 hour)
        $recentCount = 0;
        $now = time();
        foreach ($transactions as $transaction) {
            $transactionDate = $transaction['created_at'] ?? $transaction['datetime'] ?? $transaction['date'] ?? '';
            if ($transactionDate) {
                $transactionTime = strtotime($transactionDate);
                $timeDiff = $now - $transactionTime;
                if ($timeDiff <= 3600) { // Within 1 hour
                    $recentCount++;
                    $amount = $transaction['amount'] ?? $transaction['money'] ?? 0;
                    writeLog("Recent transaction: {$amount} THB at {$transactionDate}");
                }
            }
        }
        writeLog("Found {$recentCount} transactions within last hour");
    }
    
    $processedCount = 0;
    $verifiedCount = 0;
    $jellyfin = new JellyfinAPI();
    
    foreach ($pendingPayments as $payment) {
        $processedCount++;
        writeLog("Processing payment ID {$payment['id']} - User: {$payment['username']}, Amount: {$payment['amount']} THB");
        
        $matched = false;
        
        // Look for matching transaction
        foreach ($transactions as $transaction) {
            // Get transaction amount (PayNoi API may use different field names)
            $transactionAmount = floatval($transaction['amount'] ?? $transaction['money'] ?? 0);

            // Skip if not incoming transaction or zero amount
            if ($transactionAmount <= 0) {
                continue;
            }

            // Match by amount (with tolerance for random decimal 0.01-0.99)
            $amountDiff = abs($transactionAmount - $payment['amount']);
            if ($amountDiff <= 1.00) {
                // Check time difference (within 30 minutes)
                $paymentTime = strtotime($payment['created_at']);

                // PayNoi API may use different date field names
                $transactionDate = $transaction['created_at'] ?? $transaction['datetime'] ?? $transaction['date'] ?? '';
                $transactionTime = strtotime($transactionDate);
                $timeDiff = abs($paymentTime - $transactionTime);
                
                if ($timeDiff <= 1800) { // 30 minutes
                    writeLog("✅ Found matching transaction for payment ID {$payment['id']} - Amount: {$transactionAmount} THB, Time diff: " . round($timeDiff/60, 1) . " minutes");

                    try {
                        $pdo->beginTransaction();

                        // Update payment status with PHP timezone
                        $currentTime = date('Y-m-d H:i:s'); // Use PHP timezone (Asia/Bangkok)
                        $updateStmt = $pdo->prepare("
                            UPDATE payment_transactions
                            SET status = 'verified',
                                paynoi_transaction_id = ?,
                                verified_at = ?,
                                admin_notes = 'Auto-verified by cron'
                            WHERE id = ?
                        ");
                        $transactionId = $transaction['id'] ?? $transaction['trans_id'] ?? $transaction['transaction_id'] ?? '';
                        $updateStmt->execute([$transactionId, $currentTime, $payment['id']]);
                        
                        // Find Jellyfin user
                        $jellyfinUsers = $jellyfin->getUsers();
                        $jellyfinUser = null;
                        
                        foreach ($jellyfinUsers as $ju) {
                            if ($ju['Name'] === $payment['username']) {
                                $jellyfinUser = $ju;
                                break;
                            }
                        }
                        
                        if ($jellyfinUser) {
                            // Enable user and grant access to all libraries with session limit
                            $maxSessions = $payment['max_simultaneous_sessions'] ?? 1;
                            $enableResult = $jellyfin->enableAllLibraries($jellyfinUser['Id'], $maxSessions);

                            if ($enableResult) {
                                
                                // Calculate expiration date using PHP timezone
                                $durationDays = $payment['duration_days'] ?? 30; // Default 30 days if not found
                                $startDate = date('Y-m-d H:i:s'); // Current time in Asia/Bangkok
                                $expiryDate = date('Y-m-d H:i:s', strtotime("+{$durationDays} days"));

                                // Update subscription status if package_id exists
                                if ($payment['package_id']) {
                                    $subStmt = $pdo->prepare("
                                        UPDATE user_subscriptions
                                        SET start_date = COALESCE(start_date, ?),
                                            end_date = GREATEST(COALESCE(end_date, ?), ?),
                                            status = 'active',
                                            updated_at = ?
                                        WHERE id = ?
                                    ");
                                    $subStmt->execute([$startDate, $startDate, $expiryDate, $startDate, $payment['subscription_id']]);
                                }
                                
                                writeLog("✅ Enabled Jellyfin user: {$payment['username']}, Expires: {$expiryDate}");
                                $verifiedCount++;
                                $matched = true;
                                
                                // Log activity
                                $activityStmt = $pdo->prepare("
                                    INSERT INTO activity_logs (user_id, action, details, created_at)
                                    VALUES (?, 'payment_verified', ?, NOW())
                                ");
                                $activityStmt->execute([
                                    $payment['user_id'],
                                    "PayNoi payment verified automatically - Amount: {$payment['amount']} THB, Package: {$payment['package_name']}"
                                ]);
                                
                                $pdo->commit();
                                break;
                            } else {
                                writeLog("❌ Failed to enable Jellyfin user: {$payment['username']}");
                                $pdo->rollback();
                            }
                        } else {
                            writeLog("❌ Jellyfin user not found: {$payment['username']}");
                            $pdo->rollback();
                        }
                        
                    } catch (Exception $e) {
                        $pdo->rollback();
                        writeLog("❌ Error processing payment ID {$payment['id']}: " . $e->getMessage());
                    }
                }
            }
        }
        
        if (!$matched) {
            // Check if payment is old enough for manual review
            $paymentAge = time() - strtotime($payment['created_at']);
            if ($paymentAge > 7200) { // 2 hours
                try {
                    $stmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET admin_notes = 'Needs manual review - No matching PayNoi transaction found'
                        WHERE id = ?
                    ");
                    $stmt->execute([$payment['id']]);
                    writeLog("⚠️  Payment ID {$payment['id']} marked for manual review (age: " . round($paymentAge/3600, 1) . " hours)");
                } catch (Exception $e) {
                    writeLog("❌ Error updating payment ID {$payment['id']}: " . $e->getMessage());
                }
            }
        }
    }
    
    writeLog("=== PayNoi Payment Check Completed ===");
    writeLog("Processed: {$processedCount}, Verified: {$verifiedCount}");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
