<?php
// Linux Cron Job: PayNoi Payment Verification
// Optimized for production Linux environment (*************)
//
// Usage: php /var/www/html/cron/linux_payment_check.php
// Cron: every 5 seconds - /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory to web root
$webRoot = '/var/www/html';
chdir($webRoot);

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/PayNoiAPIClass.php';
require_once 'includes/JellyfinAPI.php';

// Setup logging
$logFile = '/var/log/jellyfin/payment_cron.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

writeLog("=== PayNoi Payment Check Started ===");

try {
    // Test database connection
    $pdo->query("SELECT 1");
    writeLog("✅ Database connection successful");
    
    // Get pending PayNoi payments from last 24 hours
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id, p.name as package_name, p.duration_days, p.max_simultaneous_sessions
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'paynoi'
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at ASC
        LIMIT 50
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll();
    
    writeLog("Found " . count($pendingPayments) . " pending PayNoi payments");
    
    if (empty($pendingPayments)) {
        writeLog("No pending payments to process");
        exit(0);
    }
    
    // Initialize PayNoi API
    if (!defined('PAYNOI_API_KEY') || !defined('PAYNOI_RECORD_KEY')) {
        writeLog("❌ PayNoi API keys not configured");
        exit(1);
    }
    
    $paynoiAPI = new PayNoiAPI(PAYNOI_API_KEY, PAYNOI_RECORD_KEY);
    $transactions = $paynoiAPI->getTransactions();
    
    if (!$transactions || !isset($transactions['data'])) {
        writeLog("❌ Failed to fetch PayNoi transactions");
        exit(1);
    }
    
    writeLog("✅ Fetched " . count($transactions['data']) . " PayNoi transactions");
    
    $processedCount = 0;
    $verifiedCount = 0;
    $jellyfin = new JellyfinAPI();
    
    foreach ($pendingPayments as $payment) {
        $processedCount++;
        writeLog("Processing payment ID {$payment['id']} - User: {$payment['username']}, Amount: {$payment['amount']} THB");
        
        $matched = false;
        
        // Look for matching transaction
        foreach ($transactions['data'] as $transaction) {
            // Match by amount (with small tolerance)
            $amountDiff = abs($transaction['amount'] - $payment['amount']);
            if ($amountDiff <= 0.01) {
                // Check time difference (within 30 minutes)
                $paymentTime = strtotime($payment['created_at']);
                $transactionTime = strtotime($transaction['created_at']);
                $timeDiff = abs($paymentTime - $transactionTime);
                
                if ($timeDiff <= 1800) { // 30 minutes
                    writeLog("✅ Found matching transaction for payment ID {$payment['id']}");
                    
                    try {
                        $pdo->beginTransaction();
                        
                        // Update payment status
                        $updateStmt = $pdo->prepare("
                            UPDATE payment_transactions
                            SET status = 'verified',
                                paynoi_transaction_id = ?,
                                verified_at = NOW(),
                                admin_notes = 'Auto-verified by cron'
                            WHERE id = ?
                        ");
                        $updateStmt->execute([$transaction['id'], $payment['id']]);
                        
                        // Find Jellyfin user
                        $jellyfinUsers = $jellyfin->getUsers();
                        $jellyfinUser = null;
                        
                        foreach ($jellyfinUsers as $ju) {
                            if ($ju['Name'] === $payment['username']) {
                                $jellyfinUser = $ju;
                                break;
                            }
                        }
                        
                        if ($jellyfinUser) {
                            // Enable user
                            $enableResult = $jellyfin->setUserEnabled($jellyfinUser['Id'], true);
                            
                            if ($enableResult) {
                                // Set session limit
                                $maxSessions = $payment['max_simultaneous_sessions'] ?? 1;
                                $jellyfin->setUserSessionLimit($jellyfinUser['Id'], $maxSessions);
                                
                                // Calculate expiration date
                                $durationDays = $payment['duration_days'] ?? 30; // Default 30 days if not found
                                $expiryDate = date('Y-m-d H:i:s', strtotime("+{$durationDays} days"));
                                
                                // Update subscription status if package_id exists
                                if ($payment['package_id']) {
                                    $subStmt = $pdo->prepare("
                                        UPDATE user_subscriptions
                                        SET end_date = GREATEST(COALESCE(end_date, NOW()), ?),
                                            status = 'active',
                                            updated_at = NOW()
                                        WHERE id = ?
                                    ");
                                    $subStmt->execute([$expiryDate, $payment['subscription_id']]);
                                }
                                
                                writeLog("✅ Enabled Jellyfin user: {$payment['username']}, Expires: {$expiryDate}");
                                $verifiedCount++;
                                $matched = true;
                                
                                // Log activity
                                $activityStmt = $pdo->prepare("
                                    INSERT INTO activity_logs (user_id, action, details, created_at)
                                    VALUES (?, 'payment_verified', ?, NOW())
                                ");
                                $activityStmt->execute([
                                    $payment['user_id'],
                                    "PayNoi payment verified automatically - Amount: {$payment['amount']} THB, Package: {$payment['package_name']}"
                                ]);
                                
                                $pdo->commit();
                                break;
                            } else {
                                writeLog("❌ Failed to enable Jellyfin user: {$payment['username']}");
                                $pdo->rollback();
                            }
                        } else {
                            writeLog("❌ Jellyfin user not found: {$payment['username']}");
                            $pdo->rollback();
                        }
                        
                    } catch (Exception $e) {
                        $pdo->rollback();
                        writeLog("❌ Error processing payment ID {$payment['id']}: " . $e->getMessage());
                    }
                }
            }
        }
        
        if (!$matched) {
            // Check if payment is old enough for manual review
            $paymentAge = time() - strtotime($payment['created_at']);
            if ($paymentAge > 7200) { // 2 hours
                try {
                    $stmt = $pdo->prepare("
                        UPDATE payment_transactions
                        SET admin_notes = 'Needs manual review - No matching PayNoi transaction found'
                        WHERE id = ?
                    ");
                    $stmt->execute([$payment['id']]);
                    writeLog("⚠️  Payment ID {$payment['id']} marked for manual review (age: " . round($paymentAge/3600, 1) . " hours)");
                } catch (Exception $e) {
                    writeLog("❌ Error updating payment ID {$payment['id']}: " . $e->getMessage());
                }
            }
        }
    }
    
    writeLog("=== PayNoi Payment Check Completed ===");
    writeLog("Processed: {$processedCount}, Verified: {$verifiedCount}");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
