<?php
// Test LINE QR Code loading
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test LINE QR Code - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .qr-test {
            text-align: center;
            padding: 20px;
            background: rgba(0, 195, 0, 0.1);
            border: 1px solid #00c300;
            border-radius: 8px;
            margin: 20px 0;
        }
        .qr-container {
            display: inline-block;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 10px;
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #FFD700; text-align: center;">🧪 Test LINE QR Code</h1>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📋 File System Check</h2>
            <?php
            $lineDir = __DIR__ . '/line/';
            $lineFile = $lineDir . 'line.jpg';
            
            echo "<p><strong>LINE directory:</strong> " . $lineDir . "</p>";
            
            if (is_dir($lineDir)) {
                echo "<p class='success'>✅ LINE directory exists</p>";
                
                $files = scandir($lineDir);
                echo "<p><strong>Files in line directory:</strong></p><ul>";
                foreach ($files as $file) {
                    if ($file != '.' && $file != '..') {
                        $fullPath = $lineDir . $file;
                        $size = filesize($fullPath);
                        $readable = is_readable($fullPath) ? '✅' : '❌';
                        $type = mime_content_type($fullPath);
                        echo "<li>$readable $file (" . number_format($size) . " bytes, $type)</li>";
                    }
                }
                echo "</ul>";
                
                if (file_exists($lineFile)) {
                    echo "<p class='success'>✅ line.jpg exists</p>";
                    echo "<p><strong>File size:</strong> " . filesize($lineFile) . " bytes</p>";
                    echo "<p><strong>MIME type:</strong> " . mime_content_type($lineFile) . "</p>";
                    echo "<p><strong>Readable:</strong> " . (is_readable($lineFile) ? 'Yes' : 'No') . "</p>";
                } else {
                    echo "<p class='error'>❌ line.jpg does not exist</p>";
                }
            } else {
                echo "<p class='error'>❌ LINE directory does not exist</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🖼️ Image Loading Test</h2>
            
            <div class="qr-test">
                <h3 style="color: #00c300;">Current LINE QR Code</h3>
                <div class="qr-container">
                    <img id="line-qr-current" 
                         src="line/line.jpg" 
                         alt="LINE QR Code" 
                         style="width: 200px; height: 200px; object-fit: contain;"
                         onload="document.getElementById('status-current').innerHTML='<span class=success>✅ Loaded successfully</span>'"
                         onerror="document.getElementById('status-current').innerHTML='<span class=error>❌ Failed to load</span>'">
                </div>
                <div id="status-current" class="info">🔄 Loading...</div>
            </div>
            
            <div class="qr-test">
                <h3 style="color: #00c300;">Alternative Paths Test</h3>
                
                <div class="qr-container">
                    <h4>./line/line.jpg</h4>
                    <img src="./line/line.jpg" 
                         alt="LINE QR Code" 
                         style="width: 150px; height: 150px; object-fit: contain;"
                         onload="this.nextElementSibling.innerHTML='<span class=success>✅ OK</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'">
                    <div class="info">🔄 Testing...</div>
                </div>
                
                <div class="qr-container">
                    <h4>/line/line.jpg</h4>
                    <img src="/line/line.jpg" 
                         alt="LINE QR Code" 
                         style="width: 150px; height: 150px; object-fit: contain;"
                         onload="this.nextElementSibling.innerHTML='<span class=success>✅ OK</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=error>❌ Failed</span>'">
                    <div class="info">🔄 Testing...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🔧 Generate Sample QR Code</h2>
            <p>หากไฟล์ line.jpg ไม่มี ให้ใช้ QR Code ตัวอย่างนี้:</p>
            
            <div class="qr-test">
                <h3 style="color: #00c300;">Sample LINE QR Code</h3>
                <div class="qr-container">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://line.me/ti/g/sample-group-id" 
                         alt="Sample LINE QR Code" 
                         style="width: 200px; height: 200px;">
                </div>
                <p style="color: #ccc; margin-top: 10px;">
                    QR Code ตัวอย่างสำหรับ LINE Group<br>
                    <small>(ใช้ API สร้าง QR Code อัตโนมัติ)</small>
                </p>
                
                <button onclick="downloadSampleQR()" style="background: #00c300; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                    💾 ดาวน์โหลด Sample QR Code
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📝 Instructions</h2>
            <div style="color: #ccc; line-height: 1.6;">
                <h3 style="color: #FFD700;">วิธีแก้ไขปัญหา:</h3>
                <ol>
                    <li><strong>หากไม่มีไฟล์ line.jpg:</strong>
                        <ul>
                            <li>สร้างโฟลเดอร์ <code>line</code> ในรูทของเว็บไซต์</li>
                            <li>อัปโหลดไฟล์ QR Code LINE ชื่อ <code>line.jpg</code></li>
                            <li>ตรวจสอบว่าไฟล์มีขนาดไม่เกิน 2MB</li>
                        </ul>
                    </li>
                    <li><strong>หากไฟล์มีแต่ไม่แสดง:</strong>
                        <ul>
                            <li>ตรวจสอบ permissions ของไฟล์ (ควรเป็น 644)</li>
                            <li>ตรวจสอบว่าไฟล์เป็น JPEG format</li>
                            <li>ลองเปลี่ยนชื่อไฟล์เป็น <code>line.png</code> และแก้โค้ด</li>
                        </ul>
                    </li>
                    <li><strong>ใช้ QR Code Generator:</strong>
                        <ul>
                            <li>ใช้ <a href="https://api.qrserver.com" target="_blank" style="color: #00c300;">QR Server API</a></li>
                            <li>สร้าง QR Code จาก LINE Group URL</li>
                            <li>บันทึกเป็นไฟล์ JPG หรือ PNG</li>
                        </ul>
                    </li>
                </ol>
                
                <h3 style="color: #FFD700;">ขนาดและรูปแบบที่แนะนำ:</h3>
                <ul>
                    <li>ขนาด: 200x200 pixels ขึ้นไป</li>
                    <li>รูปแบบ: JPG หรือ PNG</li>
                    <li>คุณภาพ: สูง (เพื่อให้สแกนได้ง่าย)</li>
                    <li>ขนาดไฟล์: ไม่เกิน 2MB</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/guide" style="color: #00c300; text-decoration: none; padding: 10px 20px; border: 1px solid #00c300; border-radius: 5px; margin-right: 10px;">
                ← กลับไปหน้าคู่มือ
            </a>
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                🏠 หน้าแรก
            </a>
        </div>
    </div>
    
    <script>
        function downloadSampleQR() {
            const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=400x400&data=https://line.me/ti/g/sample-group-id';
            
            // Create download link
            const a = document.createElement('a');
            a.href = qrUrl;
            a.download = 'sample-line-qr-code.png';
            a.target = '_blank';
            
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
        
        // Log loading status
        document.addEventListener('DOMContentLoaded', function() {
            console.log('LINE QR Code test page loaded');
            
            // Test current path
            const currentImg = document.getElementById('line-qr-current');
            if (currentImg) {
                console.log('Testing LINE QR Code path:', currentImg.src);
            }
        });
    </script>
</body>
</html>
