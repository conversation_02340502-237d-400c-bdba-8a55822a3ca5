<?php
/**
 * API Packages Management สำหรับ Admin Tools
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

session_start();

// ตรวจสอบ admin authentication
if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

require_once '../../includes/config.php';

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            // ดึงรายการแพ็กเกจ
            $stmt = $pdo->prepare("SELECT id, name, price, duration_days, description, is_active, created_at FROM packages ORDER BY price ASC");
            $stmt->execute();
            $packages = $stmt->fetchAll();
            
            echo json_encode($packages);
            break;
            
        case 'POST':
            // เพิ่มแพ็กเกจใหม่
            $data = json_decode(file_get_contents('php://input'), true);
            
            $name = $data['name'] ?? '';
            $price = $data['price'] ?? 0;
            $duration_days = $data['duration_days'] ?? 0;
            $description = $data['description'] ?? '';
            $is_active = $data['is_active'] ?? true;
            
            if (empty($name) || $price <= 0 || $duration_days <= 0) {
                echo json_encode(['success' => false, 'message' => 'Invalid package data']);
                exit;
            }
            
            $stmt = $pdo->prepare("INSERT INTO packages (name, price, duration_days, description, is_active, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$name, $price, $duration_days, $description, $is_active]);
            
            echo json_encode(['success' => true, 'message' => 'Package created successfully']);
            break;
            
        case 'PUT':
            // แก้ไขแพ็กเกจ
            $data = json_decode(file_get_contents('php://input'), true);
            
            $package_id = $data['id'] ?? '';
            $name = $data['name'] ?? '';
            $price = $data['price'] ?? 0;
            $duration_days = $data['duration_days'] ?? 0;
            $description = $data['description'] ?? '';
            $is_active = $data['is_active'] ?? true;
            
            if (empty($package_id) || empty($name) || $price <= 0 || $duration_days <= 0) {
                echo json_encode(['success' => false, 'message' => 'Invalid package data']);
                exit;
            }
            
            $stmt = $pdo->prepare("UPDATE packages SET name = ?, price = ?, duration_days = ?, description = ?, is_active = ? WHERE id = ?");
            $stmt->execute([$name, $price, $duration_days, $description, $is_active, $package_id]);
            
            echo json_encode(['success' => true, 'message' => 'Package updated successfully']);
            break;
            
        case 'DELETE':
            // ลบแพ็กเกจ
            $package_id = $_GET['id'] ?? '';
            
            if (empty($package_id)) {
                echo json_encode(['success' => false, 'message' => 'Package ID required']);
                exit;
            }
            
            // ตรวจสอบว่ามีผู้ใช้ใช้แพ็กเกจนี้อยู่หรือไม่
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE package_id = ?");
            $stmt->execute([$package_id]);
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                echo json_encode(['success' => false, 'message' => 'Cannot delete package with active users']);
                exit;
            }
            
            $stmt = $pdo->prepare("DELETE FROM packages WHERE id = ?");
            $stmt->execute([$package_id]);
            
            echo json_encode(['success' => true, 'message' => 'Package deleted successfully']);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Packages API Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
