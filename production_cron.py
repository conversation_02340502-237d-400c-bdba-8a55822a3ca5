#!/usr/bin/env python3
"""
Production Cron Job for Jellyfin by <PERSON>
Automated payment checking and user management for Linux production environment
Target: 192.168.1.174 / embyjames.xyz
"""

import requests
import time
import json
import logging
import sys
import os
from datetime import datetime

class ProductionCronJob:
    def __init__(self):
        self.base_url = "https://embyjames.xyz"
        self.api_endpoints = {
            'payment_check': f"{self.base_url}/api/payment-check.php",
            'manual_check': f"{self.base_url}/api/manual-check.php", 
            'expiration_check': f"{self.base_url}/api/expiration-check.php",
            'health': f"{self.base_url}/api/health.php"
        }
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = "/var/log/jellyfin"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        log_file = f"{log_dir}/payment_cron.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def call_api(self, endpoint_name, method='POST', data=None):
        """Call API endpoint with error handling"""
        try:
            url = self.api_endpoints[endpoint_name]
            
            if method == 'POST':
                response = requests.post(url, json=data, timeout=30)
            else:
                response = requests.get(url, timeout=30)
                
            if response.status_code == 200:
                try:
                    result = response.json()
                    return True, result
                except json.JSONDecodeError:
                    # API might return plain text
                    return True, {"message": response.text}
            else:
                self.logger.error(f"API {endpoint_name} failed: HTTP {response.status_code}")
                return False, {"error": f"HTTP {response.status_code}"}
                
        except requests.exceptions.Timeout:
            self.logger.error(f"API {endpoint_name} timeout")
            return False, {"error": "Timeout"}
        except requests.exceptions.ConnectionError:
            self.logger.error(f"API {endpoint_name} connection error")
            return False, {"error": "Connection error"}
        except Exception as e:
            self.logger.error(f"API {endpoint_name} error: {str(e)}")
            return False, {"error": str(e)}
    
    def check_system_health(self):
        """Check system health"""
        self.logger.info("🏥 Checking system health...")
        success, result = self.call_api('health', 'GET')
        
        if success:
            self.logger.info("✅ System health check passed")
            return True
        else:
            self.logger.error("❌ System health check failed")
            return False
    
    def process_paynoi_payments(self):
        """Process PayNoi payments"""
        self.logger.info("💰 Processing PayNoi payments...")
        success, result = self.call_api('payment_check')
        
        if success:
            if 'processed' in result:
                count = result.get('processed', 0)
                self.logger.info(f"✅ PayNoi payments processed: {count}")
            else:
                self.logger.info("✅ PayNoi payment check completed")
            return True
        else:
            self.logger.error("❌ PayNoi payment processing failed")
            return False
    
    def check_manual_payments(self):
        """Check manual payment slips"""
        self.logger.info("📄 Checking manual payment slips...")
        success, result = self.call_api('manual_check')
        
        if success:
            if 'checked' in result:
                count = result.get('checked', 0)
                self.logger.info(f"✅ Manual slips checked: {count}")
            else:
                self.logger.info("✅ Manual payment check completed")
            return True
        else:
            self.logger.error("❌ Manual payment check failed")
            return False
    
    def check_user_expiration(self):
        """Check and process user expiration"""
        self.logger.info("⏰ Checking user expiration...")
        success, result = self.call_api('expiration_check')
        
        if success:
            if 'expired' in result:
                count = result.get('expired', 0)
                self.logger.info(f"✅ Expired users processed: {count}")
            else:
                self.logger.info("✅ User expiration check completed")
            return True
        else:
            self.logger.error("❌ User expiration check failed")
            return False
    
    def run_full_cycle(self):
        """Run complete payment and user management cycle"""
        self.logger.info("🚀 Starting full payment cycle...")
        start_time = datetime.now()
        
        # Check system health first
        if not self.check_system_health():
            self.logger.error("❌ System health check failed - aborting cycle")
            return False
        
        success_count = 0
        total_tasks = 3
        
        # Process PayNoi payments
        if self.process_paynoi_payments():
            success_count += 1
        
        # Check manual payments
        if self.check_manual_payments():
            success_count += 1
            
        # Check user expiration
        if self.check_user_expiration():
            success_count += 1
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.logger.info(f"🏁 Cycle completed: {success_count}/{total_tasks} tasks successful")
        self.logger.info(f"⏱️  Duration: {duration:.2f} seconds")
        
        return success_count == total_tasks
    
    def run_payment_only(self):
        """Run payment processing only (for frequent checks)"""
        self.logger.info("💳 Running payment-only cycle...")
        
        success_count = 0
        
        # Process PayNoi payments
        if self.process_paynoi_payments():
            success_count += 1
        
        # Check manual payments  
        if self.check_manual_payments():
            success_count += 1
            
        return success_count == 2

def main():
    """Main function for cron execution"""
    if len(sys.argv) < 2:
        print("Usage: python3 production_cron.py [payment|full|health]")
        print("  payment - Check payments only (every 5 seconds)")
        print("  full    - Full cycle including expiration (every 5 minutes)")
        print("  health  - Health check only")
        sys.exit(1)
    
    mode = sys.argv[1]
    cron = ProductionCronJob()
    
    if mode == "payment":
        success = cron.run_payment_only()
    elif mode == "full":
        success = cron.run_full_cycle()
    elif mode == "health":
        success = cron.check_system_health()
    else:
        print(f"Unknown mode: {mode}")
        sys.exit(1)
    
    # Exit with appropriate code for cron monitoring
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
