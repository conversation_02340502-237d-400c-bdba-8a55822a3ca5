[2025-07-03 02:15:03] === Expiration Check Started ===
[2025-07-03 02:15:03] Found 1 expired subscriptions
[2025-07-03 02:15:03] Processing expired subscription for user: test6 (expired: 2025-07-02 22:24:03)
[2025-07-03 02:15:04] ✅ Disabled Jellyfin user: test6 (ID: 3010cfbba22c48049bb1de54ecdef525)
[2025-07-03 02:15:04] ✅ Successfully processed expiration for user: test6
[2025-07-03 02:15:04] --- Checking for inconsistencies ---
[2025-07-03 02:15:04] === Expiration Check Completed ===
[2025-07-03 02:15:04] Processed: 1 expired subscriptions
[2025-07-03 02:15:04] Disabled: 1 Jellyfin accounts
[2025-07-03 02:15:04] Errors: 0
[2025-07-03 02:15:04] === End of Expiration Check ===
[2025-07-03 02:25:03] === Expiration Check Started ===
[2025-07-03 02:25:03] Found 0 expired subscriptions
[2025-07-03 02:25:03] --- Checking for inconsistencies ---
[2025-07-03 02:25:04] ❌ Error checking orphaned user test6: HTTP Error 502
[2025-07-03 02:25:04] === Expiration Check Completed ===
[2025-07-03 02:25:04] Processed: 0 expired subscriptions
[2025-07-03 02:25:04] Disabled: 0 Jellyfin accounts
[2025-07-03 02:25:04] Errors: 1
[2025-07-03 02:25:04] === End of Expiration Check ===
[2025-07-03 02:25:15] === Expiration Check Started ===
[2025-07-03 02:25:15] Found 0 expired subscriptions
[2025-07-03 02:25:15] --- Checking for inconsistencies ---
[2025-07-03 02:25:15] ❌ Error checking orphaned user test6: HTTP Error 502
[2025-07-03 02:25:15] === Expiration Check Completed ===
[2025-07-03 02:25:15] Processed: 0 expired subscriptions
[2025-07-03 02:25:15] Disabled: 0 Jellyfin accounts
[2025-07-03 02:25:15] Errors: 1
[2025-07-03 02:25:15] === End of Expiration Check ===
[2025-07-03 02:48:25] === Expiration Check Started ===
[2025-07-03 02:48:25] Found 0 expired subscriptions
[2025-07-03 02:48:25] --- Checking for inconsistencies ---
[2025-07-03 02:48:26] === Expiration Check Completed ===
[2025-07-03 02:48:26] Processed: 0 expired subscriptions
[2025-07-03 02:48:26] Disabled: 0 Jellyfin accounts
[2025-07-03 02:48:26] Errors: 0
[2025-07-03 02:48:26] === End of Expiration Check ===
[2025-07-03 03:01:38] === Expiration Check Started ===
[2025-07-03 03:01:38] Found 0 expired subscriptions
[2025-07-03 03:01:38] --- Checking for inconsistencies ---
[2025-07-03 03:01:38] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-03 03:01:38] === Expiration Check Completed ===
[2025-07-03 03:01:38] Processed: 0 expired subscriptions
[2025-07-03 03:01:38] Disabled: 0 Jellyfin accounts
[2025-07-03 03:01:38] Errors: 1
[2025-07-03 03:01:38] === End of Expiration Check ===
[2025-07-03 03:06:11] === Expiration Check Started ===
[2025-07-03 03:06:11] Found 0 expired subscriptions
[2025-07-03 03:06:11] --- Checking for inconsistencies ---
[2025-07-03 03:06:11] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-03 03:06:11] === Expiration Check Completed ===
[2025-07-03 03:06:11] Processed: 0 expired subscriptions
[2025-07-03 03:06:11] Disabled: 0 Jellyfin accounts
[2025-07-03 03:06:11] Errors: 1
[2025-07-03 03:06:11] === End of Expiration Check ===
[2025-07-03 03:06:43] === Expiration Check Started ===
[2025-07-03 03:06:43] Found 0 expired subscriptions
[2025-07-03 03:06:43] --- Checking for inconsistencies ---
[2025-07-03 03:06:43] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-03 03:06:43] === Expiration Check Completed ===
[2025-07-03 03:06:43] Processed: 0 expired subscriptions
[2025-07-03 03:06:43] Disabled: 0 Jellyfin accounts
[2025-07-03 03:06:43] Errors: 1
[2025-07-03 03:06:43] === End of Expiration Check ===
[2025-07-03 03:06:50] === Expiration Check Started ===
[2025-07-03 03:06:50] Found 0 expired subscriptions
[2025-07-03 03:06:50] --- Checking for inconsistencies ---
[2025-07-03 03:06:50] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-03 03:06:50] === Expiration Check Completed ===
[2025-07-03 03:06:50] Processed: 0 expired subscriptions
[2025-07-03 03:06:50] Disabled: 0 Jellyfin accounts
[2025-07-03 03:06:50] Errors: 1
[2025-07-03 03:06:50] === End of Expiration Check ===
[2025-07-03 03:07:16] === Expiration Check Started ===
[2025-07-03 03:07:16] Found 0 expired subscriptions
[2025-07-03 03:07:16] --- Checking for inconsistencies ---
[2025-07-03 03:07:16] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-03 03:07:16] === Expiration Check Completed ===
[2025-07-03 03:07:16] Processed: 0 expired subscriptions
[2025-07-03 03:07:16] Disabled: 0 Jellyfin accounts
[2025-07-03 03:07:16] Errors: 1
[2025-07-03 03:07:16] === End of Expiration Check ===
[2025-07-03 03:15:46] === Expiration Check Started ===
[2025-07-03 03:15:46] Found 0 expired subscriptions
[2025-07-03 03:15:46] --- Checking for inconsistencies ---
[2025-07-03 03:15:46] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-03 03:15:46] === Expiration Check Completed ===
[2025-07-03 03:15:46] Processed: 0 expired subscriptions
[2025-07-03 03:15:46] Disabled: 0 Jellyfin accounts
[2025-07-03 03:15:46] Errors: 1
[2025-07-03 03:15:46] === End of Expiration Check ===
[2025-07-03 03:37:36] === Expiration Check Started ===
[2025-07-03 03:37:36] Found 0 expired subscriptions
[2025-07-03 03:37:36] --- Checking for inconsistencies ---
[2025-07-03 03:37:37] === Expiration Check Completed ===
[2025-07-03 03:37:37] Processed: 0 expired subscriptions
[2025-07-03 03:37:37] Disabled: 0 Jellyfin accounts
[2025-07-03 03:37:37] Errors: 0
[2025-07-03 03:37:37] === End of Expiration Check ===
[2025-07-03 03:42:38] === Expiration Check Started ===
[2025-07-03 03:42:38] Found 0 expired subscriptions
[2025-07-03 03:42:38] --- Checking for inconsistencies ---
[2025-07-03 03:42:38] === Expiration Check Completed ===
[2025-07-03 03:42:38] Processed: 0 expired subscriptions
[2025-07-03 03:42:38] Disabled: 0 Jellyfin accounts
[2025-07-03 03:42:38] Errors: 0
[2025-07-03 03:42:38] === End of Expiration Check ===
[2025-07-03 03:54:43] === Expiration Check Started ===
[2025-07-03 03:54:43] Found 0 expired subscriptions
[2025-07-03 03:54:43] --- Checking for inconsistencies ---
[2025-07-03 03:54:44] === Expiration Check Completed ===
[2025-07-03 03:54:44] Processed: 0 expired subscriptions
[2025-07-03 03:54:44] Disabled: 0 Jellyfin accounts
[2025-07-03 03:54:44] Errors: 0
[2025-07-03 03:54:44] === End of Expiration Check ===
[2025-07-03 03:59:19] === Expiration Check Started ===
[2025-07-03 03:59:20] Found 0 expired subscriptions
[2025-07-03 03:59:20] --- Checking for inconsistencies ---
[2025-07-03 03:59:20] === Expiration Check Completed ===
[2025-07-03 03:59:20] Processed: 0 expired subscriptions
[2025-07-03 03:59:20] Disabled: 0 Jellyfin accounts
[2025-07-03 03:59:20] Errors: 0
[2025-07-03 03:59:20] === End of Expiration Check ===
[2025-07-03 04:08:13] === Expiration Check Started ===
[2025-07-03 04:08:13] Found 0 expired subscriptions
[2025-07-03 04:08:13] --- Checking for inconsistencies ---
[2025-07-03 04:08:13] === Expiration Check Completed ===
[2025-07-03 04:08:13] Processed: 0 expired subscriptions
[2025-07-03 04:08:13] Disabled: 0 Jellyfin accounts
[2025-07-03 04:08:13] Errors: 0
[2025-07-03 04:08:13] === End of Expiration Check ===
[2025-07-03 04:13:16] === Expiration Check Started ===
[2025-07-03 04:13:16] Found 0 expired subscriptions
[2025-07-03 04:13:16] --- Checking for inconsistencies ---
[2025-07-03 04:13:16] === Expiration Check Completed ===
[2025-07-03 04:13:16] Processed: 0 expired subscriptions
[2025-07-03 04:13:16] Disabled: 0 Jellyfin accounts
[2025-07-03 04:13:16] Errors: 0
[2025-07-03 04:13:16] === End of Expiration Check ===
[2025-07-03 04:18:17] === Expiration Check Started ===
[2025-07-03 04:18:17] Found 0 expired subscriptions
[2025-07-03 04:18:17] --- Checking for inconsistencies ---
[2025-07-03 04:18:17] === Expiration Check Completed ===
[2025-07-03 04:18:17] Processed: 0 expired subscriptions
[2025-07-03 04:18:17] Disabled: 0 Jellyfin accounts
[2025-07-03 04:18:17] Errors: 0
[2025-07-03 04:18:17] === End of Expiration Check ===
[2025-07-03 04:23:18] === Expiration Check Started ===
[2025-07-03 04:23:18] Found 0 expired subscriptions
[2025-07-03 04:23:18] --- Checking for inconsistencies ---
[2025-07-03 04:23:18] === Expiration Check Completed ===
[2025-07-03 04:23:18] Processed: 0 expired subscriptions
[2025-07-03 04:23:18] Disabled: 0 Jellyfin accounts
[2025-07-03 04:23:18] Errors: 0
[2025-07-03 04:23:18] === End of Expiration Check ===
[2025-07-03 04:28:18] === Expiration Check Started ===
[2025-07-03 04:28:18] Found 0 expired subscriptions
[2025-07-03 04:28:18] --- Checking for inconsistencies ---
[2025-07-03 04:28:18] === Expiration Check Completed ===
[2025-07-03 04:28:18] Processed: 0 expired subscriptions
[2025-07-03 04:28:18] Disabled: 0 Jellyfin accounts
[2025-07-03 04:28:18] Errors: 0
[2025-07-03 04:28:18] === End of Expiration Check ===
[2025-07-03 04:33:19] === Expiration Check Started ===
[2025-07-03 04:33:19] Found 0 expired subscriptions
[2025-07-03 04:33:19] --- Checking for inconsistencies ---
[2025-07-03 04:33:19] === Expiration Check Completed ===
[2025-07-03 04:33:19] Processed: 0 expired subscriptions
[2025-07-03 04:33:19] Disabled: 0 Jellyfin accounts
[2025-07-03 04:33:19] Errors: 0
[2025-07-03 04:33:19] === End of Expiration Check ===
[2025-07-03 04:38:20] === Expiration Check Started ===
[2025-07-03 04:38:20] Found 0 expired subscriptions
[2025-07-03 04:38:20] --- Checking for inconsistencies ---
[2025-07-03 04:38:20] === Expiration Check Completed ===
[2025-07-03 04:38:20] Processed: 0 expired subscriptions
[2025-07-03 04:38:20] Disabled: 0 Jellyfin accounts
[2025-07-03 04:38:20] Errors: 0
[2025-07-03 04:38:20] === End of Expiration Check ===
[2025-07-03 04:43:21] === Expiration Check Started ===
[2025-07-03 04:43:21] Found 0 expired subscriptions
[2025-07-03 04:43:21] --- Checking for inconsistencies ---
[2025-07-03 04:43:21] === Expiration Check Completed ===
[2025-07-03 04:43:21] Processed: 0 expired subscriptions
[2025-07-03 04:43:21] Disabled: 0 Jellyfin accounts
[2025-07-03 04:43:21] Errors: 0
[2025-07-03 04:43:21] === End of Expiration Check ===
[2025-07-03 04:48:21] === Expiration Check Started ===
[2025-07-03 04:48:21] Found 0 expired subscriptions
[2025-07-03 04:48:21] --- Checking for inconsistencies ---
[2025-07-03 04:48:21] === Expiration Check Completed ===
[2025-07-03 04:48:21] Processed: 0 expired subscriptions
[2025-07-03 04:48:21] Disabled: 0 Jellyfin accounts
[2025-07-03 04:48:21] Errors: 0
[2025-07-03 04:48:21] === End of Expiration Check ===
[2025-07-03 04:53:22] === Expiration Check Started ===
[2025-07-03 04:53:22] Found 0 expired subscriptions
[2025-07-03 04:53:22] --- Checking for inconsistencies ---
[2025-07-03 04:53:22] === Expiration Check Completed ===
[2025-07-03 04:53:22] Processed: 0 expired subscriptions
[2025-07-03 04:53:22] Disabled: 0 Jellyfin accounts
[2025-07-03 04:53:22] Errors: 0
[2025-07-03 04:53:22] === End of Expiration Check ===
[2025-07-03 04:58:23] === Expiration Check Started ===
[2025-07-03 04:58:23] Found 0 expired subscriptions
[2025-07-03 04:58:23] --- Checking for inconsistencies ---
[2025-07-03 04:58:23] === Expiration Check Completed ===
[2025-07-03 04:58:23] Processed: 0 expired subscriptions
[2025-07-03 04:58:23] Disabled: 0 Jellyfin accounts
[2025-07-03 04:58:23] Errors: 0
[2025-07-03 04:58:23] === End of Expiration Check ===
[2025-07-03 05:03:23] === Expiration Check Started ===
[2025-07-03 05:03:23] Found 0 expired subscriptions
[2025-07-03 05:03:23] --- Checking for inconsistencies ---
[2025-07-03 05:03:23] === Expiration Check Completed ===
[2025-07-03 05:03:23] Processed: 0 expired subscriptions
[2025-07-03 05:03:23] Disabled: 0 Jellyfin accounts
[2025-07-03 05:03:23] Errors: 0
[2025-07-03 05:03:23] === End of Expiration Check ===
[2025-07-03 05:08:24] === Expiration Check Started ===
[2025-07-03 05:08:24] Found 0 expired subscriptions
[2025-07-03 05:08:24] --- Checking for inconsistencies ---
[2025-07-03 05:08:24] === Expiration Check Completed ===
[2025-07-03 05:08:24] Processed: 0 expired subscriptions
[2025-07-03 05:08:24] Disabled: 0 Jellyfin accounts
[2025-07-03 05:08:24] Errors: 0
[2025-07-03 05:08:24] === End of Expiration Check ===
[2025-07-03 05:13:25] === Expiration Check Started ===
[2025-07-03 05:13:25] Found 0 expired subscriptions
[2025-07-03 05:13:25] --- Checking for inconsistencies ---
[2025-07-03 05:13:25] === Expiration Check Completed ===
[2025-07-03 05:13:25] Processed: 0 expired subscriptions
[2025-07-03 05:13:25] Disabled: 0 Jellyfin accounts
[2025-07-03 05:13:25] Errors: 0
[2025-07-03 05:13:25] === End of Expiration Check ===
[2025-07-03 05:18:25] === Expiration Check Started ===
[2025-07-03 05:18:25] Found 0 expired subscriptions
[2025-07-03 05:18:25] --- Checking for inconsistencies ---
[2025-07-03 05:18:25] === Expiration Check Completed ===
[2025-07-03 05:18:25] Processed: 0 expired subscriptions
[2025-07-03 05:18:25] Disabled: 0 Jellyfin accounts
[2025-07-03 05:18:25] Errors: 0
[2025-07-03 05:18:25] === End of Expiration Check ===
[2025-07-03 05:23:26] === Expiration Check Started ===
[2025-07-03 05:23:26] Found 0 expired subscriptions
[2025-07-03 05:23:26] --- Checking for inconsistencies ---
[2025-07-03 05:23:26] === Expiration Check Completed ===
[2025-07-03 05:23:26] Processed: 0 expired subscriptions
[2025-07-03 05:23:26] Disabled: 0 Jellyfin accounts
[2025-07-03 05:23:26] Errors: 0
[2025-07-03 05:23:26] === End of Expiration Check ===
[2025-07-03 05:28:27] === Expiration Check Started ===
[2025-07-03 05:28:27] Found 0 expired subscriptions
[2025-07-03 05:28:27] --- Checking for inconsistencies ---
[2025-07-03 05:28:27] === Expiration Check Completed ===
[2025-07-03 05:28:27] Processed: 0 expired subscriptions
[2025-07-03 05:28:27] Disabled: 0 Jellyfin accounts
[2025-07-03 05:28:27] Errors: 0
[2025-07-03 05:28:27] === End of Expiration Check ===
[2025-07-03 05:33:27] === Expiration Check Started ===
[2025-07-03 05:33:27] Found 0 expired subscriptions
[2025-07-03 05:33:27] --- Checking for inconsistencies ---
[2025-07-03 05:33:27] === Expiration Check Completed ===
[2025-07-03 05:33:27] Processed: 0 expired subscriptions
[2025-07-03 05:33:27] Disabled: 0 Jellyfin accounts
[2025-07-03 05:33:27] Errors: 0
[2025-07-03 05:33:27] === End of Expiration Check ===
[2025-07-03 05:38:28] === Expiration Check Started ===
[2025-07-03 05:38:28] Found 0 expired subscriptions
[2025-07-03 05:38:28] --- Checking for inconsistencies ---
[2025-07-03 05:38:28] === Expiration Check Completed ===
[2025-07-03 05:38:28] Processed: 0 expired subscriptions
[2025-07-03 05:38:28] Disabled: 0 Jellyfin accounts
[2025-07-03 05:38:28] Errors: 0
[2025-07-03 05:38:28] === End of Expiration Check ===
[2025-07-03 05:43:28] === Expiration Check Started ===
[2025-07-03 05:43:28] Found 0 expired subscriptions
[2025-07-03 05:43:28] --- Checking for inconsistencies ---
[2025-07-03 05:43:28] === Expiration Check Completed ===
[2025-07-03 05:43:28] Processed: 0 expired subscriptions
[2025-07-03 05:43:28] Disabled: 0 Jellyfin accounts
[2025-07-03 05:43:28] Errors: 0
[2025-07-03 05:43:28] === End of Expiration Check ===
[2025-07-03 05:48:29] === Expiration Check Started ===
[2025-07-03 05:48:29] Found 0 expired subscriptions
[2025-07-03 05:48:29] --- Checking for inconsistencies ---
[2025-07-03 05:48:29] === Expiration Check Completed ===
[2025-07-03 05:48:29] Processed: 0 expired subscriptions
[2025-07-03 05:48:29] Disabled: 0 Jellyfin accounts
[2025-07-03 05:48:29] Errors: 0
[2025-07-03 05:48:29] === End of Expiration Check ===
[2025-07-03 05:53:30] === Expiration Check Started ===
[2025-07-03 05:53:30] Found 0 expired subscriptions
[2025-07-03 05:53:30] --- Checking for inconsistencies ---
[2025-07-03 05:53:30] === Expiration Check Completed ===
[2025-07-03 05:53:30] Processed: 0 expired subscriptions
[2025-07-03 05:53:30] Disabled: 0 Jellyfin accounts
[2025-07-03 05:53:30] Errors: 0
[2025-07-03 05:53:30] === End of Expiration Check ===
[2025-07-03 05:58:30] === Expiration Check Started ===
[2025-07-03 05:58:30] Found 0 expired subscriptions
[2025-07-03 05:58:30] --- Checking for inconsistencies ---
[2025-07-03 05:58:30] === Expiration Check Completed ===
[2025-07-03 05:58:30] Processed: 0 expired subscriptions
[2025-07-03 05:58:30] Disabled: 0 Jellyfin accounts
[2025-07-03 05:58:30] Errors: 0
[2025-07-03 05:58:30] === End of Expiration Check ===
[2025-07-03 06:03:31] === Expiration Check Started ===
[2025-07-03 06:03:31] Found 0 expired subscriptions
[2025-07-03 06:03:31] --- Checking for inconsistencies ---
[2025-07-03 06:03:31] === Expiration Check Completed ===
[2025-07-03 06:03:31] Processed: 0 expired subscriptions
[2025-07-03 06:03:31] Disabled: 0 Jellyfin accounts
[2025-07-03 06:03:31] Errors: 0
[2025-07-03 06:03:31] === End of Expiration Check ===
[2025-07-03 06:08:32] === Expiration Check Started ===
[2025-07-03 06:08:32] Found 0 expired subscriptions
[2025-07-03 06:08:32] --- Checking for inconsistencies ---
[2025-07-03 06:08:32] === Expiration Check Completed ===
[2025-07-03 06:08:32] Processed: 0 expired subscriptions
[2025-07-03 06:08:32] Disabled: 0 Jellyfin accounts
[2025-07-03 06:08:32] Errors: 0
[2025-07-03 06:08:32] === End of Expiration Check ===
[2025-07-03 06:13:33] === Expiration Check Started ===
[2025-07-03 06:13:33] Found 0 expired subscriptions
[2025-07-03 06:13:33] --- Checking for inconsistencies ---
[2025-07-03 06:13:33] === Expiration Check Completed ===
[2025-07-03 06:13:33] Processed: 0 expired subscriptions
[2025-07-03 06:13:33] Disabled: 0 Jellyfin accounts
[2025-07-03 06:13:33] Errors: 0
[2025-07-03 06:13:33] === End of Expiration Check ===
[2025-07-03 06:18:34] === Expiration Check Started ===
[2025-07-03 06:18:34] Found 0 expired subscriptions
[2025-07-03 06:18:34] --- Checking for inconsistencies ---
[2025-07-03 06:18:34] === Expiration Check Completed ===
[2025-07-03 06:18:34] Processed: 0 expired subscriptions
[2025-07-03 06:18:34] Disabled: 0 Jellyfin accounts
[2025-07-03 06:18:34] Errors: 0
[2025-07-03 06:18:34] === End of Expiration Check ===
[2025-07-03 06:23:34] === Expiration Check Started ===
[2025-07-03 06:23:34] Found 0 expired subscriptions
[2025-07-03 06:23:34] --- Checking for inconsistencies ---
[2025-07-03 06:23:34] === Expiration Check Completed ===
[2025-07-03 06:23:34] Processed: 0 expired subscriptions
[2025-07-03 06:23:34] Disabled: 0 Jellyfin accounts
[2025-07-03 06:23:34] Errors: 0
[2025-07-03 06:23:34] === End of Expiration Check ===
[2025-07-03 06:28:35] === Expiration Check Started ===
[2025-07-03 06:28:35] Found 0 expired subscriptions
[2025-07-03 06:28:35] --- Checking for inconsistencies ---
[2025-07-03 06:28:35] === Expiration Check Completed ===
[2025-07-03 06:28:35] Processed: 0 expired subscriptions
[2025-07-03 06:28:35] Disabled: 0 Jellyfin accounts
[2025-07-03 06:28:35] Errors: 0
[2025-07-03 06:28:35] === End of Expiration Check ===
[2025-07-03 06:33:36] === Expiration Check Started ===
[2025-07-03 06:33:36] Found 0 expired subscriptions
[2025-07-03 06:33:36] --- Checking for inconsistencies ---
[2025-07-03 06:33:36] === Expiration Check Completed ===
[2025-07-03 06:33:36] Processed: 0 expired subscriptions
[2025-07-03 06:33:36] Disabled: 0 Jellyfin accounts
[2025-07-03 06:33:36] Errors: 0
[2025-07-03 06:33:36] === End of Expiration Check ===
[2025-07-03 06:38:36] === Expiration Check Started ===
[2025-07-03 06:38:36] Found 0 expired subscriptions
[2025-07-03 06:38:36] --- Checking for inconsistencies ---
[2025-07-03 06:38:36] === Expiration Check Completed ===
[2025-07-03 06:38:36] Processed: 0 expired subscriptions
[2025-07-03 06:38:36] Disabled: 0 Jellyfin accounts
[2025-07-03 06:38:36] Errors: 0
[2025-07-03 06:38:36] === End of Expiration Check ===
[2025-07-03 06:43:37] === Expiration Check Started ===
[2025-07-03 06:43:37] Found 0 expired subscriptions
[2025-07-03 06:43:37] --- Checking for inconsistencies ---
[2025-07-03 06:43:37] === Expiration Check Completed ===
[2025-07-03 06:43:37] Processed: 0 expired subscriptions
[2025-07-03 06:43:37] Disabled: 0 Jellyfin accounts
[2025-07-03 06:43:37] Errors: 0
[2025-07-03 06:43:37] === End of Expiration Check ===
[2025-07-03 06:48:38] === Expiration Check Started ===
[2025-07-03 06:48:38] Found 0 expired subscriptions
[2025-07-03 06:48:38] --- Checking for inconsistencies ---
[2025-07-03 06:48:38] === Expiration Check Completed ===
[2025-07-03 06:48:38] Processed: 0 expired subscriptions
[2025-07-03 06:48:38] Disabled: 0 Jellyfin accounts
[2025-07-03 06:48:38] Errors: 0
[2025-07-03 06:48:38] === End of Expiration Check ===
[2025-07-03 06:53:38] === Expiration Check Started ===
[2025-07-03 06:53:38] Found 0 expired subscriptions
[2025-07-03 06:53:38] --- Checking for inconsistencies ---
[2025-07-03 06:53:38] === Expiration Check Completed ===
[2025-07-03 06:53:38] Processed: 0 expired subscriptions
[2025-07-03 06:53:38] Disabled: 0 Jellyfin accounts
[2025-07-03 06:53:38] Errors: 0
[2025-07-03 06:53:38] === End of Expiration Check ===
[2025-07-03 06:58:39] === Expiration Check Started ===
[2025-07-03 06:58:39] Found 0 expired subscriptions
[2025-07-03 06:58:39] --- Checking for inconsistencies ---
[2025-07-03 06:58:39] === Expiration Check Completed ===
[2025-07-03 06:58:39] Processed: 0 expired subscriptions
[2025-07-03 06:58:39] Disabled: 0 Jellyfin accounts
[2025-07-03 06:58:39] Errors: 0
[2025-07-03 06:58:39] === End of Expiration Check ===
[2025-07-03 07:03:40] === Expiration Check Started ===
[2025-07-03 07:03:40] Found 0 expired subscriptions
[2025-07-03 07:03:40] --- Checking for inconsistencies ---
[2025-07-03 07:03:40] === Expiration Check Completed ===
[2025-07-03 07:03:40] Processed: 0 expired subscriptions
[2025-07-03 07:03:40] Disabled: 0 Jellyfin accounts
[2025-07-03 07:03:40] Errors: 0
[2025-07-03 07:03:40] === End of Expiration Check ===
[2025-07-03 07:08:40] === Expiration Check Started ===
[2025-07-03 07:08:40] Found 0 expired subscriptions
[2025-07-03 07:08:40] --- Checking for inconsistencies ---
[2025-07-03 07:08:40] === Expiration Check Completed ===
[2025-07-03 07:08:40] Processed: 0 expired subscriptions
[2025-07-03 07:08:40] Disabled: 0 Jellyfin accounts
[2025-07-03 07:08:40] Errors: 0
[2025-07-03 07:08:40] === End of Expiration Check ===
[2025-07-03 07:13:41] === Expiration Check Started ===
[2025-07-03 07:13:41] Found 0 expired subscriptions
[2025-07-03 07:13:41] --- Checking for inconsistencies ---
[2025-07-03 07:13:41] === Expiration Check Completed ===
[2025-07-03 07:13:41] Processed: 0 expired subscriptions
[2025-07-03 07:13:41] Disabled: 0 Jellyfin accounts
[2025-07-03 07:13:41] Errors: 0
[2025-07-03 07:13:41] === End of Expiration Check ===
[2025-07-03 07:18:42] === Expiration Check Started ===
[2025-07-03 07:18:42] Found 0 expired subscriptions
[2025-07-03 07:18:42] --- Checking for inconsistencies ---
[2025-07-03 07:18:42] === Expiration Check Completed ===
[2025-07-03 07:18:42] Processed: 0 expired subscriptions
[2025-07-03 07:18:42] Disabled: 0 Jellyfin accounts
[2025-07-03 07:18:42] Errors: 0
[2025-07-03 07:18:42] === End of Expiration Check ===
[2025-07-03 07:23:43] === Expiration Check Started ===
[2025-07-03 07:23:43] Found 0 expired subscriptions
[2025-07-03 07:23:43] --- Checking for inconsistencies ---
[2025-07-03 07:23:43] === Expiration Check Completed ===
[2025-07-03 07:23:43] Processed: 0 expired subscriptions
[2025-07-03 07:23:43] Disabled: 0 Jellyfin accounts
[2025-07-03 07:23:43] Errors: 0
[2025-07-03 07:23:43] === End of Expiration Check ===
[2025-07-03 07:28:44] === Expiration Check Started ===
[2025-07-03 07:28:44] Found 0 expired subscriptions
[2025-07-03 07:28:44] --- Checking for inconsistencies ---
[2025-07-03 07:28:44] === Expiration Check Completed ===
[2025-07-03 07:28:44] Processed: 0 expired subscriptions
[2025-07-03 07:28:44] Disabled: 0 Jellyfin accounts
[2025-07-03 07:28:44] Errors: 0
[2025-07-03 07:28:44] === End of Expiration Check ===
[2025-07-03 07:33:45] === Expiration Check Started ===
[2025-07-03 07:33:45] Found 0 expired subscriptions
[2025-07-03 07:33:45] --- Checking for inconsistencies ---
[2025-07-03 07:33:45] === Expiration Check Completed ===
[2025-07-03 07:33:45] Processed: 0 expired subscriptions
[2025-07-03 07:33:45] Disabled: 0 Jellyfin accounts
[2025-07-03 07:33:45] Errors: 0
[2025-07-03 07:33:45] === End of Expiration Check ===
[2025-07-03 07:38:46] === Expiration Check Started ===
[2025-07-03 07:38:46] Found 0 expired subscriptions
[2025-07-03 07:38:46] --- Checking for inconsistencies ---
[2025-07-03 07:38:46] === Expiration Check Completed ===
[2025-07-03 07:38:46] Processed: 0 expired subscriptions
[2025-07-03 07:38:46] Disabled: 0 Jellyfin accounts
[2025-07-03 07:38:46] Errors: 0
[2025-07-03 07:38:46] === End of Expiration Check ===
[2025-07-03 07:43:47] === Expiration Check Started ===
[2025-07-03 07:43:47] Found 0 expired subscriptions
[2025-07-03 07:43:47] --- Checking for inconsistencies ---
[2025-07-03 07:43:47] === Expiration Check Completed ===
[2025-07-03 07:43:47] Processed: 0 expired subscriptions
[2025-07-03 07:43:47] Disabled: 0 Jellyfin accounts
[2025-07-03 07:43:47] Errors: 0
[2025-07-03 07:43:47] === End of Expiration Check ===
[2025-07-03 07:48:48] === Expiration Check Started ===
[2025-07-03 07:48:48] Found 0 expired subscriptions
[2025-07-03 07:48:48] --- Checking for inconsistencies ---
[2025-07-03 07:48:48] === Expiration Check Completed ===
[2025-07-03 07:48:48] Processed: 0 expired subscriptions
[2025-07-03 07:48:48] Disabled: 0 Jellyfin accounts
[2025-07-03 07:48:48] Errors: 0
[2025-07-03 07:48:48] === End of Expiration Check ===
[2025-07-03 07:53:49] === Expiration Check Started ===
[2025-07-03 07:53:49] Found 0 expired subscriptions
[2025-07-03 07:53:49] --- Checking for inconsistencies ---
[2025-07-03 07:53:49] === Expiration Check Completed ===
[2025-07-03 07:53:49] Processed: 0 expired subscriptions
[2025-07-03 07:53:49] Disabled: 0 Jellyfin accounts
[2025-07-03 07:53:49] Errors: 0
[2025-07-03 07:53:49] === End of Expiration Check ===
[2025-07-03 07:58:49] === Expiration Check Started ===
[2025-07-03 07:58:49] Found 0 expired subscriptions
[2025-07-03 07:58:49] --- Checking for inconsistencies ---
[2025-07-03 07:58:50] === Expiration Check Completed ===
[2025-07-03 07:58:50] Processed: 0 expired subscriptions
[2025-07-03 07:58:50] Disabled: 0 Jellyfin accounts
[2025-07-03 07:58:50] Errors: 0
[2025-07-03 07:58:50] === End of Expiration Check ===
[2025-07-03 08:03:51] === Expiration Check Started ===
[2025-07-03 08:03:51] Found 0 expired subscriptions
[2025-07-03 08:03:51] --- Checking for inconsistencies ---
[2025-07-03 08:03:51] === Expiration Check Completed ===
[2025-07-03 08:03:51] Processed: 0 expired subscriptions
[2025-07-03 08:03:51] Disabled: 0 Jellyfin accounts
[2025-07-03 08:03:51] Errors: 0
[2025-07-03 08:03:51] === End of Expiration Check ===
[2025-07-03 08:08:52] === Expiration Check Started ===
[2025-07-03 08:08:52] Found 0 expired subscriptions
[2025-07-03 08:08:52] --- Checking for inconsistencies ---
[2025-07-03 08:08:52] === Expiration Check Completed ===
[2025-07-03 08:08:52] Processed: 0 expired subscriptions
[2025-07-03 08:08:52] Disabled: 0 Jellyfin accounts
[2025-07-03 08:08:52] Errors: 0
[2025-07-03 08:08:52] === End of Expiration Check ===
[2025-07-03 08:13:52] === Expiration Check Started ===
[2025-07-03 08:13:52] Found 0 expired subscriptions
[2025-07-03 08:13:52] --- Checking for inconsistencies ---
[2025-07-03 08:13:53] === Expiration Check Completed ===
[2025-07-03 08:13:53] Processed: 0 expired subscriptions
[2025-07-03 08:13:53] Disabled: 0 Jellyfin accounts
[2025-07-03 08:13:53] Errors: 0
[2025-07-03 08:13:53] === End of Expiration Check ===
[2025-07-03 08:18:54] === Expiration Check Started ===
[2025-07-03 08:18:54] Found 0 expired subscriptions
[2025-07-03 08:18:54] --- Checking for inconsistencies ---
[2025-07-03 08:18:55] === Expiration Check Completed ===
[2025-07-03 08:18:55] Processed: 0 expired subscriptions
[2025-07-03 08:18:55] Disabled: 0 Jellyfin accounts
[2025-07-03 08:18:55] Errors: 0
[2025-07-03 08:18:55] === End of Expiration Check ===
[2025-07-03 08:23:56] === Expiration Check Started ===
[2025-07-03 08:23:56] Found 0 expired subscriptions
[2025-07-03 08:23:56] --- Checking for inconsistencies ---
[2025-07-03 08:23:56] === Expiration Check Completed ===
[2025-07-03 08:23:56] Processed: 0 expired subscriptions
[2025-07-03 08:23:56] Disabled: 0 Jellyfin accounts
[2025-07-03 08:23:56] Errors: 0
[2025-07-03 08:23:56] === End of Expiration Check ===
[2025-07-03 08:28:56] === Expiration Check Started ===
[2025-07-03 08:28:56] Found 0 expired subscriptions
[2025-07-03 08:28:56] --- Checking for inconsistencies ---
[2025-07-03 08:28:57] === Expiration Check Completed ===
[2025-07-03 08:28:57] Processed: 0 expired subscriptions
[2025-07-03 08:28:57] Disabled: 0 Jellyfin accounts
[2025-07-03 08:28:57] Errors: 0
[2025-07-03 08:28:57] === End of Expiration Check ===
[2025-07-03 08:33:57] === Expiration Check Started ===
[2025-07-03 08:33:57] Found 0 expired subscriptions
[2025-07-03 08:33:57] --- Checking for inconsistencies ---
[2025-07-03 08:33:58] === Expiration Check Completed ===
[2025-07-03 08:33:58] Processed: 0 expired subscriptions
[2025-07-03 08:33:58] Disabled: 0 Jellyfin accounts
[2025-07-03 08:33:58] Errors: 0
[2025-07-03 08:33:58] === End of Expiration Check ===
[2025-07-03 08:38:59] === Expiration Check Started ===
[2025-07-03 08:38:59] Found 0 expired subscriptions
[2025-07-03 08:38:59] --- Checking for inconsistencies ---
[2025-07-03 08:38:59] === Expiration Check Completed ===
[2025-07-03 08:38:59] Processed: 0 expired subscriptions
[2025-07-03 08:38:59] Disabled: 0 Jellyfin accounts
[2025-07-03 08:38:59] Errors: 0
[2025-07-03 08:38:59] === End of Expiration Check ===
[2025-07-03 08:44:00] === Expiration Check Started ===
[2025-07-03 08:44:00] Found 0 expired subscriptions
[2025-07-03 08:44:00] --- Checking for inconsistencies ---
[2025-07-03 08:44:00] === Expiration Check Completed ===
[2025-07-03 08:44:00] Processed: 0 expired subscriptions
[2025-07-03 08:44:00] Disabled: 0 Jellyfin accounts
[2025-07-03 08:44:00] Errors: 0
[2025-07-03 08:44:00] === End of Expiration Check ===
[2025-07-03 08:49:01] === Expiration Check Started ===
[2025-07-03 08:49:01] Found 0 expired subscriptions
[2025-07-03 08:49:01] --- Checking for inconsistencies ---
[2025-07-03 08:49:01] === Expiration Check Completed ===
[2025-07-03 08:49:01] Processed: 0 expired subscriptions
[2025-07-03 08:49:01] Disabled: 0 Jellyfin accounts
[2025-07-03 08:49:01] Errors: 0
[2025-07-03 08:49:01] === End of Expiration Check ===
[2025-07-03 08:54:02] === Expiration Check Started ===
[2025-07-03 08:54:02] Found 0 expired subscriptions
[2025-07-03 08:54:02] --- Checking for inconsistencies ---
[2025-07-03 08:54:02] === Expiration Check Completed ===
[2025-07-03 08:54:02] Processed: 0 expired subscriptions
[2025-07-03 08:54:02] Disabled: 0 Jellyfin accounts
[2025-07-03 08:54:02] Errors: 0
[2025-07-03 08:54:02] === End of Expiration Check ===
[2025-07-03 08:59:03] === Expiration Check Started ===
[2025-07-03 08:59:03] Found 0 expired subscriptions
[2025-07-03 08:59:03] --- Checking for inconsistencies ---
[2025-07-03 08:59:03] === Expiration Check Completed ===
[2025-07-03 08:59:03] Processed: 0 expired subscriptions
[2025-07-03 08:59:03] Disabled: 0 Jellyfin accounts
[2025-07-03 08:59:03] Errors: 0
[2025-07-03 08:59:03] === End of Expiration Check ===
[2025-07-03 09:04:04] === Expiration Check Started ===
[2025-07-03 09:04:04] Found 0 expired subscriptions
[2025-07-03 09:04:04] --- Checking for inconsistencies ---
[2025-07-03 09:04:04] === Expiration Check Completed ===
[2025-07-03 09:04:04] Processed: 0 expired subscriptions
[2025-07-03 09:04:04] Disabled: 0 Jellyfin accounts
[2025-07-03 09:04:04] Errors: 0
[2025-07-03 09:04:04] === End of Expiration Check ===
[2025-07-03 09:09:05] === Expiration Check Started ===
[2025-07-03 09:09:05] Found 0 expired subscriptions
[2025-07-03 09:09:05] --- Checking for inconsistencies ---
[2025-07-03 09:09:05] === Expiration Check Completed ===
[2025-07-03 09:09:05] Processed: 0 expired subscriptions
[2025-07-03 09:09:05] Disabled: 0 Jellyfin accounts
[2025-07-03 09:09:05] Errors: 0
[2025-07-03 09:09:05] === End of Expiration Check ===
[2025-07-03 09:14:06] === Expiration Check Started ===
[2025-07-03 09:14:06] Found 0 expired subscriptions
[2025-07-03 09:14:06] --- Checking for inconsistencies ---
[2025-07-03 09:14:07] === Expiration Check Completed ===
[2025-07-03 09:14:07] Processed: 0 expired subscriptions
[2025-07-03 09:14:07] Disabled: 0 Jellyfin accounts
[2025-07-03 09:14:07] Errors: 0
[2025-07-03 09:14:07] === End of Expiration Check ===
[2025-07-03 09:19:08] === Expiration Check Started ===
[2025-07-03 09:19:08] Found 0 expired subscriptions
[2025-07-03 09:19:08] --- Checking for inconsistencies ---
[2025-07-03 09:19:08] === Expiration Check Completed ===
[2025-07-03 09:19:08] Processed: 0 expired subscriptions
[2025-07-03 09:19:08] Disabled: 0 Jellyfin accounts
[2025-07-03 09:19:08] Errors: 0
[2025-07-03 09:19:08] === End of Expiration Check ===
[2025-07-03 09:24:09] === Expiration Check Started ===
[2025-07-03 09:24:09] Found 0 expired subscriptions
[2025-07-03 09:24:09] --- Checking for inconsistencies ---
[2025-07-03 09:24:09] === Expiration Check Completed ===
[2025-07-03 09:24:09] Processed: 0 expired subscriptions
[2025-07-03 09:24:09] Disabled: 0 Jellyfin accounts
[2025-07-03 09:24:09] Errors: 0
[2025-07-03 09:24:09] === End of Expiration Check ===
[2025-07-03 09:29:10] === Expiration Check Started ===
[2025-07-03 09:29:10] Found 0 expired subscriptions
[2025-07-03 09:29:10] --- Checking for inconsistencies ---
[2025-07-03 09:29:10] === Expiration Check Completed ===
[2025-07-03 09:29:10] Processed: 0 expired subscriptions
[2025-07-03 09:29:10] Disabled: 0 Jellyfin accounts
[2025-07-03 09:29:10] Errors: 0
[2025-07-03 09:29:10] === End of Expiration Check ===
[2025-07-03 09:34:11] === Expiration Check Started ===
[2025-07-03 09:34:11] Found 0 expired subscriptions
[2025-07-03 09:34:11] --- Checking for inconsistencies ---
[2025-07-03 09:34:11] === Expiration Check Completed ===
[2025-07-03 09:34:11] Processed: 0 expired subscriptions
[2025-07-03 09:34:11] Disabled: 0 Jellyfin accounts
[2025-07-03 09:34:11] Errors: 0
[2025-07-03 09:34:11] === End of Expiration Check ===
[2025-07-03 09:39:12] === Expiration Check Started ===
[2025-07-03 09:39:12] Found 0 expired subscriptions
[2025-07-03 09:39:12] --- Checking for inconsistencies ---
[2025-07-03 09:39:12] === Expiration Check Completed ===
[2025-07-03 09:39:12] Processed: 0 expired subscriptions
[2025-07-03 09:39:12] Disabled: 0 Jellyfin accounts
[2025-07-03 09:39:12] Errors: 0
[2025-07-03 09:39:12] === End of Expiration Check ===
[2025-07-03 09:44:14] === Expiration Check Started ===
[2025-07-03 09:44:14] Found 0 expired subscriptions
[2025-07-03 09:44:14] --- Checking for inconsistencies ---
[2025-07-03 09:44:14] === Expiration Check Completed ===
[2025-07-03 09:44:14] Processed: 0 expired subscriptions
[2025-07-03 09:44:14] Disabled: 0 Jellyfin accounts
[2025-07-03 09:44:14] Errors: 0
[2025-07-03 09:44:14] === End of Expiration Check ===
[2025-07-03 09:49:15] === Expiration Check Started ===
[2025-07-03 09:49:15] Found 0 expired subscriptions
[2025-07-03 09:49:15] --- Checking for inconsistencies ---
[2025-07-03 09:49:15] === Expiration Check Completed ===
[2025-07-03 09:49:15] Processed: 0 expired subscriptions
[2025-07-03 09:49:15] Disabled: 0 Jellyfin accounts
[2025-07-03 09:49:15] Errors: 0
[2025-07-03 09:49:15] === End of Expiration Check ===
[2025-07-03 09:54:16] === Expiration Check Started ===
[2025-07-03 09:54:16] Found 0 expired subscriptions
[2025-07-03 09:54:16] --- Checking for inconsistencies ---
[2025-07-03 09:54:16] === Expiration Check Completed ===
[2025-07-03 09:54:16] Processed: 0 expired subscriptions
[2025-07-03 09:54:16] Disabled: 0 Jellyfin accounts
[2025-07-03 09:54:16] Errors: 0
[2025-07-03 09:54:16] === End of Expiration Check ===
[2025-07-03 09:59:17] === Expiration Check Started ===
[2025-07-03 09:59:17] Found 0 expired subscriptions
[2025-07-03 09:59:17] --- Checking for inconsistencies ---
[2025-07-03 09:59:17] === Expiration Check Completed ===
[2025-07-03 09:59:17] Processed: 0 expired subscriptions
[2025-07-03 09:59:17] Disabled: 0 Jellyfin accounts
[2025-07-03 09:59:17] Errors: 0
[2025-07-03 09:59:17] === End of Expiration Check ===
[2025-07-03 10:04:18] === Expiration Check Started ===
[2025-07-03 10:04:18] Found 0 expired subscriptions
[2025-07-03 10:04:18] --- Checking for inconsistencies ---
[2025-07-03 10:04:18] === Expiration Check Completed ===
[2025-07-03 10:04:18] Processed: 0 expired subscriptions
[2025-07-03 10:04:18] Disabled: 0 Jellyfin accounts
[2025-07-03 10:04:18] Errors: 0
[2025-07-03 10:04:18] === End of Expiration Check ===
[2025-07-03 10:09:19] === Expiration Check Started ===
[2025-07-03 10:09:19] Found 0 expired subscriptions
[2025-07-03 10:09:19] --- Checking for inconsistencies ---
[2025-07-03 10:09:19] === Expiration Check Completed ===
[2025-07-03 10:09:19] Processed: 0 expired subscriptions
[2025-07-03 10:09:19] Disabled: 0 Jellyfin accounts
[2025-07-03 10:09:19] Errors: 0
[2025-07-03 10:09:19] === End of Expiration Check ===
[2025-07-03 10:14:20] === Expiration Check Started ===
[2025-07-03 10:14:20] Found 0 expired subscriptions
[2025-07-03 10:14:20] --- Checking for inconsistencies ---
[2025-07-03 10:14:20] === Expiration Check Completed ===
[2025-07-03 10:14:20] Processed: 0 expired subscriptions
[2025-07-03 10:14:20] Disabled: 0 Jellyfin accounts
[2025-07-03 10:14:20] Errors: 0
[2025-07-03 10:14:20] === End of Expiration Check ===
[2025-07-03 10:19:22] === Expiration Check Started ===
[2025-07-03 10:19:22] Found 0 expired subscriptions
[2025-07-03 10:19:22] --- Checking for inconsistencies ---
[2025-07-03 10:19:29] ❌ Error checking orphaned user test7: HTTP Error 502
[2025-07-03 10:19:29] === Expiration Check Completed ===
[2025-07-03 10:19:29] Processed: 0 expired subscriptions
[2025-07-03 10:19:29] Disabled: 0 Jellyfin accounts
[2025-07-03 10:19:29] Errors: 1
[2025-07-03 10:19:29] === End of Expiration Check ===
[2025-07-03 10:24:30] === Expiration Check Started ===
[2025-07-03 10:24:30] Found 0 expired subscriptions
[2025-07-03 10:24:30] --- Checking for inconsistencies ---
[2025-07-03 10:24:30] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-03 10:24:30] === Expiration Check Completed ===
[2025-07-03 10:24:30] Processed: 0 expired subscriptions
[2025-07-03 10:24:30] Disabled: 0 Jellyfin accounts
[2025-07-03 10:24:30] Errors: 1
[2025-07-03 10:24:30] === End of Expiration Check ===
[2025-07-03 10:29:32] === Expiration Check Started ===
[2025-07-03 10:29:32] Found 0 expired subscriptions
[2025-07-03 10:29:32] --- Checking for inconsistencies ---
[2025-07-03 10:29:32] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-03 10:29:32] === Expiration Check Completed ===
[2025-07-03 10:29:32] Processed: 0 expired subscriptions
[2025-07-03 10:29:32] Disabled: 0 Jellyfin accounts
[2025-07-03 10:29:32] Errors: 1
[2025-07-03 10:29:32] === End of Expiration Check ===
[2025-07-03 10:34:33] === Expiration Check Started ===
[2025-07-03 10:34:33] Found 0 expired subscriptions
[2025-07-03 10:34:33] --- Checking for inconsistencies ---
[2025-07-03 10:34:33] === Expiration Check Completed ===
[2025-07-03 10:34:33] Processed: 0 expired subscriptions
[2025-07-03 10:34:33] Disabled: 0 Jellyfin accounts
[2025-07-03 10:34:33] Errors: 0
[2025-07-03 10:34:33] === End of Expiration Check ===
[2025-07-03 10:39:35] === Expiration Check Started ===
[2025-07-03 10:39:35] Found 0 expired subscriptions
[2025-07-03 10:39:35] --- Checking for inconsistencies ---
[2025-07-03 10:39:36] === Expiration Check Completed ===
[2025-07-03 10:39:36] Processed: 0 expired subscriptions
[2025-07-03 10:39:36] Disabled: 0 Jellyfin accounts
[2025-07-03 10:39:36] Errors: 0
[2025-07-03 10:39:36] === End of Expiration Check ===
[2025-07-03 10:44:37] === Expiration Check Started ===
[2025-07-03 10:44:37] Found 0 expired subscriptions
[2025-07-03 10:44:37] --- Checking for inconsistencies ---
[2025-07-03 10:44:37] === Expiration Check Completed ===
[2025-07-03 10:44:37] Processed: 0 expired subscriptions
[2025-07-03 10:44:37] Disabled: 0 Jellyfin accounts
[2025-07-03 10:44:37] Errors: 0
[2025-07-03 10:44:37] === End of Expiration Check ===
[2025-07-03 10:49:39] === Expiration Check Started ===
[2025-07-03 10:49:39] Found 0 expired subscriptions
[2025-07-03 10:49:39] --- Checking for inconsistencies ---
[2025-07-03 10:49:39] === Expiration Check Completed ===
[2025-07-03 10:49:39] Processed: 0 expired subscriptions
[2025-07-03 10:49:39] Disabled: 0 Jellyfin accounts
[2025-07-03 10:49:39] Errors: 0
[2025-07-03 10:49:39] === End of Expiration Check ===
[2025-07-03 10:54:41] === Expiration Check Started ===
[2025-07-03 10:54:41] Found 0 expired subscriptions
[2025-07-03 10:54:41] --- Checking for inconsistencies ---
[2025-07-03 10:54:41] === Expiration Check Completed ===
[2025-07-03 10:54:41] Processed: 0 expired subscriptions
[2025-07-03 10:54:41] Disabled: 0 Jellyfin accounts
[2025-07-03 10:54:41] Errors: 0
[2025-07-03 10:54:41] === End of Expiration Check ===
[2025-07-03 10:59:42] === Expiration Check Started ===
[2025-07-03 10:59:42] Found 0 expired subscriptions
[2025-07-03 10:59:42] --- Checking for inconsistencies ---
[2025-07-03 10:59:42] === Expiration Check Completed ===
[2025-07-03 10:59:42] Processed: 0 expired subscriptions
[2025-07-03 10:59:42] Disabled: 0 Jellyfin accounts
[2025-07-03 10:59:42] Errors: 0
[2025-07-03 10:59:42] === End of Expiration Check ===
[2025-07-03 11:04:44] === Expiration Check Started ===
[2025-07-03 11:04:44] Found 0 expired subscriptions
[2025-07-03 11:04:44] --- Checking for inconsistencies ---
[2025-07-03 11:04:44] === Expiration Check Completed ===
[2025-07-03 11:04:44] Processed: 0 expired subscriptions
[2025-07-03 11:04:44] Disabled: 0 Jellyfin accounts
[2025-07-03 11:04:44] Errors: 0
[2025-07-03 11:04:44] === End of Expiration Check ===
[2025-07-03 11:09:46] === Expiration Check Started ===
[2025-07-03 11:09:46] Found 0 expired subscriptions
[2025-07-03 11:09:46] --- Checking for inconsistencies ---
[2025-07-03 11:09:46] === Expiration Check Completed ===
[2025-07-03 11:09:46] Processed: 0 expired subscriptions
[2025-07-03 11:09:46] Disabled: 0 Jellyfin accounts
[2025-07-03 11:09:46] Errors: 0
[2025-07-03 11:09:46] === End of Expiration Check ===
[2025-07-03 11:14:48] === Expiration Check Started ===
[2025-07-03 11:14:48] Found 0 expired subscriptions
[2025-07-03 11:14:48] --- Checking for inconsistencies ---
[2025-07-03 11:14:48] === Expiration Check Completed ===
[2025-07-03 11:14:48] Processed: 0 expired subscriptions
[2025-07-03 11:14:48] Disabled: 0 Jellyfin accounts
[2025-07-03 11:14:48] Errors: 0
[2025-07-03 11:14:48] === End of Expiration Check ===
[2025-07-03 11:19:50] === Expiration Check Started ===
[2025-07-03 11:19:50] Found 0 expired subscriptions
[2025-07-03 11:19:50] --- Checking for inconsistencies ---
[2025-07-03 11:19:50] === Expiration Check Completed ===
[2025-07-03 11:19:50] Processed: 0 expired subscriptions
[2025-07-03 11:19:50] Disabled: 0 Jellyfin accounts
[2025-07-03 11:19:50] Errors: 0
[2025-07-03 11:19:50] === End of Expiration Check ===
[2025-07-03 11:24:52] === Expiration Check Started ===
[2025-07-03 11:24:52] Found 0 expired subscriptions
[2025-07-03 11:24:52] --- Checking for inconsistencies ---
[2025-07-03 11:24:52] === Expiration Check Completed ===
[2025-07-03 11:24:52] Processed: 0 expired subscriptions
[2025-07-03 11:24:52] Disabled: 0 Jellyfin accounts
[2025-07-03 11:24:52] Errors: 0
[2025-07-03 11:24:52] === End of Expiration Check ===
[2025-07-03 11:29:54] === Expiration Check Started ===
[2025-07-03 11:29:54] Found 0 expired subscriptions
[2025-07-03 11:29:54] --- Checking for inconsistencies ---
[2025-07-03 11:29:54] === Expiration Check Completed ===
[2025-07-03 11:29:54] Processed: 0 expired subscriptions
[2025-07-03 11:29:54] Disabled: 0 Jellyfin accounts
[2025-07-03 11:29:54] Errors: 0
[2025-07-03 11:29:54] === End of Expiration Check ===
[2025-07-03 11:34:56] === Expiration Check Started ===
[2025-07-03 11:34:56] Found 0 expired subscriptions
[2025-07-03 11:34:56] --- Checking for inconsistencies ---
[2025-07-03 11:34:56] === Expiration Check Completed ===
[2025-07-03 11:34:56] Processed: 0 expired subscriptions
[2025-07-03 11:34:56] Disabled: 0 Jellyfin accounts
[2025-07-03 11:34:56] Errors: 0
[2025-07-03 11:34:56] === End of Expiration Check ===
[2025-07-03 11:39:58] === Expiration Check Started ===
[2025-07-03 11:39:58] Found 0 expired subscriptions
[2025-07-03 11:39:58] --- Checking for inconsistencies ---
[2025-07-03 11:39:58] === Expiration Check Completed ===
[2025-07-03 11:39:58] Processed: 0 expired subscriptions
[2025-07-03 11:39:58] Disabled: 0 Jellyfin accounts
[2025-07-03 11:39:58] Errors: 0
[2025-07-03 11:39:58] === End of Expiration Check ===
[2025-07-03 11:44:59] === Expiration Check Started ===
[2025-07-03 11:44:59] Found 0 expired subscriptions
[2025-07-03 11:44:59] --- Checking for inconsistencies ---
[2025-07-03 11:44:59] === Expiration Check Completed ===
[2025-07-03 11:44:59] Processed: 0 expired subscriptions
[2025-07-03 11:44:59] Disabled: 0 Jellyfin accounts
[2025-07-03 11:44:59] Errors: 0
[2025-07-03 11:44:59] === End of Expiration Check ===
[2025-07-03 11:50:01] === Expiration Check Started ===
[2025-07-03 11:50:01] Found 0 expired subscriptions
[2025-07-03 11:50:01] --- Checking for inconsistencies ---
[2025-07-03 11:50:01] === Expiration Check Completed ===
[2025-07-03 11:50:01] Processed: 0 expired subscriptions
[2025-07-03 11:50:01] Disabled: 0 Jellyfin accounts
[2025-07-03 11:50:01] Errors: 0
[2025-07-03 11:50:01] === End of Expiration Check ===
[2025-07-03 11:55:03] === Expiration Check Started ===
[2025-07-03 11:55:03] Found 0 expired subscriptions
[2025-07-03 11:55:03] --- Checking for inconsistencies ---
[2025-07-03 11:55:03] === Expiration Check Completed ===
[2025-07-03 11:55:03] Processed: 0 expired subscriptions
[2025-07-03 11:55:03] Disabled: 0 Jellyfin accounts
[2025-07-03 11:55:03] Errors: 0
[2025-07-03 11:55:03] === End of Expiration Check ===
[2025-07-03 12:00:05] === Expiration Check Started ===
[2025-07-03 12:00:05] Found 0 expired subscriptions
[2025-07-03 12:00:05] --- Checking for inconsistencies ---
[2025-07-03 12:00:06] === Expiration Check Completed ===
[2025-07-03 12:00:06] Processed: 0 expired subscriptions
[2025-07-03 12:00:06] Disabled: 0 Jellyfin accounts
[2025-07-03 12:00:06] Errors: 0
[2025-07-03 12:00:06] === End of Expiration Check ===
[2025-07-03 12:05:07] === Expiration Check Started ===
[2025-07-03 12:05:07] Found 0 expired subscriptions
[2025-07-03 12:05:07] --- Checking for inconsistencies ---
[2025-07-03 12:05:07] === Expiration Check Completed ===
[2025-07-03 12:05:07] Processed: 0 expired subscriptions
[2025-07-03 12:05:07] Disabled: 0 Jellyfin accounts
[2025-07-03 12:05:07] Errors: 0
[2025-07-03 12:05:07] === End of Expiration Check ===
[2025-07-03 12:10:09] === Expiration Check Started ===
[2025-07-03 12:10:09] Found 0 expired subscriptions
[2025-07-03 12:10:09] --- Checking for inconsistencies ---
[2025-07-03 12:10:09] === Expiration Check Completed ===
[2025-07-03 12:10:09] Processed: 0 expired subscriptions
[2025-07-03 12:10:09] Disabled: 0 Jellyfin accounts
[2025-07-03 12:10:09] Errors: 0
[2025-07-03 12:10:09] === End of Expiration Check ===
[2025-07-03 12:15:11] === Expiration Check Started ===
[2025-07-03 12:15:11] Found 0 expired subscriptions
[2025-07-03 12:15:11] --- Checking for inconsistencies ---
[2025-07-03 12:15:11] === Expiration Check Completed ===
[2025-07-03 12:15:11] Processed: 0 expired subscriptions
[2025-07-03 12:15:11] Disabled: 0 Jellyfin accounts
[2025-07-03 12:15:11] Errors: 0
[2025-07-03 12:15:11] === End of Expiration Check ===
[2025-07-03 12:20:13] === Expiration Check Started ===
[2025-07-03 12:20:13] Found 0 expired subscriptions
[2025-07-03 12:20:13] --- Checking for inconsistencies ---
[2025-07-03 12:20:13] === Expiration Check Completed ===
[2025-07-03 12:20:13] Processed: 0 expired subscriptions
[2025-07-03 12:20:13] Disabled: 0 Jellyfin accounts
[2025-07-03 12:20:13] Errors: 0
[2025-07-03 12:20:13] === End of Expiration Check ===
[2025-07-03 12:25:15] === Expiration Check Started ===
[2025-07-03 12:25:15] Found 0 expired subscriptions
[2025-07-03 12:25:15] --- Checking for inconsistencies ---
[2025-07-03 12:25:15] === Expiration Check Completed ===
[2025-07-03 12:25:15] Processed: 0 expired subscriptions
[2025-07-03 12:25:15] Disabled: 0 Jellyfin accounts
[2025-07-03 12:25:15] Errors: 0
[2025-07-03 12:25:15] === End of Expiration Check ===
[2025-07-03 12:30:17] === Expiration Check Started ===
[2025-07-03 12:30:17] Found 0 expired subscriptions
[2025-07-03 12:30:17] --- Checking for inconsistencies ---
[2025-07-03 12:30:17] === Expiration Check Completed ===
[2025-07-03 12:30:17] Processed: 0 expired subscriptions
[2025-07-03 12:30:17] Disabled: 0 Jellyfin accounts
[2025-07-03 12:30:17] Errors: 0
[2025-07-03 12:30:17] === End of Expiration Check ===
[2025-07-03 12:35:19] === Expiration Check Started ===
[2025-07-03 12:35:19] Found 0 expired subscriptions
[2025-07-03 12:35:19] --- Checking for inconsistencies ---
[2025-07-03 12:35:19] === Expiration Check Completed ===
[2025-07-03 12:35:19] Processed: 0 expired subscriptions
[2025-07-03 12:35:19] Disabled: 0 Jellyfin accounts
[2025-07-03 12:35:19] Errors: 0
[2025-07-03 12:35:19] === End of Expiration Check ===
[2025-07-03 12:40:21] === Expiration Check Started ===
[2025-07-03 12:40:21] Found 0 expired subscriptions
[2025-07-03 12:40:21] --- Checking for inconsistencies ---
[2025-07-03 12:40:21] === Expiration Check Completed ===
[2025-07-03 12:40:21] Processed: 0 expired subscriptions
[2025-07-03 12:40:21] Disabled: 0 Jellyfin accounts
[2025-07-03 12:40:21] Errors: 0
[2025-07-03 12:40:21] === End of Expiration Check ===
[2025-07-03 12:45:22] === Expiration Check Started ===
[2025-07-03 12:45:22] Found 0 expired subscriptions
[2025-07-03 12:45:22] --- Checking for inconsistencies ---
[2025-07-03 12:45:22] === Expiration Check Completed ===
[2025-07-03 12:45:22] Processed: 0 expired subscriptions
[2025-07-03 12:45:22] Disabled: 0 Jellyfin accounts
[2025-07-03 12:45:22] Errors: 0
[2025-07-03 12:45:22] === End of Expiration Check ===
[2025-07-03 12:50:23] === Expiration Check Started ===
[2025-07-03 12:50:23] Found 0 expired subscriptions
[2025-07-03 12:50:23] --- Checking for inconsistencies ---
[2025-07-03 12:50:23] === Expiration Check Completed ===
[2025-07-03 12:50:23] Processed: 0 expired subscriptions
[2025-07-03 12:50:23] Disabled: 0 Jellyfin accounts
[2025-07-03 12:50:23] Errors: 0
[2025-07-03 12:50:23] === End of Expiration Check ===
[2025-07-03 12:55:24] === Expiration Check Started ===
[2025-07-03 12:55:24] Found 0 expired subscriptions
[2025-07-03 12:55:24] --- Checking for inconsistencies ---
[2025-07-03 12:55:24] === Expiration Check Completed ===
[2025-07-03 12:55:24] Processed: 0 expired subscriptions
[2025-07-03 12:55:24] Disabled: 0 Jellyfin accounts
[2025-07-03 12:55:24] Errors: 0
[2025-07-03 12:55:24] === End of Expiration Check ===
[2025-07-03 13:00:24] === Expiration Check Started ===
[2025-07-03 13:00:24] Found 0 expired subscriptions
[2025-07-03 13:00:24] --- Checking for inconsistencies ---
[2025-07-03 13:00:24] === Expiration Check Completed ===
[2025-07-03 13:00:24] Processed: 0 expired subscriptions
[2025-07-03 13:00:24] Disabled: 0 Jellyfin accounts
[2025-07-03 13:00:24] Errors: 0
[2025-07-03 13:00:24] === End of Expiration Check ===
[2025-07-03 13:05:25] === Expiration Check Started ===
[2025-07-03 13:05:25] Found 0 expired subscriptions
[2025-07-03 13:05:25] --- Checking for inconsistencies ---
[2025-07-03 13:05:25] === Expiration Check Completed ===
[2025-07-03 13:05:25] Processed: 0 expired subscriptions
[2025-07-03 13:05:25] Disabled: 0 Jellyfin accounts
[2025-07-03 13:05:25] Errors: 0
[2025-07-03 13:05:25] === End of Expiration Check ===
[2025-07-03 13:10:26] === Expiration Check Started ===
[2025-07-03 13:10:26] Found 0 expired subscriptions
[2025-07-03 13:10:26] --- Checking for inconsistencies ---
[2025-07-03 13:10:26] === Expiration Check Completed ===
[2025-07-03 13:10:26] Processed: 0 expired subscriptions
[2025-07-03 13:10:26] Disabled: 0 Jellyfin accounts
[2025-07-03 13:10:26] Errors: 0
[2025-07-03 13:10:26] === End of Expiration Check ===
[2025-07-03 13:15:26] === Expiration Check Started ===
[2025-07-03 13:15:26] Found 0 expired subscriptions
[2025-07-03 13:15:26] --- Checking for inconsistencies ---
[2025-07-03 13:15:26] === Expiration Check Completed ===
[2025-07-03 13:15:26] Processed: 0 expired subscriptions
[2025-07-03 13:15:26] Disabled: 0 Jellyfin accounts
[2025-07-03 13:15:26] Errors: 0
[2025-07-03 13:15:26] === End of Expiration Check ===
[2025-07-03 13:20:27] === Expiration Check Started ===
[2025-07-03 13:20:27] Found 0 expired subscriptions
[2025-07-03 13:20:27] --- Checking for inconsistencies ---
[2025-07-03 13:20:27] === Expiration Check Completed ===
[2025-07-03 13:20:27] Processed: 0 expired subscriptions
[2025-07-03 13:20:27] Disabled: 0 Jellyfin accounts
[2025-07-03 13:20:27] Errors: 0
[2025-07-03 13:20:27] === End of Expiration Check ===
[2025-07-03 13:25:28] === Expiration Check Started ===
[2025-07-03 13:25:28] Found 0 expired subscriptions
[2025-07-03 13:25:28] --- Checking for inconsistencies ---
[2025-07-03 13:25:28] === Expiration Check Completed ===
[2025-07-03 13:25:28] Processed: 0 expired subscriptions
[2025-07-03 13:25:28] Disabled: 0 Jellyfin accounts
[2025-07-03 13:25:28] Errors: 0
[2025-07-03 13:25:28] === End of Expiration Check ===
[2025-07-03 13:30:29] === Expiration Check Started ===
[2025-07-03 13:30:29] Found 0 expired subscriptions
[2025-07-03 13:30:29] --- Checking for inconsistencies ---
[2025-07-03 13:30:29] === Expiration Check Completed ===
[2025-07-03 13:30:29] Processed: 0 expired subscriptions
[2025-07-03 13:30:29] Disabled: 0 Jellyfin accounts
[2025-07-03 13:30:29] Errors: 0
[2025-07-03 13:30:29] === End of Expiration Check ===
[2025-07-03 13:35:30] === Expiration Check Started ===
[2025-07-03 13:35:30] Found 0 expired subscriptions
[2025-07-03 13:35:30] --- Checking for inconsistencies ---
[2025-07-03 13:35:30] === Expiration Check Completed ===
[2025-07-03 13:35:30] Processed: 0 expired subscriptions
[2025-07-03 13:35:30] Disabled: 0 Jellyfin accounts
[2025-07-03 13:35:30] Errors: 0
[2025-07-03 13:35:30] === End of Expiration Check ===
[2025-07-03 13:40:30] === Expiration Check Started ===
[2025-07-03 13:40:30] Found 0 expired subscriptions
[2025-07-03 13:40:30] --- Checking for inconsistencies ---
[2025-07-03 13:40:30] === Expiration Check Completed ===
[2025-07-03 13:40:30] Processed: 0 expired subscriptions
[2025-07-03 13:40:30] Disabled: 0 Jellyfin accounts
[2025-07-03 13:40:30] Errors: 0
[2025-07-03 13:40:30] === End of Expiration Check ===
[2025-07-03 13:45:31] === Expiration Check Started ===
[2025-07-03 13:45:31] Found 0 expired subscriptions
[2025-07-03 13:45:31] --- Checking for inconsistencies ---
[2025-07-03 13:45:31] === Expiration Check Completed ===
[2025-07-03 13:45:31] Processed: 0 expired subscriptions
[2025-07-03 13:45:31] Disabled: 0 Jellyfin accounts
[2025-07-03 13:45:31] Errors: 0
[2025-07-03 13:45:31] === End of Expiration Check ===
[2025-07-03 13:50:32] === Expiration Check Started ===
[2025-07-03 13:50:32] Found 0 expired subscriptions
[2025-07-03 13:50:32] --- Checking for inconsistencies ---
[2025-07-03 13:50:32] === Expiration Check Completed ===
[2025-07-03 13:50:32] Processed: 0 expired subscriptions
[2025-07-03 13:50:32] Disabled: 0 Jellyfin accounts
[2025-07-03 13:50:32] Errors: 0
[2025-07-03 13:50:32] === End of Expiration Check ===
[2025-07-03 13:55:33] === Expiration Check Started ===
[2025-07-03 13:55:33] Found 0 expired subscriptions
[2025-07-03 13:55:33] --- Checking for inconsistencies ---
[2025-07-03 13:55:33] === Expiration Check Completed ===
[2025-07-03 13:55:33] Processed: 0 expired subscriptions
[2025-07-03 13:55:33] Disabled: 0 Jellyfin accounts
[2025-07-03 13:55:33] Errors: 0
[2025-07-03 13:55:33] === End of Expiration Check ===
[2025-07-03 14:00:34] === Expiration Check Started ===
[2025-07-03 14:00:34] Found 0 expired subscriptions
[2025-07-03 14:00:34] --- Checking for inconsistencies ---
[2025-07-03 14:00:34] === Expiration Check Completed ===
[2025-07-03 14:00:34] Processed: 0 expired subscriptions
[2025-07-03 14:00:34] Disabled: 0 Jellyfin accounts
[2025-07-03 14:00:34] Errors: 0
[2025-07-03 14:00:34] === End of Expiration Check ===
[2025-07-03 14:05:34] === Expiration Check Started ===
[2025-07-03 14:05:34] Found 0 expired subscriptions
[2025-07-03 14:05:34] --- Checking for inconsistencies ---
[2025-07-03 14:05:34] === Expiration Check Completed ===
[2025-07-03 14:05:34] Processed: 0 expired subscriptions
[2025-07-03 14:05:34] Disabled: 0 Jellyfin accounts
[2025-07-03 14:05:34] Errors: 0
[2025-07-03 14:05:34] === End of Expiration Check ===
[2025-07-03 14:10:35] === Expiration Check Started ===
[2025-07-03 14:10:35] Found 0 expired subscriptions
[2025-07-03 14:10:35] --- Checking for inconsistencies ---
[2025-07-03 14:10:35] === Expiration Check Completed ===
[2025-07-03 14:10:35] Processed: 0 expired subscriptions
[2025-07-03 14:10:35] Disabled: 0 Jellyfin accounts
[2025-07-03 14:10:35] Errors: 0
[2025-07-03 14:10:35] === End of Expiration Check ===
[2025-07-03 14:15:37] === Expiration Check Started ===
[2025-07-03 14:15:37] Found 0 expired subscriptions
[2025-07-03 14:15:37] --- Checking for inconsistencies ---
[2025-07-03 14:15:37] === Expiration Check Completed ===
[2025-07-03 14:15:37] Processed: 0 expired subscriptions
[2025-07-03 14:15:37] Disabled: 0 Jellyfin accounts
[2025-07-03 14:15:37] Errors: 0
[2025-07-03 14:15:37] === End of Expiration Check ===
[2025-07-03 14:20:38] === Expiration Check Started ===
[2025-07-03 14:20:38] Found 0 expired subscriptions
[2025-07-03 14:20:38] --- Checking for inconsistencies ---
[2025-07-03 14:20:38] === Expiration Check Completed ===
[2025-07-03 14:20:38] Processed: 0 expired subscriptions
[2025-07-03 14:20:38] Disabled: 0 Jellyfin accounts
[2025-07-03 14:20:38] Errors: 0
[2025-07-03 14:20:38] === End of Expiration Check ===
[2025-07-03 14:25:38] === Expiration Check Started ===
[2025-07-03 14:25:38] Found 0 expired subscriptions
[2025-07-03 14:25:38] --- Checking for inconsistencies ---
[2025-07-03 14:25:38] === Expiration Check Completed ===
[2025-07-03 14:25:38] Processed: 0 expired subscriptions
[2025-07-03 14:25:38] Disabled: 0 Jellyfin accounts
[2025-07-03 14:25:38] Errors: 0
[2025-07-03 14:25:38] === End of Expiration Check ===
[2025-07-03 14:30:39] === Expiration Check Started ===
[2025-07-03 14:30:39] Found 0 expired subscriptions
[2025-07-03 14:30:39] --- Checking for inconsistencies ---
[2025-07-03 14:30:39] === Expiration Check Completed ===
[2025-07-03 14:30:39] Processed: 0 expired subscriptions
[2025-07-03 14:30:39] Disabled: 0 Jellyfin accounts
[2025-07-03 14:30:39] Errors: 0
[2025-07-03 14:30:39] === End of Expiration Check ===
[2025-07-03 14:35:40] === Expiration Check Started ===
[2025-07-03 14:35:40] Found 0 expired subscriptions
[2025-07-03 14:35:40] --- Checking for inconsistencies ---
[2025-07-03 14:35:40] === Expiration Check Completed ===
[2025-07-03 14:35:40] Processed: 0 expired subscriptions
[2025-07-03 14:35:40] Disabled: 0 Jellyfin accounts
[2025-07-03 14:35:40] Errors: 0
[2025-07-03 14:35:40] === End of Expiration Check ===
[2025-07-03 14:40:41] === Expiration Check Started ===
[2025-07-03 14:40:41] Found 0 expired subscriptions
[2025-07-03 14:40:41] --- Checking for inconsistencies ---
[2025-07-03 14:40:41] === Expiration Check Completed ===
[2025-07-03 14:40:41] Processed: 0 expired subscriptions
[2025-07-03 14:40:41] Disabled: 0 Jellyfin accounts
[2025-07-03 14:40:41] Errors: 0
[2025-07-03 14:40:41] === End of Expiration Check ===
[2025-07-03 14:45:41] === Expiration Check Started ===
[2025-07-03 14:45:41] Found 0 expired subscriptions
[2025-07-03 14:45:41] --- Checking for inconsistencies ---
[2025-07-03 14:45:41] === Expiration Check Completed ===
[2025-07-03 14:45:41] Processed: 0 expired subscriptions
[2025-07-03 14:45:41] Disabled: 0 Jellyfin accounts
[2025-07-03 14:45:41] Errors: 0
[2025-07-03 14:45:41] === End of Expiration Check ===
[2025-07-03 14:50:42] === Expiration Check Started ===
[2025-07-03 14:50:42] Found 0 expired subscriptions
[2025-07-03 14:50:42] --- Checking for inconsistencies ---
[2025-07-03 14:50:42] === Expiration Check Completed ===
[2025-07-03 14:50:42] Processed: 0 expired subscriptions
[2025-07-03 14:50:42] Disabled: 0 Jellyfin accounts
[2025-07-03 14:50:42] Errors: 0
[2025-07-03 14:50:42] === End of Expiration Check ===
[2025-07-03 14:55:43] === Expiration Check Started ===
[2025-07-03 14:55:43] Found 0 expired subscriptions
[2025-07-03 14:55:43] --- Checking for inconsistencies ---
[2025-07-03 14:55:43] === Expiration Check Completed ===
[2025-07-03 14:55:43] Processed: 0 expired subscriptions
[2025-07-03 14:55:43] Disabled: 0 Jellyfin accounts
[2025-07-03 14:55:43] Errors: 0
[2025-07-03 14:55:43] === End of Expiration Check ===
[2025-07-03 15:00:44] === Expiration Check Started ===
[2025-07-03 15:00:44] Found 0 expired subscriptions
[2025-07-03 15:00:44] --- Checking for inconsistencies ---
[2025-07-03 15:00:44] === Expiration Check Completed ===
[2025-07-03 15:00:44] Processed: 0 expired subscriptions
[2025-07-03 15:00:44] Disabled: 0 Jellyfin accounts
[2025-07-03 15:00:44] Errors: 0
[2025-07-03 15:00:44] === End of Expiration Check ===
[2025-07-03 15:05:44] === Expiration Check Started ===
[2025-07-03 15:05:44] Found 0 expired subscriptions
[2025-07-03 15:05:44] --- Checking for inconsistencies ---
[2025-07-03 15:05:44] === Expiration Check Completed ===
[2025-07-03 15:05:44] Processed: 0 expired subscriptions
[2025-07-03 15:05:44] Disabled: 0 Jellyfin accounts
[2025-07-03 15:05:44] Errors: 0
[2025-07-03 15:05:44] === End of Expiration Check ===
[2025-07-03 15:10:45] === Expiration Check Started ===
[2025-07-03 15:10:45] Found 0 expired subscriptions
[2025-07-03 15:10:45] --- Checking for inconsistencies ---
[2025-07-03 15:10:45] === Expiration Check Completed ===
[2025-07-03 15:10:45] Processed: 0 expired subscriptions
[2025-07-03 15:10:45] Disabled: 0 Jellyfin accounts
[2025-07-03 15:10:45] Errors: 0
[2025-07-03 15:10:45] === End of Expiration Check ===
[2025-07-03 15:15:46] === Expiration Check Started ===
[2025-07-03 15:15:46] Found 0 expired subscriptions
[2025-07-03 15:15:46] --- Checking for inconsistencies ---
[2025-07-03 15:15:46] === Expiration Check Completed ===
[2025-07-03 15:15:46] Processed: 0 expired subscriptions
[2025-07-03 15:15:46] Disabled: 0 Jellyfin accounts
[2025-07-03 15:15:46] Errors: 0
[2025-07-03 15:15:46] === End of Expiration Check ===
[2025-07-03 15:20:46] === Expiration Check Started ===
[2025-07-03 15:20:46] Found 0 expired subscriptions
[2025-07-03 15:20:46] --- Checking for inconsistencies ---
[2025-07-03 15:20:46] === Expiration Check Completed ===
[2025-07-03 15:20:46] Processed: 0 expired subscriptions
[2025-07-03 15:20:46] Disabled: 0 Jellyfin accounts
[2025-07-03 15:20:46] Errors: 0
[2025-07-03 15:20:46] === End of Expiration Check ===
[2025-07-03 15:25:47] === Expiration Check Started ===
[2025-07-03 15:25:47] Found 0 expired subscriptions
[2025-07-03 15:25:47] --- Checking for inconsistencies ---
[2025-07-03 15:25:47] === Expiration Check Completed ===
[2025-07-03 15:25:47] Processed: 0 expired subscriptions
[2025-07-03 15:25:47] Disabled: 0 Jellyfin accounts
[2025-07-03 15:25:47] Errors: 0
[2025-07-03 15:25:47] === End of Expiration Check ===
[2025-07-03 15:30:48] === Expiration Check Started ===
[2025-07-03 15:30:48] Found 0 expired subscriptions
[2025-07-03 15:30:48] --- Checking for inconsistencies ---
[2025-07-03 15:30:48] === Expiration Check Completed ===
[2025-07-03 15:30:48] Processed: 0 expired subscriptions
[2025-07-03 15:30:48] Disabled: 0 Jellyfin accounts
[2025-07-03 15:30:48] Errors: 0
[2025-07-03 15:30:48] === End of Expiration Check ===
[2025-07-03 15:35:48] === Expiration Check Started ===
[2025-07-03 15:35:48] Found 0 expired subscriptions
[2025-07-03 15:35:48] --- Checking for inconsistencies ---
[2025-07-03 15:35:48] === Expiration Check Completed ===
[2025-07-03 15:35:48] Processed: 0 expired subscriptions
[2025-07-03 15:35:48] Disabled: 0 Jellyfin accounts
[2025-07-03 15:35:48] Errors: 0
[2025-07-03 15:35:48] === End of Expiration Check ===
[2025-07-03 15:40:49] === Expiration Check Started ===
[2025-07-03 15:40:49] Found 0 expired subscriptions
[2025-07-03 15:40:49] --- Checking for inconsistencies ---
[2025-07-03 15:40:49] === Expiration Check Completed ===
[2025-07-03 15:40:49] Processed: 0 expired subscriptions
[2025-07-03 15:40:49] Disabled: 0 Jellyfin accounts
[2025-07-03 15:40:49] Errors: 0
[2025-07-03 15:40:49] === End of Expiration Check ===
[2025-07-03 15:45:50] === Expiration Check Started ===
[2025-07-03 15:45:50] Found 0 expired subscriptions
[2025-07-03 15:45:50] --- Checking for inconsistencies ---
[2025-07-03 15:45:50] === Expiration Check Completed ===
[2025-07-03 15:45:50] Processed: 0 expired subscriptions
[2025-07-03 15:45:50] Disabled: 0 Jellyfin accounts
[2025-07-03 15:45:50] Errors: 0
[2025-07-03 15:45:50] === End of Expiration Check ===
[2025-07-03 15:50:51] === Expiration Check Started ===
[2025-07-03 15:50:51] Found 0 expired subscriptions
[2025-07-03 15:50:51] --- Checking for inconsistencies ---
[2025-07-03 15:50:51] === Expiration Check Completed ===
[2025-07-03 15:50:51] Processed: 0 expired subscriptions
[2025-07-03 15:50:51] Disabled: 0 Jellyfin accounts
[2025-07-03 15:50:51] Errors: 0
[2025-07-03 15:50:51] === End of Expiration Check ===
[2025-07-03 15:55:52] === Expiration Check Started ===
[2025-07-03 15:55:52] Found 0 expired subscriptions
[2025-07-03 15:55:52] --- Checking for inconsistencies ---
[2025-07-03 15:55:52] === Expiration Check Completed ===
[2025-07-03 15:55:52] Processed: 0 expired subscriptions
[2025-07-03 15:55:52] Disabled: 0 Jellyfin accounts
[2025-07-03 15:55:52] Errors: 0
[2025-07-03 15:55:52] === End of Expiration Check ===
[2025-07-03 16:00:53] === Expiration Check Started ===
[2025-07-03 16:00:53] Found 0 expired subscriptions
[2025-07-03 16:00:53] --- Checking for inconsistencies ---
[2025-07-03 16:00:53] === Expiration Check Completed ===
[2025-07-03 16:00:53] Processed: 0 expired subscriptions
[2025-07-03 16:00:53] Disabled: 0 Jellyfin accounts
[2025-07-03 16:00:53] Errors: 0
[2025-07-03 16:00:53] === End of Expiration Check ===
[2025-07-03 16:05:53] === Expiration Check Started ===
[2025-07-03 16:05:53] Found 0 expired subscriptions
[2025-07-03 16:05:53] --- Checking for inconsistencies ---
[2025-07-03 16:05:53] === Expiration Check Completed ===
[2025-07-03 16:05:53] Processed: 0 expired subscriptions
[2025-07-03 16:05:53] Disabled: 0 Jellyfin accounts
[2025-07-03 16:05:53] Errors: 0
[2025-07-03 16:05:53] === End of Expiration Check ===
[2025-07-03 16:10:54] === Expiration Check Started ===
[2025-07-03 16:10:54] Found 0 expired subscriptions
[2025-07-03 16:10:54] --- Checking for inconsistencies ---
[2025-07-03 16:10:54] === Expiration Check Completed ===
[2025-07-03 16:10:54] Processed: 0 expired subscriptions
[2025-07-03 16:10:54] Disabled: 0 Jellyfin accounts
[2025-07-03 16:10:54] Errors: 0
[2025-07-03 16:10:54] === End of Expiration Check ===
[2025-07-03 16:15:55] === Expiration Check Started ===
[2025-07-03 16:15:55] Found 0 expired subscriptions
[2025-07-03 16:15:55] --- Checking for inconsistencies ---
[2025-07-03 16:15:55] === Expiration Check Completed ===
[2025-07-03 16:15:55] Processed: 0 expired subscriptions
[2025-07-03 16:15:55] Disabled: 0 Jellyfin accounts
[2025-07-03 16:15:55] Errors: 0
[2025-07-03 16:15:55] === End of Expiration Check ===
[2025-07-03 16:20:55] === Expiration Check Started ===
[2025-07-03 16:20:55] Found 0 expired subscriptions
[2025-07-03 16:20:55] --- Checking for inconsistencies ---
[2025-07-03 16:20:55] === Expiration Check Completed ===
[2025-07-03 16:20:55] Processed: 0 expired subscriptions
[2025-07-03 16:20:55] Disabled: 0 Jellyfin accounts
[2025-07-03 16:20:55] Errors: 0
[2025-07-03 16:20:55] === End of Expiration Check ===
[2025-07-03 16:25:56] === Expiration Check Started ===
[2025-07-03 16:25:56] Found 0 expired subscriptions
[2025-07-03 16:25:56] --- Checking for inconsistencies ---
[2025-07-03 16:25:56] === Expiration Check Completed ===
[2025-07-03 16:25:56] Processed: 0 expired subscriptions
[2025-07-03 16:25:56] Disabled: 0 Jellyfin accounts
[2025-07-03 16:25:56] Errors: 0
[2025-07-03 16:25:56] === End of Expiration Check ===
[2025-07-03 16:30:57] === Expiration Check Started ===
[2025-07-03 16:30:57] Found 0 expired subscriptions
[2025-07-03 16:30:57] --- Checking for inconsistencies ---
[2025-07-03 16:30:57] === Expiration Check Completed ===
[2025-07-03 16:30:57] Processed: 0 expired subscriptions
[2025-07-03 16:30:57] Disabled: 0 Jellyfin accounts
[2025-07-03 16:30:57] Errors: 0
[2025-07-03 16:30:57] === End of Expiration Check ===
[2025-07-03 16:35:58] === Expiration Check Started ===
[2025-07-03 16:35:58] Found 0 expired subscriptions
[2025-07-03 16:35:58] --- Checking for inconsistencies ---
[2025-07-03 16:35:58] === Expiration Check Completed ===
[2025-07-03 16:35:58] Processed: 0 expired subscriptions
[2025-07-03 16:35:58] Disabled: 0 Jellyfin accounts
[2025-07-03 16:35:58] Errors: 0
[2025-07-03 16:35:58] === End of Expiration Check ===
[2025-07-03 16:40:59] === Expiration Check Started ===
[2025-07-03 16:40:59] Found 0 expired subscriptions
[2025-07-03 16:40:59] --- Checking for inconsistencies ---
[2025-07-03 16:40:59] === Expiration Check Completed ===
[2025-07-03 16:40:59] Processed: 0 expired subscriptions
[2025-07-03 16:40:59] Disabled: 0 Jellyfin accounts
[2025-07-03 16:40:59] Errors: 0
[2025-07-03 16:40:59] === End of Expiration Check ===
[2025-07-03 16:46:00] === Expiration Check Started ===
[2025-07-03 16:46:00] Found 0 expired subscriptions
[2025-07-03 16:46:00] --- Checking for inconsistencies ---
[2025-07-03 16:46:00] === Expiration Check Completed ===
[2025-07-03 16:46:00] Processed: 0 expired subscriptions
[2025-07-03 16:46:00] Disabled: 0 Jellyfin accounts
[2025-07-03 16:46:00] Errors: 0
[2025-07-03 16:46:00] === End of Expiration Check ===
[2025-07-03 16:51:00] === Expiration Check Started ===
[2025-07-03 16:51:00] Found 0 expired subscriptions
[2025-07-03 16:51:00] --- Checking for inconsistencies ---
[2025-07-03 16:51:00] === Expiration Check Completed ===
[2025-07-03 16:51:00] Processed: 0 expired subscriptions
[2025-07-03 16:51:00] Disabled: 0 Jellyfin accounts
[2025-07-03 16:51:00] Errors: 0
[2025-07-03 16:51:00] === End of Expiration Check ===
[2025-07-03 16:56:01] === Expiration Check Started ===
[2025-07-03 16:56:01] Found 0 expired subscriptions
[2025-07-03 16:56:01] --- Checking for inconsistencies ---
[2025-07-03 16:56:01] === Expiration Check Completed ===
[2025-07-03 16:56:01] Processed: 0 expired subscriptions
[2025-07-03 16:56:01] Disabled: 0 Jellyfin accounts
[2025-07-03 16:56:01] Errors: 0
[2025-07-03 16:56:01] === End of Expiration Check ===
[2025-07-03 17:01:02] === Expiration Check Started ===
[2025-07-03 17:01:02] Found 0 expired subscriptions
[2025-07-03 17:01:02] --- Checking for inconsistencies ---
[2025-07-03 17:01:02] === Expiration Check Completed ===
[2025-07-03 17:01:02] Processed: 0 expired subscriptions
[2025-07-03 17:01:02] Disabled: 0 Jellyfin accounts
[2025-07-03 17:01:02] Errors: 0
[2025-07-03 17:01:02] === End of Expiration Check ===
[2025-07-03 17:06:03] === Expiration Check Started ===
[2025-07-03 17:06:03] Found 0 expired subscriptions
[2025-07-03 17:06:03] --- Checking for inconsistencies ---
[2025-07-03 17:06:03] === Expiration Check Completed ===
[2025-07-03 17:06:03] Processed: 0 expired subscriptions
[2025-07-03 17:06:03] Disabled: 0 Jellyfin accounts
[2025-07-03 17:06:03] Errors: 0
[2025-07-03 17:06:03] === End of Expiration Check ===
[2025-07-03 17:11:04] === Expiration Check Started ===
[2025-07-03 17:11:04] Found 0 expired subscriptions
[2025-07-03 17:11:04] --- Checking for inconsistencies ---
[2025-07-03 17:11:04] === Expiration Check Completed ===
[2025-07-03 17:11:04] Processed: 0 expired subscriptions
[2025-07-03 17:11:04] Disabled: 0 Jellyfin accounts
[2025-07-03 17:11:04] Errors: 0
[2025-07-03 17:11:04] === End of Expiration Check ===
[2025-07-03 17:16:04] === Expiration Check Started ===
[2025-07-03 17:16:04] Found 0 expired subscriptions
[2025-07-03 17:16:04] --- Checking for inconsistencies ---
[2025-07-03 17:16:04] === Expiration Check Completed ===
[2025-07-03 17:16:04] Processed: 0 expired subscriptions
[2025-07-03 17:16:04] Disabled: 0 Jellyfin accounts
[2025-07-03 17:16:04] Errors: 0
[2025-07-03 17:16:04] === End of Expiration Check ===
[2025-07-03 17:21:05] === Expiration Check Started ===
[2025-07-03 17:21:05] Found 0 expired subscriptions
[2025-07-03 17:21:05] --- Checking for inconsistencies ---
[2025-07-03 17:21:05] === Expiration Check Completed ===
[2025-07-03 17:21:05] Processed: 0 expired subscriptions
[2025-07-03 17:21:05] Disabled: 0 Jellyfin accounts
[2025-07-03 17:21:05] Errors: 0
[2025-07-03 17:21:05] === End of Expiration Check ===
[2025-07-03 17:26:06] === Expiration Check Started ===
[2025-07-03 17:26:06] Found 0 expired subscriptions
[2025-07-03 17:26:06] --- Checking for inconsistencies ---
[2025-07-03 17:26:06] === Expiration Check Completed ===
[2025-07-03 17:26:06] Processed: 0 expired subscriptions
[2025-07-03 17:26:06] Disabled: 0 Jellyfin accounts
[2025-07-03 17:26:06] Errors: 0
[2025-07-03 17:26:06] === End of Expiration Check ===
[2025-07-03 17:31:07] === Expiration Check Started ===
[2025-07-03 17:31:07] Found 0 expired subscriptions
[2025-07-03 17:31:07] --- Checking for inconsistencies ---
[2025-07-03 17:31:07] === Expiration Check Completed ===
[2025-07-03 17:31:07] Processed: 0 expired subscriptions
[2025-07-03 17:31:07] Disabled: 0 Jellyfin accounts
[2025-07-03 17:31:07] Errors: 0
[2025-07-03 17:31:07] === End of Expiration Check ===
[2025-07-03 17:36:08] === Expiration Check Started ===
[2025-07-03 17:36:08] Found 0 expired subscriptions
[2025-07-03 17:36:08] --- Checking for inconsistencies ---
[2025-07-03 17:36:08] === Expiration Check Completed ===
[2025-07-03 17:36:08] Processed: 0 expired subscriptions
[2025-07-03 17:36:08] Disabled: 0 Jellyfin accounts
[2025-07-03 17:36:08] Errors: 0
[2025-07-03 17:36:08] === End of Expiration Check ===
[2025-07-03 17:41:09] === Expiration Check Started ===
[2025-07-03 17:41:09] Found 0 expired subscriptions
[2025-07-03 17:41:09] --- Checking for inconsistencies ---
[2025-07-03 17:41:09] === Expiration Check Completed ===
[2025-07-03 17:41:09] Processed: 0 expired subscriptions
[2025-07-03 17:41:09] Disabled: 0 Jellyfin accounts
[2025-07-03 17:41:09] Errors: 0
[2025-07-03 17:41:09] === End of Expiration Check ===
[2025-07-03 17:46:09] === Expiration Check Started ===
[2025-07-03 17:46:09] Found 0 expired subscriptions
[2025-07-03 17:46:09] --- Checking for inconsistencies ---
[2025-07-03 17:46:09] === Expiration Check Completed ===
[2025-07-03 17:46:09] Processed: 0 expired subscriptions
[2025-07-03 17:46:09] Disabled: 0 Jellyfin accounts
[2025-07-03 17:46:09] Errors: 0
[2025-07-03 17:46:09] === End of Expiration Check ===
[2025-07-03 17:51:10] === Expiration Check Started ===
[2025-07-03 17:51:10] Found 0 expired subscriptions
[2025-07-03 17:51:10] --- Checking for inconsistencies ---
[2025-07-03 17:51:10] === Expiration Check Completed ===
[2025-07-03 17:51:10] Processed: 0 expired subscriptions
[2025-07-03 17:51:10] Disabled: 0 Jellyfin accounts
[2025-07-03 17:51:10] Errors: 0
[2025-07-03 17:51:10] === End of Expiration Check ===
[2025-07-03 17:56:11] === Expiration Check Started ===
[2025-07-03 17:56:11] Found 0 expired subscriptions
[2025-07-03 17:56:11] --- Checking for inconsistencies ---
[2025-07-03 17:56:11] === Expiration Check Completed ===
[2025-07-03 17:56:11] Processed: 0 expired subscriptions
[2025-07-03 17:56:11] Disabled: 0 Jellyfin accounts
[2025-07-03 17:56:11] Errors: 0
[2025-07-03 17:56:11] === End of Expiration Check ===
[2025-07-03 18:01:12] === Expiration Check Started ===
[2025-07-03 18:01:12] Found 0 expired subscriptions
[2025-07-03 18:01:12] --- Checking for inconsistencies ---
[2025-07-03 18:01:12] === Expiration Check Completed ===
[2025-07-03 18:01:12] Processed: 0 expired subscriptions
[2025-07-03 18:01:12] Disabled: 0 Jellyfin accounts
[2025-07-03 18:01:12] Errors: 0
[2025-07-03 18:01:12] === End of Expiration Check ===
[2025-07-03 18:06:13] === Expiration Check Started ===
[2025-07-03 18:06:13] Found 0 expired subscriptions
[2025-07-03 18:06:13] --- Checking for inconsistencies ---
[2025-07-03 18:06:13] === Expiration Check Completed ===
[2025-07-03 18:06:13] Processed: 0 expired subscriptions
[2025-07-03 18:06:13] Disabled: 0 Jellyfin accounts
[2025-07-03 18:06:13] Errors: 0
[2025-07-03 18:06:13] === End of Expiration Check ===
[2025-07-03 18:11:14] === Expiration Check Started ===
[2025-07-03 18:11:14] Found 0 expired subscriptions
[2025-07-03 18:11:14] --- Checking for inconsistencies ---
[2025-07-03 18:11:14] === Expiration Check Completed ===
[2025-07-03 18:11:14] Processed: 0 expired subscriptions
[2025-07-03 18:11:14] Disabled: 0 Jellyfin accounts
[2025-07-03 18:11:14] Errors: 0
[2025-07-03 18:11:14] === End of Expiration Check ===
[2025-07-03 18:16:14] === Expiration Check Started ===
[2025-07-03 18:16:14] Found 0 expired subscriptions
[2025-07-03 18:16:14] --- Checking for inconsistencies ---
[2025-07-03 18:16:14] === Expiration Check Completed ===
[2025-07-03 18:16:14] Processed: 0 expired subscriptions
[2025-07-03 18:16:14] Disabled: 0 Jellyfin accounts
[2025-07-03 18:16:14] Errors: 0
[2025-07-03 18:16:14] === End of Expiration Check ===
[2025-07-03 18:21:15] === Expiration Check Started ===
[2025-07-03 18:21:15] Found 0 expired subscriptions
[2025-07-03 18:21:15] --- Checking for inconsistencies ---
[2025-07-03 18:21:15] === Expiration Check Completed ===
[2025-07-03 18:21:15] Processed: 0 expired subscriptions
[2025-07-03 18:21:15] Disabled: 0 Jellyfin accounts
[2025-07-03 18:21:15] Errors: 0
[2025-07-03 18:21:15] === End of Expiration Check ===
[2025-07-03 18:26:16] === Expiration Check Started ===
[2025-07-03 18:26:16] Found 0 expired subscriptions
[2025-07-03 18:26:16] --- Checking for inconsistencies ---
[2025-07-03 18:26:16] === Expiration Check Completed ===
[2025-07-03 18:26:16] Processed: 0 expired subscriptions
[2025-07-03 18:26:16] Disabled: 0 Jellyfin accounts
[2025-07-03 18:26:16] Errors: 0
[2025-07-03 18:26:16] === End of Expiration Check ===
[2025-07-03 18:31:17] === Expiration Check Started ===
[2025-07-03 18:31:17] Found 0 expired subscriptions
[2025-07-03 18:31:17] --- Checking for inconsistencies ---
[2025-07-03 18:31:17] === Expiration Check Completed ===
[2025-07-03 18:31:17] Processed: 0 expired subscriptions
[2025-07-03 18:31:17] Disabled: 0 Jellyfin accounts
[2025-07-03 18:31:17] Errors: 0
[2025-07-03 18:31:17] === End of Expiration Check ===
[2025-07-03 18:36:17] === Expiration Check Started ===
[2025-07-03 18:36:17] Found 0 expired subscriptions
[2025-07-03 18:36:17] --- Checking for inconsistencies ---
[2025-07-03 18:36:17] === Expiration Check Completed ===
[2025-07-03 18:36:17] Processed: 0 expired subscriptions
[2025-07-03 18:36:17] Disabled: 0 Jellyfin accounts
[2025-07-03 18:36:17] Errors: 0
[2025-07-03 18:36:17] === End of Expiration Check ===
[2025-07-03 18:41:19] === Expiration Check Started ===
[2025-07-03 18:41:19] Found 0 expired subscriptions
[2025-07-03 18:41:19] --- Checking for inconsistencies ---
[2025-07-03 18:41:19] === Expiration Check Completed ===
[2025-07-03 18:41:19] Processed: 0 expired subscriptions
[2025-07-03 18:41:19] Disabled: 0 Jellyfin accounts
[2025-07-03 18:41:19] Errors: 0
[2025-07-03 18:41:19] === End of Expiration Check ===
[2025-07-03 18:46:19] === Expiration Check Started ===
[2025-07-03 18:46:19] Found 0 expired subscriptions
[2025-07-03 18:46:19] --- Checking for inconsistencies ---
[2025-07-03 18:46:19] === Expiration Check Completed ===
[2025-07-03 18:46:19] Processed: 0 expired subscriptions
[2025-07-03 18:46:19] Disabled: 0 Jellyfin accounts
[2025-07-03 18:46:19] Errors: 0
[2025-07-03 18:46:19] === End of Expiration Check ===
[2025-07-03 18:51:20] === Expiration Check Started ===
[2025-07-03 18:51:20] Found 0 expired subscriptions
[2025-07-03 18:51:20] --- Checking for inconsistencies ---
[2025-07-03 18:51:20] === Expiration Check Completed ===
[2025-07-03 18:51:20] Processed: 0 expired subscriptions
[2025-07-03 18:51:20] Disabled: 0 Jellyfin accounts
[2025-07-03 18:51:20] Errors: 0
[2025-07-03 18:51:20] === End of Expiration Check ===
[2025-07-03 18:56:21] === Expiration Check Started ===
[2025-07-03 18:56:21] Found 0 expired subscriptions
[2025-07-03 18:56:21] --- Checking for inconsistencies ---
[2025-07-03 18:56:21] === Expiration Check Completed ===
[2025-07-03 18:56:21] Processed: 0 expired subscriptions
[2025-07-03 18:56:21] Disabled: 0 Jellyfin accounts
[2025-07-03 18:56:21] Errors: 0
[2025-07-03 18:56:21] === End of Expiration Check ===
[2025-07-03 19:01:22] === Expiration Check Started ===
[2025-07-03 19:01:22] Found 0 expired subscriptions
[2025-07-03 19:01:22] --- Checking for inconsistencies ---
[2025-07-03 19:01:22] === Expiration Check Completed ===
[2025-07-03 19:01:22] Processed: 0 expired subscriptions
[2025-07-03 19:01:22] Disabled: 0 Jellyfin accounts
[2025-07-03 19:01:22] Errors: 0
[2025-07-03 19:01:22] === End of Expiration Check ===
[2025-07-03 19:06:22] === Expiration Check Started ===
[2025-07-03 19:06:22] Found 0 expired subscriptions
[2025-07-03 19:06:22] --- Checking for inconsistencies ---
[2025-07-03 19:06:22] === Expiration Check Completed ===
[2025-07-03 19:06:22] Processed: 0 expired subscriptions
[2025-07-03 19:06:22] Disabled: 0 Jellyfin accounts
[2025-07-03 19:06:22] Errors: 0
[2025-07-03 19:06:22] === End of Expiration Check ===
[2025-07-03 19:11:24] === Expiration Check Started ===
[2025-07-03 19:11:24] Found 0 expired subscriptions
[2025-07-03 19:11:24] --- Checking for inconsistencies ---
[2025-07-03 19:11:24] === Expiration Check Completed ===
[2025-07-03 19:11:24] Processed: 0 expired subscriptions
[2025-07-03 19:11:24] Disabled: 0 Jellyfin accounts
[2025-07-03 19:11:24] Errors: 0
[2025-07-03 19:11:24] === End of Expiration Check ===
[2025-07-03 19:16:24] === Expiration Check Started ===
[2025-07-03 19:16:24] Found 0 expired subscriptions
[2025-07-03 19:16:24] --- Checking for inconsistencies ---
[2025-07-03 19:16:24] === Expiration Check Completed ===
[2025-07-03 19:16:24] Processed: 0 expired subscriptions
[2025-07-03 19:16:24] Disabled: 0 Jellyfin accounts
[2025-07-03 19:16:24] Errors: 0
[2025-07-03 19:16:24] === End of Expiration Check ===
[2025-07-03 19:21:25] === Expiration Check Started ===
[2025-07-03 19:21:25] Found 0 expired subscriptions
[2025-07-03 19:21:25] --- Checking for inconsistencies ---
[2025-07-03 19:21:25] === Expiration Check Completed ===
[2025-07-03 19:21:25] Processed: 0 expired subscriptions
[2025-07-03 19:21:25] Disabled: 0 Jellyfin accounts
[2025-07-03 19:21:25] Errors: 0
[2025-07-03 19:21:25] === End of Expiration Check ===
[2025-07-03 19:26:26] === Expiration Check Started ===
[2025-07-03 19:26:26] Found 0 expired subscriptions
[2025-07-03 19:26:26] --- Checking for inconsistencies ---
[2025-07-03 19:26:26] === Expiration Check Completed ===
[2025-07-03 19:26:26] Processed: 0 expired subscriptions
[2025-07-03 19:26:26] Disabled: 0 Jellyfin accounts
[2025-07-03 19:26:26] Errors: 0
[2025-07-03 19:26:26] === End of Expiration Check ===
[2025-07-03 19:31:27] === Expiration Check Started ===
[2025-07-03 19:31:27] Found 0 expired subscriptions
[2025-07-03 19:31:27] --- Checking for inconsistencies ---
[2025-07-03 19:31:27] === Expiration Check Completed ===
[2025-07-03 19:31:27] Processed: 0 expired subscriptions
[2025-07-03 19:31:27] Disabled: 0 Jellyfin accounts
[2025-07-03 19:31:27] Errors: 0
[2025-07-03 19:31:27] === End of Expiration Check ===
[2025-07-03 19:36:28] === Expiration Check Started ===
[2025-07-03 19:36:28] Found 0 expired subscriptions
[2025-07-03 19:36:28] --- Checking for inconsistencies ---
[2025-07-03 19:36:28] === Expiration Check Completed ===
[2025-07-03 19:36:28] Processed: 0 expired subscriptions
[2025-07-03 19:36:28] Disabled: 0 Jellyfin accounts
[2025-07-03 19:36:28] Errors: 0
[2025-07-03 19:36:28] === End of Expiration Check ===
[2025-07-03 19:41:29] === Expiration Check Started ===
[2025-07-03 19:41:29] Found 0 expired subscriptions
[2025-07-03 19:41:29] --- Checking for inconsistencies ---
[2025-07-03 19:41:29] === Expiration Check Completed ===
[2025-07-03 19:41:29] Processed: 0 expired subscriptions
[2025-07-03 19:41:29] Disabled: 0 Jellyfin accounts
[2025-07-03 19:41:29] Errors: 0
[2025-07-03 19:41:29] === End of Expiration Check ===
[2025-07-03 19:46:30] === Expiration Check Started ===
[2025-07-03 19:46:30] Found 0 expired subscriptions
[2025-07-03 19:46:30] --- Checking for inconsistencies ---
[2025-07-03 19:46:30] === Expiration Check Completed ===
[2025-07-03 19:46:30] Processed: 0 expired subscriptions
[2025-07-03 19:46:30] Disabled: 0 Jellyfin accounts
[2025-07-03 19:46:30] Errors: 0
[2025-07-03 19:46:30] === End of Expiration Check ===
[2025-07-03 19:51:30] === Expiration Check Started ===
[2025-07-03 19:51:30] Found 0 expired subscriptions
[2025-07-03 19:51:30] --- Checking for inconsistencies ---
[2025-07-03 19:51:30] === Expiration Check Completed ===
[2025-07-03 19:51:30] Processed: 0 expired subscriptions
[2025-07-03 19:51:30] Disabled: 0 Jellyfin accounts
[2025-07-03 19:51:30] Errors: 0
[2025-07-03 19:51:30] === End of Expiration Check ===
[2025-07-03 19:56:32] === Expiration Check Started ===
[2025-07-03 19:56:32] Found 0 expired subscriptions
[2025-07-03 19:56:32] --- Checking for inconsistencies ---
[2025-07-03 19:56:32] === Expiration Check Completed ===
[2025-07-03 19:56:32] Processed: 0 expired subscriptions
[2025-07-03 19:56:32] Disabled: 0 Jellyfin accounts
[2025-07-03 19:56:32] Errors: 0
[2025-07-03 19:56:32] === End of Expiration Check ===
[2025-07-03 20:01:32] === Expiration Check Started ===
[2025-07-03 20:01:32] Found 0 expired subscriptions
[2025-07-03 20:01:32] --- Checking for inconsistencies ---
[2025-07-03 20:01:32] === Expiration Check Completed ===
[2025-07-03 20:01:32] Processed: 0 expired subscriptions
[2025-07-03 20:01:32] Disabled: 0 Jellyfin accounts
[2025-07-03 20:01:32] Errors: 0
[2025-07-03 20:01:32] === End of Expiration Check ===
[2025-07-03 20:06:34] === Expiration Check Started ===
[2025-07-03 20:06:34] Found 0 expired subscriptions
[2025-07-03 20:06:34] --- Checking for inconsistencies ---
[2025-07-03 20:06:34] === Expiration Check Completed ===
[2025-07-03 20:06:34] Processed: 0 expired subscriptions
[2025-07-03 20:06:34] Disabled: 0 Jellyfin accounts
[2025-07-03 20:06:34] Errors: 0
[2025-07-03 20:06:34] === End of Expiration Check ===
[2025-07-03 20:11:34] === Expiration Check Started ===
[2025-07-03 20:11:34] Found 0 expired subscriptions
[2025-07-03 20:11:34] --- Checking for inconsistencies ---
[2025-07-03 20:11:34] === Expiration Check Completed ===
[2025-07-03 20:11:34] Processed: 0 expired subscriptions
[2025-07-03 20:11:34] Disabled: 0 Jellyfin accounts
[2025-07-03 20:11:34] Errors: 0
[2025-07-03 20:11:34] === End of Expiration Check ===
[2025-07-03 20:16:35] === Expiration Check Started ===
[2025-07-03 20:16:35] Found 0 expired subscriptions
[2025-07-03 20:16:35] --- Checking for inconsistencies ---
[2025-07-03 20:16:35] === Expiration Check Completed ===
[2025-07-03 20:16:35] Processed: 0 expired subscriptions
[2025-07-03 20:16:35] Disabled: 0 Jellyfin accounts
[2025-07-03 20:16:35] Errors: 0
[2025-07-03 20:16:35] === End of Expiration Check ===
[2025-07-03 20:21:36] === Expiration Check Started ===
[2025-07-03 20:21:36] Found 0 expired subscriptions
[2025-07-03 20:21:36] --- Checking for inconsistencies ---
[2025-07-03 20:21:36] === Expiration Check Completed ===
[2025-07-03 20:21:36] Processed: 0 expired subscriptions
[2025-07-03 20:21:36] Disabled: 0 Jellyfin accounts
[2025-07-03 20:21:36] Errors: 0
[2025-07-03 20:21:36] === End of Expiration Check ===
[2025-07-03 20:26:37] === Expiration Check Started ===
[2025-07-03 20:26:37] Found 0 expired subscriptions
[2025-07-03 20:26:37] --- Checking for inconsistencies ---
[2025-07-03 20:26:37] === Expiration Check Completed ===
[2025-07-03 20:26:37] Processed: 0 expired subscriptions
[2025-07-03 20:26:37] Disabled: 0 Jellyfin accounts
[2025-07-03 20:26:37] Errors: 0
[2025-07-03 20:26:37] === End of Expiration Check ===
[2025-07-03 20:31:37] === Expiration Check Started ===
[2025-07-03 20:31:37] Found 0 expired subscriptions
[2025-07-03 20:31:37] --- Checking for inconsistencies ---
[2025-07-03 20:31:37] === Expiration Check Completed ===
[2025-07-03 20:31:37] Processed: 0 expired subscriptions
[2025-07-03 20:31:37] Disabled: 0 Jellyfin accounts
[2025-07-03 20:31:37] Errors: 0
[2025-07-03 20:31:37] === End of Expiration Check ===
[2025-07-03 20:36:38] === Expiration Check Started ===
[2025-07-03 20:36:38] Found 0 expired subscriptions
[2025-07-03 20:36:38] --- Checking for inconsistencies ---
[2025-07-03 20:36:38] === Expiration Check Completed ===
[2025-07-03 20:36:38] Processed: 0 expired subscriptions
[2025-07-03 20:36:38] Disabled: 0 Jellyfin accounts
[2025-07-03 20:36:38] Errors: 0
[2025-07-03 20:36:38] === End of Expiration Check ===
[2025-07-03 20:41:39] === Expiration Check Started ===
[2025-07-03 20:41:39] Found 0 expired subscriptions
[2025-07-03 20:41:39] --- Checking for inconsistencies ---
[2025-07-03 20:41:39] === Expiration Check Completed ===
[2025-07-03 20:41:39] Processed: 0 expired subscriptions
[2025-07-03 20:41:39] Disabled: 0 Jellyfin accounts
[2025-07-03 20:41:39] Errors: 0
[2025-07-03 20:41:39] === End of Expiration Check ===
[2025-07-03 20:46:40] === Expiration Check Started ===
[2025-07-03 20:46:40] Found 0 expired subscriptions
[2025-07-03 20:46:40] --- Checking for inconsistencies ---
[2025-07-03 20:46:40] === Expiration Check Completed ===
[2025-07-03 20:46:40] Processed: 0 expired subscriptions
[2025-07-03 20:46:40] Disabled: 0 Jellyfin accounts
[2025-07-03 20:46:40] Errors: 0
[2025-07-03 20:46:40] === End of Expiration Check ===
[2025-07-03 20:51:40] === Expiration Check Started ===
[2025-07-03 20:51:40] Found 0 expired subscriptions
[2025-07-03 20:51:40] --- Checking for inconsistencies ---
[2025-07-03 20:51:40] === Expiration Check Completed ===
[2025-07-03 20:51:40] Processed: 0 expired subscriptions
[2025-07-03 20:51:40] Disabled: 0 Jellyfin accounts
[2025-07-03 20:51:40] Errors: 0
[2025-07-03 20:51:40] === End of Expiration Check ===
[2025-07-03 20:56:42] === Expiration Check Started ===
[2025-07-03 20:56:42] Found 0 expired subscriptions
[2025-07-03 20:56:42] --- Checking for inconsistencies ---
[2025-07-03 20:56:42] === Expiration Check Completed ===
[2025-07-03 20:56:42] Processed: 0 expired subscriptions
[2025-07-03 20:56:42] Disabled: 0 Jellyfin accounts
[2025-07-03 20:56:42] Errors: 0
[2025-07-03 20:56:42] === End of Expiration Check ===
[2025-07-03 21:01:42] === Expiration Check Started ===
[2025-07-03 21:01:42] Found 0 expired subscriptions
[2025-07-03 21:01:42] --- Checking for inconsistencies ---
[2025-07-03 21:01:42] === Expiration Check Completed ===
[2025-07-03 21:01:42] Processed: 0 expired subscriptions
[2025-07-03 21:01:42] Disabled: 0 Jellyfin accounts
[2025-07-03 21:01:42] Errors: 0
[2025-07-03 21:01:42] === End of Expiration Check ===
[2025-07-03 21:06:43] === Expiration Check Started ===
[2025-07-03 21:06:43] Found 0 expired subscriptions
[2025-07-03 21:06:43] --- Checking for inconsistencies ---
[2025-07-03 21:06:43] === Expiration Check Completed ===
[2025-07-03 21:06:43] Processed: 0 expired subscriptions
[2025-07-03 21:06:43] Disabled: 0 Jellyfin accounts
[2025-07-03 21:06:43] Errors: 0
[2025-07-03 21:06:43] === End of Expiration Check ===
[2025-07-03 21:11:44] === Expiration Check Started ===
[2025-07-03 21:11:44] Found 0 expired subscriptions
[2025-07-03 21:11:44] --- Checking for inconsistencies ---
[2025-07-03 21:11:44] === Expiration Check Completed ===
[2025-07-03 21:11:44] Processed: 0 expired subscriptions
[2025-07-03 21:11:44] Disabled: 0 Jellyfin accounts
[2025-07-03 21:11:44] Errors: 0
[2025-07-03 21:11:44] === End of Expiration Check ===
[2025-07-03 21:16:45] === Expiration Check Started ===
[2025-07-03 21:16:45] Found 0 expired subscriptions
[2025-07-03 21:16:45] --- Checking for inconsistencies ---
[2025-07-03 21:16:45] === Expiration Check Completed ===
[2025-07-03 21:16:45] Processed: 0 expired subscriptions
[2025-07-03 21:16:45] Disabled: 0 Jellyfin accounts
[2025-07-03 21:16:45] Errors: 0
[2025-07-03 21:16:45] === End of Expiration Check ===
[2025-07-03 21:21:46] === Expiration Check Started ===
[2025-07-03 21:21:46] Found 0 expired subscriptions
[2025-07-03 21:21:46] --- Checking for inconsistencies ---
[2025-07-03 21:21:46] === Expiration Check Completed ===
[2025-07-03 21:21:46] Processed: 0 expired subscriptions
[2025-07-03 21:21:46] Disabled: 0 Jellyfin accounts
[2025-07-03 21:21:46] Errors: 0
[2025-07-03 21:21:46] === End of Expiration Check ===
[2025-07-03 21:26:47] === Expiration Check Started ===
[2025-07-03 21:26:47] Found 0 expired subscriptions
[2025-07-03 21:26:47] --- Checking for inconsistencies ---
[2025-07-03 21:26:47] === Expiration Check Completed ===
[2025-07-03 21:26:47] Processed: 0 expired subscriptions
[2025-07-03 21:26:47] Disabled: 0 Jellyfin accounts
[2025-07-03 21:26:47] Errors: 0
[2025-07-03 21:26:47] === End of Expiration Check ===
[2025-07-03 21:31:48] === Expiration Check Started ===
[2025-07-03 21:31:48] Found 0 expired subscriptions
[2025-07-03 21:31:48] --- Checking for inconsistencies ---
[2025-07-03 21:31:48] === Expiration Check Completed ===
[2025-07-03 21:31:48] Processed: 0 expired subscriptions
[2025-07-03 21:31:48] Disabled: 0 Jellyfin accounts
[2025-07-03 21:31:48] Errors: 0
[2025-07-03 21:31:48] === End of Expiration Check ===
[2025-07-03 21:36:48] === Expiration Check Started ===
[2025-07-03 21:36:48] Found 0 expired subscriptions
[2025-07-03 21:36:48] --- Checking for inconsistencies ---
[2025-07-03 21:36:48] === Expiration Check Completed ===
[2025-07-03 21:36:48] Processed: 0 expired subscriptions
[2025-07-03 21:36:48] Disabled: 0 Jellyfin accounts
[2025-07-03 21:36:48] Errors: 0
[2025-07-03 21:36:48] === End of Expiration Check ===
[2025-07-03 21:41:49] === Expiration Check Started ===
[2025-07-03 21:41:49] Found 0 expired subscriptions
[2025-07-03 21:41:49] --- Checking for inconsistencies ---
[2025-07-03 21:41:49] === Expiration Check Completed ===
[2025-07-03 21:41:49] Processed: 0 expired subscriptions
[2025-07-03 21:41:49] Disabled: 0 Jellyfin accounts
[2025-07-03 21:41:49] Errors: 0
[2025-07-03 21:41:49] === End of Expiration Check ===
[2025-07-03 21:46:50] === Expiration Check Started ===
[2025-07-03 21:46:50] Found 0 expired subscriptions
[2025-07-03 21:46:50] --- Checking for inconsistencies ---
[2025-07-03 21:46:50] === Expiration Check Completed ===
[2025-07-03 21:46:50] Processed: 0 expired subscriptions
[2025-07-03 21:46:50] Disabled: 0 Jellyfin accounts
[2025-07-03 21:46:50] Errors: 0
[2025-07-03 21:46:50] === End of Expiration Check ===
[2025-07-03 21:51:51] === Expiration Check Started ===
[2025-07-03 21:51:51] Found 0 expired subscriptions
[2025-07-03 21:51:51] --- Checking for inconsistencies ---
[2025-07-03 21:51:51] === Expiration Check Completed ===
[2025-07-03 21:51:51] Processed: 0 expired subscriptions
[2025-07-03 21:51:51] Disabled: 0 Jellyfin accounts
[2025-07-03 21:51:51] Errors: 0
[2025-07-03 21:51:51] === End of Expiration Check ===
[2025-07-03 21:56:52] === Expiration Check Started ===
[2025-07-03 21:56:52] Found 0 expired subscriptions
[2025-07-03 21:56:52] --- Checking for inconsistencies ---
[2025-07-03 21:56:52] === Expiration Check Completed ===
[2025-07-03 21:56:52] Processed: 0 expired subscriptions
[2025-07-03 21:56:52] Disabled: 0 Jellyfin accounts
[2025-07-03 21:56:52] Errors: 0
[2025-07-03 21:56:52] === End of Expiration Check ===
[2025-07-03 22:01:53] === Expiration Check Started ===
[2025-07-03 22:01:53] Found 0 expired subscriptions
[2025-07-03 22:01:53] --- Checking for inconsistencies ---
[2025-07-03 22:01:53] === Expiration Check Completed ===
[2025-07-03 22:01:53] Processed: 0 expired subscriptions
[2025-07-03 22:01:53] Disabled: 0 Jellyfin accounts
[2025-07-03 22:01:53] Errors: 0
[2025-07-03 22:01:53] === End of Expiration Check ===
[2025-07-03 22:06:54] === Expiration Check Started ===
[2025-07-03 22:06:54] Found 0 expired subscriptions
[2025-07-03 22:06:54] --- Checking for inconsistencies ---
[2025-07-03 22:06:54] === Expiration Check Completed ===
[2025-07-03 22:06:54] Processed: 0 expired subscriptions
[2025-07-03 22:06:54] Disabled: 0 Jellyfin accounts
[2025-07-03 22:06:54] Errors: 0
[2025-07-03 22:06:54] === End of Expiration Check ===
[2025-07-03 22:11:55] === Expiration Check Started ===
[2025-07-03 22:11:55] Found 0 expired subscriptions
[2025-07-03 22:11:55] --- Checking for inconsistencies ---
[2025-07-03 22:11:55] === Expiration Check Completed ===
[2025-07-03 22:11:55] Processed: 0 expired subscriptions
[2025-07-03 22:11:55] Disabled: 0 Jellyfin accounts
[2025-07-03 22:11:55] Errors: 0
[2025-07-03 22:11:55] === End of Expiration Check ===
[2025-07-03 22:16:56] === Expiration Check Started ===
[2025-07-03 22:16:56] Found 0 expired subscriptions
[2025-07-03 22:16:56] --- Checking for inconsistencies ---
[2025-07-03 22:16:56] === Expiration Check Completed ===
[2025-07-03 22:16:56] Processed: 0 expired subscriptions
[2025-07-03 22:16:56] Disabled: 0 Jellyfin accounts
[2025-07-03 22:16:56] Errors: 0
[2025-07-03 22:16:56] === End of Expiration Check ===
[2025-07-03 22:21:57] === Expiration Check Started ===
[2025-07-03 22:21:57] Found 0 expired subscriptions
[2025-07-03 22:21:57] --- Checking for inconsistencies ---
[2025-07-03 22:21:57] === Expiration Check Completed ===
[2025-07-03 22:21:57] Processed: 0 expired subscriptions
[2025-07-03 22:21:57] Disabled: 0 Jellyfin accounts
[2025-07-03 22:21:57] Errors: 0
[2025-07-03 22:21:57] === End of Expiration Check ===
[2025-07-03 22:26:57] === Expiration Check Started ===
[2025-07-03 22:26:57] Found 0 expired subscriptions
[2025-07-03 22:26:57] --- Checking for inconsistencies ---
[2025-07-03 22:26:57] === Expiration Check Completed ===
[2025-07-03 22:26:57] Processed: 0 expired subscriptions
[2025-07-03 22:26:57] Disabled: 0 Jellyfin accounts
[2025-07-03 22:26:57] Errors: 0
[2025-07-03 22:26:57] === End of Expiration Check ===
[2025-07-03 22:31:59] === Expiration Check Started ===
[2025-07-03 22:31:59] Found 0 expired subscriptions
[2025-07-03 22:31:59] --- Checking for inconsistencies ---
[2025-07-03 22:31:59] === Expiration Check Completed ===
[2025-07-03 22:31:59] Processed: 0 expired subscriptions
[2025-07-03 22:31:59] Disabled: 0 Jellyfin accounts
[2025-07-03 22:31:59] Errors: 0
[2025-07-03 22:31:59] === End of Expiration Check ===
[2025-07-03 22:36:59] === Expiration Check Started ===
[2025-07-03 22:36:59] Found 0 expired subscriptions
[2025-07-03 22:36:59] --- Checking for inconsistencies ---
[2025-07-03 22:36:59] === Expiration Check Completed ===
[2025-07-03 22:36:59] Processed: 0 expired subscriptions
[2025-07-03 22:36:59] Disabled: 0 Jellyfin accounts
[2025-07-03 22:36:59] Errors: 0
[2025-07-03 22:36:59] === End of Expiration Check ===
[2025-07-03 22:42:00] === Expiration Check Started ===
[2025-07-03 22:42:00] Found 0 expired subscriptions
[2025-07-03 22:42:00] --- Checking for inconsistencies ---
[2025-07-03 22:42:00] === Expiration Check Completed ===
[2025-07-03 22:42:00] Processed: 0 expired subscriptions
[2025-07-03 22:42:00] Disabled: 0 Jellyfin accounts
[2025-07-03 22:42:00] Errors: 0
[2025-07-03 22:42:00] === End of Expiration Check ===
[2025-07-03 22:47:00] === Expiration Check Started ===
[2025-07-03 22:47:00] Found 0 expired subscriptions
[2025-07-03 22:47:00] --- Checking for inconsistencies ---
[2025-07-03 22:47:00] === Expiration Check Completed ===
[2025-07-03 22:47:00] Processed: 0 expired subscriptions
[2025-07-03 22:47:00] Disabled: 0 Jellyfin accounts
[2025-07-03 22:47:00] Errors: 0
[2025-07-03 22:47:00] === End of Expiration Check ===
[2025-07-03 22:52:01] === Expiration Check Started ===
[2025-07-03 22:52:01] Found 0 expired subscriptions
[2025-07-03 22:52:01] --- Checking for inconsistencies ---
[2025-07-03 22:52:01] === Expiration Check Completed ===
[2025-07-03 22:52:01] Processed: 0 expired subscriptions
[2025-07-03 22:52:01] Disabled: 0 Jellyfin accounts
[2025-07-03 22:52:01] Errors: 0
[2025-07-03 22:52:01] === End of Expiration Check ===
[2025-07-03 22:57:02] === Expiration Check Started ===
[2025-07-03 22:57:02] Found 0 expired subscriptions
[2025-07-03 22:57:02] --- Checking for inconsistencies ---
[2025-07-03 22:57:02] === Expiration Check Completed ===
[2025-07-03 22:57:02] Processed: 0 expired subscriptions
[2025-07-03 22:57:02] Disabled: 0 Jellyfin accounts
[2025-07-03 22:57:02] Errors: 0
[2025-07-03 22:57:02] === End of Expiration Check ===
[2025-07-03 23:02:03] === Expiration Check Started ===
[2025-07-03 23:02:03] Found 0 expired subscriptions
[2025-07-03 23:02:03] --- Checking for inconsistencies ---
[2025-07-03 23:02:03] === Expiration Check Completed ===
[2025-07-03 23:02:03] Processed: 0 expired subscriptions
[2025-07-03 23:02:03] Disabled: 0 Jellyfin accounts
[2025-07-03 23:02:03] Errors: 0
[2025-07-03 23:02:03] === End of Expiration Check ===
[2025-07-03 23:07:04] === Expiration Check Started ===
[2025-07-03 23:07:04] Found 0 expired subscriptions
[2025-07-03 23:07:04] --- Checking for inconsistencies ---
[2025-07-03 23:07:04] === Expiration Check Completed ===
[2025-07-03 23:07:04] Processed: 0 expired subscriptions
[2025-07-03 23:07:04] Disabled: 0 Jellyfin accounts
[2025-07-03 23:07:04] Errors: 0
[2025-07-03 23:07:04] === End of Expiration Check ===
[2025-07-03 23:12:05] === Expiration Check Started ===
[2025-07-03 23:12:05] Found 0 expired subscriptions
[2025-07-03 23:12:05] --- Checking for inconsistencies ---
[2025-07-03 23:12:05] === Expiration Check Completed ===
[2025-07-03 23:12:05] Processed: 0 expired subscriptions
[2025-07-03 23:12:05] Disabled: 0 Jellyfin accounts
[2025-07-03 23:12:05] Errors: 0
[2025-07-03 23:12:05] === End of Expiration Check ===
[2025-07-03 23:17:06] === Expiration Check Started ===
[2025-07-03 23:17:06] Found 0 expired subscriptions
[2025-07-03 23:17:06] --- Checking for inconsistencies ---
[2025-07-03 23:17:06] === Expiration Check Completed ===
[2025-07-03 23:17:06] Processed: 0 expired subscriptions
[2025-07-03 23:17:06] Disabled: 0 Jellyfin accounts
[2025-07-03 23:17:06] Errors: 0
[2025-07-03 23:17:06] === End of Expiration Check ===
[2025-07-03 23:22:06] === Expiration Check Started ===
[2025-07-03 23:22:06] Found 0 expired subscriptions
[2025-07-03 23:22:06] --- Checking for inconsistencies ---
[2025-07-03 23:22:06] === Expiration Check Completed ===
[2025-07-03 23:22:06] Processed: 0 expired subscriptions
[2025-07-03 23:22:06] Disabled: 0 Jellyfin accounts
[2025-07-03 23:22:06] Errors: 0
[2025-07-03 23:22:06] === End of Expiration Check ===
[2025-07-03 23:27:08] === Expiration Check Started ===
[2025-07-03 23:27:08] Found 0 expired subscriptions
[2025-07-03 23:27:08] --- Checking for inconsistencies ---
[2025-07-03 23:27:08] === Expiration Check Completed ===
[2025-07-03 23:27:08] Processed: 0 expired subscriptions
[2025-07-03 23:27:08] Disabled: 0 Jellyfin accounts
[2025-07-03 23:27:08] Errors: 0
[2025-07-03 23:27:08] === End of Expiration Check ===
[2025-07-03 23:32:08] === Expiration Check Started ===
[2025-07-03 23:32:08] Found 0 expired subscriptions
[2025-07-03 23:32:08] --- Checking for inconsistencies ---
[2025-07-03 23:32:08] === Expiration Check Completed ===
[2025-07-03 23:32:08] Processed: 0 expired subscriptions
[2025-07-03 23:32:08] Disabled: 0 Jellyfin accounts
[2025-07-03 23:32:08] Errors: 0
[2025-07-03 23:32:08] === End of Expiration Check ===
[2025-07-03 23:37:09] === Expiration Check Started ===
[2025-07-03 23:37:09] Found 0 expired subscriptions
[2025-07-03 23:37:09] --- Checking for inconsistencies ---
[2025-07-03 23:37:09] === Expiration Check Completed ===
[2025-07-03 23:37:09] Processed: 0 expired subscriptions
[2025-07-03 23:37:09] Disabled: 0 Jellyfin accounts
[2025-07-03 23:37:09] Errors: 0
[2025-07-03 23:37:09] === End of Expiration Check ===
[2025-07-03 23:42:10] === Expiration Check Started ===
[2025-07-03 23:42:10] Found 0 expired subscriptions
[2025-07-03 23:42:10] --- Checking for inconsistencies ---
[2025-07-03 23:42:10] === Expiration Check Completed ===
[2025-07-03 23:42:10] Processed: 0 expired subscriptions
[2025-07-03 23:42:10] Disabled: 0 Jellyfin accounts
[2025-07-03 23:42:10] Errors: 0
[2025-07-03 23:42:10] === End of Expiration Check ===
[2025-07-03 23:47:12] === Expiration Check Started ===
[2025-07-03 23:47:12] Found 0 expired subscriptions
[2025-07-03 23:47:12] --- Checking for inconsistencies ---
[2025-07-03 23:47:12] === Expiration Check Completed ===
[2025-07-03 23:47:12] Processed: 0 expired subscriptions
[2025-07-03 23:47:12] Disabled: 0 Jellyfin accounts
[2025-07-03 23:47:12] Errors: 0
[2025-07-03 23:47:12] === End of Expiration Check ===
[2025-07-03 23:52:12] === Expiration Check Started ===
[2025-07-03 23:52:12] Found 0 expired subscriptions
[2025-07-03 23:52:12] --- Checking for inconsistencies ---
[2025-07-03 23:52:12] === Expiration Check Completed ===
[2025-07-03 23:52:12] Processed: 0 expired subscriptions
[2025-07-03 23:52:12] Disabled: 0 Jellyfin accounts
[2025-07-03 23:52:12] Errors: 0
[2025-07-03 23:52:12] === End of Expiration Check ===
[2025-07-03 23:57:13] === Expiration Check Started ===
[2025-07-03 23:57:13] Found 0 expired subscriptions
[2025-07-03 23:57:13] --- Checking for inconsistencies ---
[2025-07-03 23:57:13] === Expiration Check Completed ===
[2025-07-03 23:57:13] Processed: 0 expired subscriptions
[2025-07-03 23:57:13] Disabled: 0 Jellyfin accounts
[2025-07-03 23:57:13] Errors: 0
[2025-07-03 23:57:13] === End of Expiration Check ===
[2025-07-04 00:02:14] === Expiration Check Started ===
[2025-07-04 00:02:14] Found 0 expired subscriptions
[2025-07-04 00:02:14] --- Checking for inconsistencies ---
[2025-07-04 00:02:14] === Expiration Check Completed ===
[2025-07-04 00:02:14] Processed: 0 expired subscriptions
[2025-07-04 00:02:14] Disabled: 0 Jellyfin accounts
[2025-07-04 00:02:14] Errors: 0
[2025-07-04 00:02:14] === End of Expiration Check ===
[2025-07-04 00:07:15] === Expiration Check Started ===
[2025-07-04 00:07:15] Found 0 expired subscriptions
[2025-07-04 00:07:15] --- Checking for inconsistencies ---
[2025-07-04 00:07:15] === Expiration Check Completed ===
[2025-07-04 00:07:15] Processed: 0 expired subscriptions
[2025-07-04 00:07:15] Disabled: 0 Jellyfin accounts
[2025-07-04 00:07:15] Errors: 0
[2025-07-04 00:07:15] === End of Expiration Check ===
[2025-07-04 00:12:16] === Expiration Check Started ===
[2025-07-04 00:12:16] Found 0 expired subscriptions
[2025-07-04 00:12:16] --- Checking for inconsistencies ---
[2025-07-04 00:12:16] === Expiration Check Completed ===
[2025-07-04 00:12:16] Processed: 0 expired subscriptions
[2025-07-04 00:12:16] Disabled: 0 Jellyfin accounts
[2025-07-04 00:12:16] Errors: 0
[2025-07-04 00:12:16] === End of Expiration Check ===
[2025-07-04 00:17:17] === Expiration Check Started ===
[2025-07-04 00:17:17] Found 0 expired subscriptions
[2025-07-04 00:17:17] --- Checking for inconsistencies ---
[2025-07-04 00:17:17] === Expiration Check Completed ===
[2025-07-04 00:17:17] Processed: 0 expired subscriptions
[2025-07-04 00:17:17] Disabled: 0 Jellyfin accounts
[2025-07-04 00:17:17] Errors: 0
[2025-07-04 00:17:17] === End of Expiration Check ===
[2025-07-04 00:22:18] === Expiration Check Started ===
[2025-07-04 00:22:18] Found 0 expired subscriptions
[2025-07-04 00:22:18] --- Checking for inconsistencies ---
[2025-07-04 00:22:18] === Expiration Check Completed ===
[2025-07-04 00:22:18] Processed: 0 expired subscriptions
[2025-07-04 00:22:18] Disabled: 0 Jellyfin accounts
[2025-07-04 00:22:18] Errors: 0
[2025-07-04 00:22:18] === End of Expiration Check ===
[2025-07-04 00:27:18] === Expiration Check Started ===
[2025-07-04 00:27:18] Found 0 expired subscriptions
[2025-07-04 00:27:18] --- Checking for inconsistencies ---
[2025-07-04 00:27:18] === Expiration Check Completed ===
[2025-07-04 00:27:18] Processed: 0 expired subscriptions
[2025-07-04 00:27:18] Disabled: 0 Jellyfin accounts
[2025-07-04 00:27:18] Errors: 0
[2025-07-04 00:27:18] === End of Expiration Check ===
[2025-07-04 00:32:19] === Expiration Check Started ===
[2025-07-04 00:32:19] Found 0 expired subscriptions
[2025-07-04 00:32:19] --- Checking for inconsistencies ---
[2025-07-04 00:32:19] === Expiration Check Completed ===
[2025-07-04 00:32:19] Processed: 0 expired subscriptions
[2025-07-04 00:32:19] Disabled: 0 Jellyfin accounts
[2025-07-04 00:32:19] Errors: 0
[2025-07-04 00:32:19] === End of Expiration Check ===
[2025-07-04 00:37:20] === Expiration Check Started ===
[2025-07-04 00:37:20] Found 0 expired subscriptions
[2025-07-04 00:37:20] --- Checking for inconsistencies ---
[2025-07-04 00:37:20] === Expiration Check Completed ===
[2025-07-04 00:37:20] Processed: 0 expired subscriptions
[2025-07-04 00:37:20] Disabled: 0 Jellyfin accounts
[2025-07-04 00:37:20] Errors: 0
[2025-07-04 00:37:20] === End of Expiration Check ===
[2025-07-04 00:42:21] === Expiration Check Started ===
[2025-07-04 00:42:21] Found 0 expired subscriptions
[2025-07-04 00:42:21] --- Checking for inconsistencies ---
[2025-07-04 00:42:21] === Expiration Check Completed ===
[2025-07-04 00:42:21] Processed: 0 expired subscriptions
[2025-07-04 00:42:21] Disabled: 0 Jellyfin accounts
[2025-07-04 00:42:21] Errors: 0
[2025-07-04 00:42:21] === End of Expiration Check ===
[2025-07-04 00:47:22] === Expiration Check Started ===
[2025-07-04 00:47:22] Found 0 expired subscriptions
[2025-07-04 00:47:22] --- Checking for inconsistencies ---
[2025-07-04 00:47:22] === Expiration Check Completed ===
[2025-07-04 00:47:22] Processed: 0 expired subscriptions
[2025-07-04 00:47:22] Disabled: 0 Jellyfin accounts
[2025-07-04 00:47:22] Errors: 0
[2025-07-04 00:47:22] === End of Expiration Check ===
[2025-07-04 00:52:22] === Expiration Check Started ===
[2025-07-04 00:52:22] Found 0 expired subscriptions
[2025-07-04 00:52:22] --- Checking for inconsistencies ---
[2025-07-04 00:52:22] === Expiration Check Completed ===
[2025-07-04 00:52:22] Processed: 0 expired subscriptions
[2025-07-04 00:52:22] Disabled: 0 Jellyfin accounts
[2025-07-04 00:52:22] Errors: 0
[2025-07-04 00:52:22] === End of Expiration Check ===
[2025-07-04 00:57:23] === Expiration Check Started ===
[2025-07-04 00:57:23] Found 0 expired subscriptions
[2025-07-04 00:57:23] --- Checking for inconsistencies ---
[2025-07-04 00:57:23] === Expiration Check Completed ===
[2025-07-04 00:57:23] Processed: 0 expired subscriptions
[2025-07-04 00:57:23] Disabled: 0 Jellyfin accounts
[2025-07-04 00:57:23] Errors: 0
[2025-07-04 00:57:23] === End of Expiration Check ===
[2025-07-04 01:02:24] === Expiration Check Started ===
[2025-07-04 01:02:24] Found 0 expired subscriptions
[2025-07-04 01:02:24] --- Checking for inconsistencies ---
[2025-07-04 01:02:24] === Expiration Check Completed ===
[2025-07-04 01:02:24] Processed: 0 expired subscriptions
[2025-07-04 01:02:24] Disabled: 0 Jellyfin accounts
[2025-07-04 01:02:24] Errors: 0
[2025-07-04 01:02:24] === End of Expiration Check ===
[2025-07-04 01:07:25] === Expiration Check Started ===
[2025-07-04 01:07:25] Found 0 expired subscriptions
[2025-07-04 01:07:25] --- Checking for inconsistencies ---
[2025-07-04 01:07:25] === Expiration Check Completed ===
[2025-07-04 01:07:25] Processed: 0 expired subscriptions
[2025-07-04 01:07:25] Disabled: 0 Jellyfin accounts
[2025-07-04 01:07:25] Errors: 0
[2025-07-04 01:07:25] === End of Expiration Check ===
[2025-07-04 01:12:26] === Expiration Check Started ===
[2025-07-04 01:12:26] Found 0 expired subscriptions
[2025-07-04 01:12:26] --- Checking for inconsistencies ---
[2025-07-04 01:12:26] === Expiration Check Completed ===
[2025-07-04 01:12:26] Processed: 0 expired subscriptions
[2025-07-04 01:12:26] Disabled: 0 Jellyfin accounts
[2025-07-04 01:12:26] Errors: 0
[2025-07-04 01:12:26] === End of Expiration Check ===
[2025-07-04 01:17:27] === Expiration Check Started ===
[2025-07-04 01:17:27] Found 0 expired subscriptions
[2025-07-04 01:17:27] --- Checking for inconsistencies ---
[2025-07-04 01:17:27] === Expiration Check Completed ===
[2025-07-04 01:17:27] Processed: 0 expired subscriptions
[2025-07-04 01:17:27] Disabled: 0 Jellyfin accounts
[2025-07-04 01:17:27] Errors: 0
[2025-07-04 01:17:27] === End of Expiration Check ===
[2025-07-04 01:22:27] === Expiration Check Started ===
[2025-07-04 01:22:27] Found 0 expired subscriptions
[2025-07-04 01:22:27] --- Checking for inconsistencies ---
[2025-07-04 01:22:27] === Expiration Check Completed ===
[2025-07-04 01:22:27] Processed: 0 expired subscriptions
[2025-07-04 01:22:27] Disabled: 0 Jellyfin accounts
[2025-07-04 01:22:27] Errors: 0
[2025-07-04 01:22:27] === End of Expiration Check ===
[2025-07-04 01:27:28] === Expiration Check Started ===
[2025-07-04 01:27:28] Found 0 expired subscriptions
[2025-07-04 01:27:28] --- Checking for inconsistencies ---
[2025-07-04 01:27:28] === Expiration Check Completed ===
[2025-07-04 01:27:28] Processed: 0 expired subscriptions
[2025-07-04 01:27:28] Disabled: 0 Jellyfin accounts
[2025-07-04 01:27:28] Errors: 0
[2025-07-04 01:27:28] === End of Expiration Check ===
[2025-07-04 01:32:29] === Expiration Check Started ===
[2025-07-04 01:32:29] Found 0 expired subscriptions
[2025-07-04 01:32:29] --- Checking for inconsistencies ---
[2025-07-04 01:32:29] === Expiration Check Completed ===
[2025-07-04 01:32:29] Processed: 0 expired subscriptions
[2025-07-04 01:32:29] Disabled: 0 Jellyfin accounts
[2025-07-04 01:32:29] Errors: 0
[2025-07-04 01:32:29] === End of Expiration Check ===
[2025-07-04 01:37:31] === Expiration Check Started ===
[2025-07-04 01:37:31] Found 0 expired subscriptions
[2025-07-04 01:37:31] --- Checking for inconsistencies ---
[2025-07-04 01:37:31] === Expiration Check Completed ===
[2025-07-04 01:37:31] Processed: 0 expired subscriptions
[2025-07-04 01:37:31] Disabled: 0 Jellyfin accounts
[2025-07-04 01:37:31] Errors: 0
[2025-07-04 01:37:31] === End of Expiration Check ===
[2025-07-04 01:42:32] === Expiration Check Started ===
[2025-07-04 01:42:32] Found 0 expired subscriptions
[2025-07-04 01:42:32] --- Checking for inconsistencies ---
[2025-07-04 01:42:32] === Expiration Check Completed ===
[2025-07-04 01:42:32] Processed: 0 expired subscriptions
[2025-07-04 01:42:32] Disabled: 0 Jellyfin accounts
[2025-07-04 01:42:32] Errors: 0
[2025-07-04 01:42:32] === End of Expiration Check ===
[2025-07-04 01:47:34] === Expiration Check Started ===
[2025-07-04 01:47:34] Found 0 expired subscriptions
[2025-07-04 01:47:34] --- Checking for inconsistencies ---
[2025-07-04 01:47:34] === Expiration Check Completed ===
[2025-07-04 01:47:34] Processed: 0 expired subscriptions
[2025-07-04 01:47:34] Disabled: 0 Jellyfin accounts
[2025-07-04 01:47:34] Errors: 0
[2025-07-04 01:47:34] === End of Expiration Check ===
[2025-07-04 01:52:35] === Expiration Check Started ===
[2025-07-04 01:52:35] Found 0 expired subscriptions
[2025-07-04 01:52:35] --- Checking for inconsistencies ---
[2025-07-04 01:52:35] === Expiration Check Completed ===
[2025-07-04 01:52:35] Processed: 0 expired subscriptions
[2025-07-04 01:52:35] Disabled: 0 Jellyfin accounts
[2025-07-04 01:52:35] Errors: 0
[2025-07-04 01:52:35] === End of Expiration Check ===
[2025-07-04 01:57:37] === Expiration Check Started ===
[2025-07-04 01:57:37] Found 0 expired subscriptions
[2025-07-04 01:57:37] --- Checking for inconsistencies ---
[2025-07-04 01:57:37] === Expiration Check Completed ===
[2025-07-04 01:57:37] Processed: 0 expired subscriptions
[2025-07-04 01:57:37] Disabled: 0 Jellyfin accounts
[2025-07-04 01:57:37] Errors: 0
[2025-07-04 01:57:37] === End of Expiration Check ===
[2025-07-04 02:02:38] === Expiration Check Started ===
[2025-07-04 02:02:38] Found 0 expired subscriptions
[2025-07-04 02:02:38] --- Checking for inconsistencies ---
[2025-07-04 02:02:38] === Expiration Check Completed ===
[2025-07-04 02:02:38] Processed: 0 expired subscriptions
[2025-07-04 02:02:38] Disabled: 0 Jellyfin accounts
[2025-07-04 02:02:38] Errors: 0
[2025-07-04 02:02:38] === End of Expiration Check ===
[2025-07-04 02:07:40] === Expiration Check Started ===
[2025-07-04 02:07:40] Found 0 expired subscriptions
[2025-07-04 02:07:40] --- Checking for inconsistencies ---
[2025-07-04 02:07:40] === Expiration Check Completed ===
[2025-07-04 02:07:40] Processed: 0 expired subscriptions
[2025-07-04 02:07:40] Disabled: 0 Jellyfin accounts
[2025-07-04 02:07:40] Errors: 0
[2025-07-04 02:07:40] === End of Expiration Check ===
[2025-07-04 02:12:43] === Expiration Check Started ===
[2025-07-04 02:12:43] Found 0 expired subscriptions
[2025-07-04 02:12:43] --- Checking for inconsistencies ---
[2025-07-04 02:12:43] === Expiration Check Completed ===
[2025-07-04 02:12:43] Processed: 0 expired subscriptions
[2025-07-04 02:12:43] Disabled: 0 Jellyfin accounts
[2025-07-04 02:12:43] Errors: 0
[2025-07-04 02:12:43] === End of Expiration Check ===
[2025-07-04 02:17:44] === Expiration Check Started ===
[2025-07-04 02:17:44] Found 0 expired subscriptions
[2025-07-04 02:17:44] --- Checking for inconsistencies ---
[2025-07-04 02:17:44] === Expiration Check Completed ===
[2025-07-04 02:17:44] Processed: 0 expired subscriptions
[2025-07-04 02:17:44] Disabled: 0 Jellyfin accounts
[2025-07-04 02:17:44] Errors: 0
[2025-07-04 02:17:44] === End of Expiration Check ===
[2025-07-04 02:22:46] === Expiration Check Started ===
[2025-07-04 02:22:46] Found 0 expired subscriptions
[2025-07-04 02:22:46] --- Checking for inconsistencies ---
[2025-07-04 02:22:46] === Expiration Check Completed ===
[2025-07-04 02:22:46] Processed: 0 expired subscriptions
[2025-07-04 02:22:46] Disabled: 0 Jellyfin accounts
[2025-07-04 02:22:46] Errors: 0
[2025-07-04 02:22:46] === End of Expiration Check ===
[2025-07-04 02:27:47] === Expiration Check Started ===
[2025-07-04 02:27:47] Found 0 expired subscriptions
[2025-07-04 02:27:47] --- Checking for inconsistencies ---
[2025-07-04 02:27:47] === Expiration Check Completed ===
[2025-07-04 02:27:47] Processed: 0 expired subscriptions
[2025-07-04 02:27:47] Disabled: 0 Jellyfin accounts
[2025-07-04 02:27:47] Errors: 0
[2025-07-04 02:27:47] === End of Expiration Check ===
[2025-07-04 02:32:48] === Expiration Check Started ===
[2025-07-04 02:32:48] Found 0 expired subscriptions
[2025-07-04 02:32:48] --- Checking for inconsistencies ---
[2025-07-04 02:32:48] === Expiration Check Completed ===
[2025-07-04 02:32:48] Processed: 0 expired subscriptions
[2025-07-04 02:32:48] Disabled: 0 Jellyfin accounts
[2025-07-04 02:32:48] Errors: 0
[2025-07-04 02:32:48] === End of Expiration Check ===
[2025-07-04 02:37:49] === Expiration Check Started ===
[2025-07-04 02:37:49] Found 0 expired subscriptions
[2025-07-04 02:37:49] --- Checking for inconsistencies ---
[2025-07-04 02:37:49] === Expiration Check Completed ===
[2025-07-04 02:37:49] Processed: 0 expired subscriptions
[2025-07-04 02:37:49] Disabled: 0 Jellyfin accounts
[2025-07-04 02:37:49] Errors: 0
[2025-07-04 02:37:49] === End of Expiration Check ===
[2025-07-04 02:42:50] === Expiration Check Started ===
[2025-07-04 02:42:50] Found 0 expired subscriptions
[2025-07-04 02:42:50] --- Checking for inconsistencies ---
[2025-07-04 02:42:50] === Expiration Check Completed ===
[2025-07-04 02:42:50] Processed: 0 expired subscriptions
[2025-07-04 02:42:50] Disabled: 0 Jellyfin accounts
[2025-07-04 02:42:50] Errors: 0
[2025-07-04 02:42:50] === End of Expiration Check ===
[2025-07-04 02:47:51] === Expiration Check Started ===
[2025-07-04 02:47:51] Found 0 expired subscriptions
[2025-07-04 02:47:51] --- Checking for inconsistencies ---
[2025-07-04 02:47:51] === Expiration Check Completed ===
[2025-07-04 02:47:51] Processed: 0 expired subscriptions
[2025-07-04 02:47:51] Disabled: 0 Jellyfin accounts
[2025-07-04 02:47:51] Errors: 0
[2025-07-04 02:47:51] === End of Expiration Check ===
[2025-07-04 02:52:52] === Expiration Check Started ===
[2025-07-04 02:52:52] Found 0 expired subscriptions
[2025-07-04 02:52:52] --- Checking for inconsistencies ---
[2025-07-04 02:52:52] === Expiration Check Completed ===
[2025-07-04 02:52:52] Processed: 0 expired subscriptions
[2025-07-04 02:52:52] Disabled: 0 Jellyfin accounts
[2025-07-04 02:52:52] Errors: 0
[2025-07-04 02:52:52] === End of Expiration Check ===
[2025-07-04 02:57:53] === Expiration Check Started ===
[2025-07-04 02:57:53] Found 0 expired subscriptions
[2025-07-04 02:57:53] --- Checking for inconsistencies ---
[2025-07-04 02:57:53] === Expiration Check Completed ===
[2025-07-04 02:57:53] Processed: 0 expired subscriptions
[2025-07-04 02:57:53] Disabled: 0 Jellyfin accounts
[2025-07-04 02:57:53] Errors: 0
[2025-07-04 02:57:53] === End of Expiration Check ===
[2025-07-04 03:02:54] === Expiration Check Started ===
[2025-07-04 03:02:54] Found 0 expired subscriptions
[2025-07-04 03:02:54] --- Checking for inconsistencies ---
[2025-07-04 03:02:54] === Expiration Check Completed ===
[2025-07-04 03:02:54] Processed: 0 expired subscriptions
[2025-07-04 03:02:54] Disabled: 0 Jellyfin accounts
[2025-07-04 03:02:54] Errors: 0
[2025-07-04 03:02:54] === End of Expiration Check ===
[2025-07-04 03:07:54] === Expiration Check Started ===
[2025-07-04 03:07:54] Found 0 expired subscriptions
[2025-07-04 03:07:54] --- Checking for inconsistencies ---
[2025-07-04 03:07:54] === Expiration Check Completed ===
[2025-07-04 03:07:54] Processed: 0 expired subscriptions
[2025-07-04 03:07:54] Disabled: 0 Jellyfin accounts
[2025-07-04 03:07:54] Errors: 0
[2025-07-04 03:07:54] === End of Expiration Check ===
[2025-07-04 03:12:55] === Expiration Check Started ===
[2025-07-04 03:12:55] Found 0 expired subscriptions
[2025-07-04 03:12:55] --- Checking for inconsistencies ---
[2025-07-04 03:12:55] === Expiration Check Completed ===
[2025-07-04 03:12:55] Processed: 0 expired subscriptions
[2025-07-04 03:12:55] Disabled: 0 Jellyfin accounts
[2025-07-04 03:12:55] Errors: 0
[2025-07-04 03:12:55] === End of Expiration Check ===
[2025-07-04 03:17:56] === Expiration Check Started ===
[2025-07-04 03:17:56] Found 0 expired subscriptions
[2025-07-04 03:17:56] --- Checking for inconsistencies ---
[2025-07-04 03:17:56] === Expiration Check Completed ===
[2025-07-04 03:17:56] Processed: 0 expired subscriptions
[2025-07-04 03:17:56] Disabled: 0 Jellyfin accounts
[2025-07-04 03:17:56] Errors: 0
[2025-07-04 03:17:56] === End of Expiration Check ===
[2025-07-04 03:22:56] === Expiration Check Started ===
[2025-07-04 03:22:56] Found 0 expired subscriptions
[2025-07-04 03:22:56] --- Checking for inconsistencies ---
[2025-07-04 03:22:56] === Expiration Check Completed ===
[2025-07-04 03:22:56] Processed: 0 expired subscriptions
[2025-07-04 03:22:56] Disabled: 0 Jellyfin accounts
[2025-07-04 03:22:56] Errors: 0
[2025-07-04 03:22:56] === End of Expiration Check ===
[2025-07-04 03:27:57] === Expiration Check Started ===
[2025-07-04 03:27:57] Found 0 expired subscriptions
[2025-07-04 03:27:57] --- Checking for inconsistencies ---
[2025-07-04 03:27:57] === Expiration Check Completed ===
[2025-07-04 03:27:57] Processed: 0 expired subscriptions
[2025-07-04 03:27:57] Disabled: 0 Jellyfin accounts
[2025-07-04 03:27:57] Errors: 0
[2025-07-04 03:27:57] === End of Expiration Check ===
[2025-07-04 03:32:57] === Expiration Check Started ===
[2025-07-04 03:32:57] Found 0 expired subscriptions
[2025-07-04 03:32:57] --- Checking for inconsistencies ---
[2025-07-04 03:32:58] === Expiration Check Completed ===
[2025-07-04 03:32:58] Processed: 0 expired subscriptions
[2025-07-04 03:32:58] Disabled: 0 Jellyfin accounts
[2025-07-04 03:32:58] Errors: 0
[2025-07-04 03:32:58] === End of Expiration Check ===
[2025-07-04 03:37:58] === Expiration Check Started ===
[2025-07-04 03:37:58] Found 0 expired subscriptions
[2025-07-04 03:37:58] --- Checking for inconsistencies ---
[2025-07-04 03:37:58] === Expiration Check Completed ===
[2025-07-04 03:37:58] Processed: 0 expired subscriptions
[2025-07-04 03:37:58] Disabled: 0 Jellyfin accounts
[2025-07-04 03:37:58] Errors: 0
[2025-07-04 03:37:58] === End of Expiration Check ===
[2025-07-04 03:42:59] === Expiration Check Started ===
[2025-07-04 03:42:59] Found 0 expired subscriptions
[2025-07-04 03:42:59] --- Checking for inconsistencies ---
[2025-07-04 03:42:59] === Expiration Check Completed ===
[2025-07-04 03:42:59] Processed: 0 expired subscriptions
[2025-07-04 03:42:59] Disabled: 0 Jellyfin accounts
[2025-07-04 03:42:59] Errors: 0
[2025-07-04 03:42:59] === End of Expiration Check ===
[2025-07-04 03:49:23] === Expiration Check Started ===
[2025-07-04 03:49:23] Found 0 expired subscriptions
[2025-07-04 03:49:23] --- Checking for inconsistencies ---
[2025-07-04 03:49:23] === Expiration Check Completed ===
[2025-07-04 03:49:23] Processed: 0 expired subscriptions
[2025-07-04 03:49:23] Disabled: 0 Jellyfin accounts
[2025-07-04 03:49:23] Errors: 0
[2025-07-04 03:49:23] === End of Expiration Check ===
[2025-07-04 03:55:14] === Expiration Check Started ===
[2025-07-04 03:55:14] Found 0 expired subscriptions
[2025-07-04 03:55:14] --- Checking for inconsistencies ---
[2025-07-04 03:55:14] === Expiration Check Completed ===
[2025-07-04 03:55:14] Processed: 0 expired subscriptions
[2025-07-04 03:55:14] Disabled: 0 Jellyfin accounts
[2025-07-04 03:55:14] Errors: 0
[2025-07-04 03:55:14] === End of Expiration Check ===
[2025-07-04 04:04:43] === Expiration Check Started ===
[2025-07-04 04:04:43] Found 0 expired subscriptions
[2025-07-04 04:04:43] --- Checking for inconsistencies ---
[2025-07-04 04:04:43] === Expiration Check Completed ===
[2025-07-04 04:04:43] Processed: 0 expired subscriptions
[2025-07-04 04:04:43] Disabled: 0 Jellyfin accounts
[2025-07-04 04:04:43] Errors: 0
[2025-07-04 04:04:43] === End of Expiration Check ===
[2025-07-04 04:10:19] === Expiration Check Started ===
[2025-07-04 04:10:19] Found 1 expired subscriptions
[2025-07-04 04:10:19] Processing expired subscription for user: test6 (expired: 2025-07-04 11:09:42)
[2025-07-04 04:10:19] ✅ Disabled Jellyfin user: test6 (ID: 3010cfbba22c48049bb1de54ecdef525)
[2025-07-04 04:10:19] ✅ Successfully processed expiration for user: test6
[2025-07-04 04:10:19] --- Checking for inconsistencies ---
[2025-07-04 04:10:19] === Expiration Check Completed ===
[2025-07-04 04:10:19] Processed: 1 expired subscriptions
[2025-07-04 04:10:19] Disabled: 1 Jellyfin accounts
[2025-07-04 04:10:19] Errors: 0
[2025-07-04 04:10:19] === End of Expiration Check ===
[2025-07-04 05:04:45] === Expiration Check Started ===
[2025-07-04 05:04:45] Found 0 expired subscriptions
[2025-07-04 05:04:45] --- Checking for inconsistencies ---
[2025-07-04 05:04:46] === Expiration Check Completed ===
[2025-07-04 05:04:46] Processed: 0 expired subscriptions
[2025-07-04 05:04:46] Disabled: 0 Jellyfin accounts
[2025-07-04 05:04:46] Errors: 0
[2025-07-04 05:04:46] === End of Expiration Check ===
[2025-07-04 06:04:48] === Expiration Check Started ===
[2025-07-04 06:04:48] Found 0 expired subscriptions
[2025-07-04 06:04:48] --- Checking for inconsistencies ---
[2025-07-04 06:04:48] === Expiration Check Completed ===
[2025-07-04 06:04:48] Processed: 0 expired subscriptions
[2025-07-04 06:04:48] Disabled: 0 Jellyfin accounts
[2025-07-04 06:04:48] Errors: 0
[2025-07-04 06:04:48] === End of Expiration Check ===
[2025-07-04 07:04:50] === Expiration Check Started ===
[2025-07-04 07:04:50] Found 0 expired subscriptions
[2025-07-04 07:04:50] --- Checking for inconsistencies ---
[2025-07-04 07:04:50] === Expiration Check Completed ===
[2025-07-04 07:04:50] Processed: 0 expired subscriptions
[2025-07-04 07:04:50] Disabled: 0 Jellyfin accounts
[2025-07-04 07:04:50] Errors: 0
[2025-07-04 07:04:50] === End of Expiration Check ===
[2025-07-04 08:04:53] === Expiration Check Started ===
[2025-07-04 08:04:53] Found 0 expired subscriptions
[2025-07-04 08:04:53] --- Checking for inconsistencies ---
[2025-07-04 08:04:53] === Expiration Check Completed ===
[2025-07-04 08:04:53] Processed: 0 expired subscriptions
[2025-07-04 08:04:53] Disabled: 0 Jellyfin accounts
[2025-07-04 08:04:53] Errors: 0
[2025-07-04 08:04:53] === End of Expiration Check ===
[2025-07-04 09:04:55] === Expiration Check Started ===
[2025-07-04 09:04:55] Found 0 expired subscriptions
[2025-07-04 09:04:55] --- Checking for inconsistencies ---
[2025-07-04 09:04:55] === Expiration Check Completed ===
[2025-07-04 09:04:55] Processed: 0 expired subscriptions
[2025-07-04 09:04:55] Disabled: 0 Jellyfin accounts
[2025-07-04 09:04:55] Errors: 0
[2025-07-04 09:04:55] === End of Expiration Check ===
[2025-07-04 10:04:57] === Expiration Check Started ===
[2025-07-04 10:04:57] Found 0 expired subscriptions
[2025-07-04 10:04:57] --- Checking for inconsistencies ---
[2025-07-04 10:04:58] === Expiration Check Completed ===
[2025-07-04 10:04:58] Processed: 0 expired subscriptions
[2025-07-04 10:04:58] Disabled: 0 Jellyfin accounts
[2025-07-04 10:04:58] Errors: 0
[2025-07-04 10:04:58] === End of Expiration Check ===
[2025-07-04 11:05:00] === Expiration Check Started ===
[2025-07-04 11:05:00] Found 0 expired subscriptions
[2025-07-04 11:05:00] --- Checking for inconsistencies ---
[2025-07-04 11:05:00] === Expiration Check Completed ===
[2025-07-04 11:05:00] Processed: 0 expired subscriptions
[2025-07-04 11:05:00] Disabled: 0 Jellyfin accounts
[2025-07-04 11:05:00] Errors: 0
[2025-07-04 11:05:00] === End of Expiration Check ===
[2025-07-04 12:05:03] === Expiration Check Started ===
[2025-07-04 12:05:03] Found 0 expired subscriptions
[2025-07-04 12:05:03] --- Checking for inconsistencies ---
[2025-07-04 12:05:04] === Expiration Check Completed ===
[2025-07-04 12:05:04] Processed: 0 expired subscriptions
[2025-07-04 12:05:04] Disabled: 0 Jellyfin accounts
[2025-07-04 12:05:04] Errors: 0
[2025-07-04 12:05:04] === End of Expiration Check ===
[2025-07-04 13:05:06] === Expiration Check Started ===
[2025-07-04 13:05:06] Found 1 expired subscriptions
[2025-07-04 13:05:06] Processing expired subscription for user: test7 (expired: 2025-07-04 19:48:46)
[2025-07-04 13:05:07] ✅ Disabled Jellyfin user: test7 (ID: 25edde6cdee64618b490fe61fbb5839b)
[2025-07-04 13:05:07] ✅ Successfully processed expiration for user: test7
[2025-07-04 13:05:07] --- Checking for inconsistencies ---
[2025-07-04 13:05:07] === Expiration Check Completed ===
[2025-07-04 13:05:07] Processed: 1 expired subscriptions
[2025-07-04 13:05:07] Disabled: 1 Jellyfin accounts
[2025-07-04 13:05:07] Errors: 0
[2025-07-04 13:05:07] === End of Expiration Check ===
[2025-07-04 14:05:10] === Expiration Check Started ===
[2025-07-04 14:05:10] Found 0 expired subscriptions
[2025-07-04 14:05:10] --- Checking for inconsistencies ---
[2025-07-04 14:05:11] === Expiration Check Completed ===
[2025-07-04 14:05:11] Processed: 0 expired subscriptions
[2025-07-04 14:05:11] Disabled: 0 Jellyfin accounts
[2025-07-04 14:05:11] Errors: 0
[2025-07-04 14:05:11] === End of Expiration Check ===
[2025-07-04 15:05:13] === Expiration Check Started ===
[2025-07-04 15:05:13] Found 0 expired subscriptions
[2025-07-04 15:05:13] --- Checking for inconsistencies ---
[2025-07-04 15:05:14] === Expiration Check Completed ===
[2025-07-04 15:05:14] Processed: 0 expired subscriptions
[2025-07-04 15:05:14] Disabled: 0 Jellyfin accounts
[2025-07-04 15:05:14] Errors: 0
[2025-07-04 15:05:14] === End of Expiration Check ===
[2025-07-04 16:05:17] === Expiration Check Started ===
[2025-07-04 16:05:17] Found 0 expired subscriptions
[2025-07-04 16:05:17] --- Checking for inconsistencies ---
[2025-07-04 16:05:17] === Expiration Check Completed ===
[2025-07-04 16:05:17] Processed: 0 expired subscriptions
[2025-07-04 16:05:17] Disabled: 0 Jellyfin accounts
[2025-07-04 16:05:17] Errors: 0
[2025-07-04 16:05:17] === End of Expiration Check ===
[2025-07-04 17:05:19] === Expiration Check Started ===
[2025-07-04 17:05:19] Found 0 expired subscriptions
[2025-07-04 17:05:19] --- Checking for inconsistencies ---
[2025-07-04 17:05:20] === Expiration Check Completed ===
[2025-07-04 17:05:20] Processed: 0 expired subscriptions
[2025-07-04 17:05:20] Disabled: 0 Jellyfin accounts
[2025-07-04 17:05:20] Errors: 0
[2025-07-04 17:05:20] === End of Expiration Check ===
[2025-07-04 18:05:21] === Expiration Check Started ===
[2025-07-04 18:05:21] Found 0 expired subscriptions
[2025-07-04 18:05:21] --- Checking for inconsistencies ---
[2025-07-04 18:05:22] === Expiration Check Completed ===
[2025-07-04 18:05:22] Processed: 0 expired subscriptions
[2025-07-04 18:05:22] Disabled: 0 Jellyfin accounts
[2025-07-04 18:05:22] Errors: 0
[2025-07-04 18:05:22] === End of Expiration Check ===
[2025-07-04 19:05:24] === Expiration Check Started ===
[2025-07-04 19:05:24] Found 0 expired subscriptions
[2025-07-04 19:05:24] --- Checking for inconsistencies ---
[2025-07-04 19:05:24] === Expiration Check Completed ===
[2025-07-04 19:05:24] Processed: 0 expired subscriptions
[2025-07-04 19:05:24] Disabled: 0 Jellyfin accounts
[2025-07-04 19:05:24] Errors: 0
[2025-07-04 19:05:24] === End of Expiration Check ===
[2025-07-04 20:05:26] === Expiration Check Started ===
[2025-07-04 20:05:26] Found 0 expired subscriptions
[2025-07-04 20:05:26] --- Checking for inconsistencies ---
[2025-07-04 20:05:26] === Expiration Check Completed ===
[2025-07-04 20:05:26] Processed: 0 expired subscriptions
[2025-07-04 20:05:26] Disabled: 0 Jellyfin accounts
[2025-07-04 20:05:26] Errors: 0
[2025-07-04 20:05:26] === End of Expiration Check ===
[2025-07-04 21:05:28] === Expiration Check Started ===
[2025-07-04 21:05:28] Found 0 expired subscriptions
[2025-07-04 21:05:28] --- Checking for inconsistencies ---
[2025-07-04 21:05:28] === Expiration Check Completed ===
[2025-07-04 21:05:28] Processed: 0 expired subscriptions
[2025-07-04 21:05:28] Disabled: 0 Jellyfin accounts
[2025-07-04 21:05:28] Errors: 0
[2025-07-04 21:05:28] === End of Expiration Check ===
[2025-07-04 22:05:30] === Expiration Check Started ===
[2025-07-04 22:05:30] Found 0 expired subscriptions
[2025-07-04 22:05:30] --- Checking for inconsistencies ---
[2025-07-04 22:05:31] === Expiration Check Completed ===
[2025-07-04 22:05:31] Processed: 0 expired subscriptions
[2025-07-04 22:05:31] Disabled: 0 Jellyfin accounts
[2025-07-04 22:05:31] Errors: 0
[2025-07-04 22:05:31] === End of Expiration Check ===
[2025-07-04 23:05:33] === Expiration Check Started ===
[2025-07-04 23:05:33] Found 0 expired subscriptions
[2025-07-04 23:05:33] --- Checking for inconsistencies ---
[2025-07-04 23:05:33] === Expiration Check Completed ===
[2025-07-04 23:05:33] Processed: 0 expired subscriptions
[2025-07-04 23:05:33] Disabled: 0 Jellyfin accounts
[2025-07-04 23:05:33] Errors: 0
[2025-07-04 23:05:33] === End of Expiration Check ===
[2025-07-05 00:05:35] === Expiration Check Started ===
[2025-07-05 00:05:35] Found 0 expired subscriptions
[2025-07-05 00:05:35] --- Checking for inconsistencies ---
[2025-07-05 00:05:35] === Expiration Check Completed ===
[2025-07-05 00:05:35] Processed: 0 expired subscriptions
[2025-07-05 00:05:35] Disabled: 0 Jellyfin accounts
[2025-07-05 00:05:35] Errors: 0
[2025-07-05 00:05:35] === End of Expiration Check ===
[2025-07-05 01:05:37] === Expiration Check Started ===
[2025-07-05 01:05:37] Found 0 expired subscriptions
[2025-07-05 01:05:37] --- Checking for inconsistencies ---
[2025-07-05 01:05:38] === Expiration Check Completed ===
[2025-07-05 01:05:38] Processed: 0 expired subscriptions
[2025-07-05 01:05:38] Disabled: 0 Jellyfin accounts
[2025-07-05 01:05:38] Errors: 0
[2025-07-05 01:05:38] === End of Expiration Check ===
[2025-07-05 02:05:39] === Expiration Check Started ===
[2025-07-05 02:05:39] Found 0 expired subscriptions
[2025-07-05 02:05:39] --- Checking for inconsistencies ---
[2025-07-05 02:05:40] === Expiration Check Completed ===
[2025-07-05 02:05:40] Processed: 0 expired subscriptions
[2025-07-05 02:05:40] Disabled: 0 Jellyfin accounts
[2025-07-05 02:05:40] Errors: 0
[2025-07-05 02:05:40] === End of Expiration Check ===
[2025-07-05 03:05:42] === Expiration Check Started ===
[2025-07-05 03:05:42] Found 0 expired subscriptions
[2025-07-05 03:05:42] --- Checking for inconsistencies ---
[2025-07-05 03:05:42] === Expiration Check Completed ===
[2025-07-05 03:05:42] Processed: 0 expired subscriptions
[2025-07-05 03:05:42] Disabled: 0 Jellyfin accounts
[2025-07-05 03:05:42] Errors: 0
[2025-07-05 03:05:42] === End of Expiration Check ===
[2025-07-05 04:05:45] === Expiration Check Started ===
[2025-07-05 04:05:45] Found 0 expired subscriptions
[2025-07-05 04:05:45] --- Checking for inconsistencies ---
[2025-07-05 04:05:45] === Expiration Check Completed ===
[2025-07-05 04:05:45] Processed: 0 expired subscriptions
[2025-07-05 04:05:45] Disabled: 0 Jellyfin accounts
[2025-07-05 04:05:45] Errors: 0
[2025-07-05 04:05:45] === End of Expiration Check ===
[2025-07-05 04:44:11] === Expiration Check Started ===
[2025-07-05 04:44:11] Found 0 expired subscriptions
[2025-07-05 04:44:11] --- Checking for inconsistencies ---
[2025-07-05 04:44:12] === Expiration Check Completed ===
[2025-07-05 04:44:12] Processed: 0 expired subscriptions
[2025-07-05 04:44:12] Disabled: 0 Jellyfin accounts
[2025-07-05 04:44:12] Errors: 0
[2025-07-05 04:44:12] === End of Expiration Check ===
[2025-07-05 05:44:13] === Expiration Check Started ===
[2025-07-05 05:44:13] Found 0 expired subscriptions
[2025-07-05 05:44:14] --- Checking for inconsistencies ---
[2025-07-05 05:44:15] === Expiration Check Completed ===
[2025-07-05 05:44:15] Processed: 0 expired subscriptions
[2025-07-05 05:44:15] Disabled: 0 Jellyfin accounts
[2025-07-05 05:44:15] Errors: 0
[2025-07-05 05:44:15] === End of Expiration Check ===
[2025-07-05 06:21:02] === Expiration Check Started ===
[2025-07-05 06:21:02] Found 0 expired subscriptions
[2025-07-05 06:21:02] --- Checking for inconsistencies ---
[2025-07-05 06:21:02] === Expiration Check Completed ===
[2025-07-05 06:21:02] Processed: 0 expired subscriptions
[2025-07-05 06:21:02] Disabled: 0 Jellyfin accounts
[2025-07-05 06:21:02] Errors: 0
[2025-07-05 06:21:02] === End of Expiration Check ===
[2025-07-05 07:21:05] === Expiration Check Started ===
[2025-07-05 07:21:05] Found 0 expired subscriptions
[2025-07-05 07:21:05] --- Checking for inconsistencies ---
[2025-07-05 07:21:06] === Expiration Check Completed ===
[2025-07-05 07:21:06] Processed: 0 expired subscriptions
[2025-07-05 07:21:06] Disabled: 0 Jellyfin accounts
[2025-07-05 07:21:06] Errors: 0
[2025-07-05 07:21:06] === End of Expiration Check ===
[2025-07-05 08:21:07] === Expiration Check Started ===
[2025-07-05 08:21:07] Found 0 expired subscriptions
[2025-07-05 08:21:07] --- Checking for inconsistencies ---
[2025-07-05 08:21:08] === Expiration Check Completed ===
[2025-07-05 08:21:08] Processed: 0 expired subscriptions
[2025-07-05 08:21:08] Disabled: 0 Jellyfin accounts
[2025-07-05 08:21:08] Errors: 0
[2025-07-05 08:21:08] === End of Expiration Check ===
[2025-07-05 09:21:11] === Expiration Check Started ===
[2025-07-05 09:21:11] Found 0 expired subscriptions
[2025-07-05 09:21:11] --- Checking for inconsistencies ---
[2025-07-05 09:21:12] === Expiration Check Completed ===
[2025-07-05 09:21:12] Processed: 0 expired subscriptions
[2025-07-05 09:21:12] Disabled: 0 Jellyfin accounts
[2025-07-05 09:21:12] Errors: 0
[2025-07-05 09:21:12] === End of Expiration Check ===
[2025-07-05 10:21:14] === Expiration Check Started ===
[2025-07-05 10:21:14] Found 0 expired subscriptions
[2025-07-05 10:21:14] --- Checking for inconsistencies ---
[2025-07-05 10:21:15] === Expiration Check Completed ===
[2025-07-05 10:21:15] Processed: 0 expired subscriptions
[2025-07-05 10:21:15] Disabled: 0 Jellyfin accounts
[2025-07-05 10:21:15] Errors: 0
[2025-07-05 10:21:15] === End of Expiration Check ===
[2025-07-05 11:21:17] === Expiration Check Started ===
[2025-07-05 11:21:17] Found 0 expired subscriptions
[2025-07-05 11:21:17] --- Checking for inconsistencies ---
[2025-07-05 11:21:18] === Expiration Check Completed ===
[2025-07-05 11:21:18] Processed: 0 expired subscriptions
[2025-07-05 11:21:18] Disabled: 0 Jellyfin accounts
[2025-07-05 11:21:18] Errors: 0
[2025-07-05 11:21:18] === End of Expiration Check ===
[2025-07-05 12:21:20] === Expiration Check Started ===
[2025-07-05 12:21:20] Found 0 expired subscriptions
[2025-07-05 12:21:20] --- Checking for inconsistencies ---
[2025-07-05 12:21:21] === Expiration Check Completed ===
[2025-07-05 12:21:21] Processed: 0 expired subscriptions
[2025-07-05 12:21:21] Disabled: 0 Jellyfin accounts
[2025-07-05 12:21:21] Errors: 0
[2025-07-05 12:21:21] === End of Expiration Check ===
[2025-07-05 13:21:24] === Expiration Check Started ===
[2025-07-05 13:21:24] Found 0 expired subscriptions
[2025-07-05 13:21:24] --- Checking for inconsistencies ---
[2025-07-05 13:21:25] === Expiration Check Completed ===
[2025-07-05 13:21:25] Processed: 0 expired subscriptions
[2025-07-05 13:21:25] Disabled: 0 Jellyfin accounts
[2025-07-05 13:21:25] Errors: 0
[2025-07-05 13:21:25] === End of Expiration Check ===
[2025-07-05 14:21:27] === Expiration Check Started ===
[2025-07-05 14:21:27] Found 0 expired subscriptions
[2025-07-05 14:21:27] --- Checking for inconsistencies ---
[2025-07-05 14:21:28] === Expiration Check Completed ===
[2025-07-05 14:21:28] Processed: 0 expired subscriptions
[2025-07-05 14:21:28] Disabled: 0 Jellyfin accounts
[2025-07-05 14:21:28] Errors: 0
[2025-07-05 14:21:28] === End of Expiration Check ===
[2025-07-05 15:21:30] === Expiration Check Started ===
[2025-07-05 15:21:30] Found 0 expired subscriptions
[2025-07-05 15:21:30] --- Checking for inconsistencies ---
[2025-07-05 15:21:31] === Expiration Check Completed ===
[2025-07-05 15:21:31] Processed: 0 expired subscriptions
[2025-07-05 15:21:31] Disabled: 0 Jellyfin accounts
[2025-07-05 15:21:31] Errors: 0
[2025-07-05 15:21:31] === End of Expiration Check ===
[2025-07-05 15:47:16] === Expiration Check Started ===
[2025-07-05 15:47:16] Found 0 expired subscriptions
[2025-07-05 15:47:16] --- Checking for inconsistencies ---
[2025-07-05 15:47:16] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-05 15:47:16] ❌ Error checking orphaned user test7: HTTP Error 530
[2025-07-05 15:47:17] ❌ Error checking orphaned user Hemawat: HTTP Error 530
[2025-07-05 15:47:17] === Expiration Check Completed ===
[2025-07-05 15:47:17] Processed: 0 expired subscriptions
[2025-07-05 15:47:17] Disabled: 0 Jellyfin accounts
[2025-07-05 15:47:17] Errors: 3
[2025-07-05 15:47:17] === End of Expiration Check ===
[2025-07-05 16:38:11] === Expiration Check Started ===
[2025-07-05 16:38:11] Found 0 expired subscriptions
[2025-07-05 16:38:11] --- Checking for inconsistencies ---
[2025-07-05 16:38:13] === Expiration Check Completed ===
[2025-07-05 16:38:13] Processed: 0 expired subscriptions
[2025-07-05 16:38:13] Disabled: 0 Jellyfin accounts
[2025-07-05 16:38:13] Errors: 0
[2025-07-05 16:38:13] === End of Expiration Check ===
[2025-07-06 02:25:43] === Expiration Check Started ===
[2025-07-06 02:25:43] Found 0 expired subscriptions
[2025-07-06 02:25:43] --- Checking for inconsistencies ---
[2025-07-06 02:25:45] === Expiration Check Completed ===
[2025-07-06 02:25:45] Processed: 0 expired subscriptions
[2025-07-06 02:25:45] Disabled: 0 Jellyfin accounts
[2025-07-06 02:25:45] Errors: 0
[2025-07-06 02:25:45] === End of Expiration Check ===
[2025-07-06 03:25:47] === Expiration Check Started ===
[2025-07-06 03:25:47] Found 0 expired subscriptions
[2025-07-06 03:25:47] --- Checking for inconsistencies ---
[2025-07-06 03:25:47] === Expiration Check Completed ===
[2025-07-06 03:25:47] Processed: 0 expired subscriptions
[2025-07-06 03:25:47] Disabled: 0 Jellyfin accounts
[2025-07-06 03:25:47] Errors: 0
[2025-07-06 03:25:47] === End of Expiration Check ===
[2025-07-06 04:25:49] === Expiration Check Started ===
[2025-07-06 04:25:49] Found 0 expired subscriptions
[2025-07-06 04:25:49] --- Checking for inconsistencies ---
[2025-07-06 04:25:49] ❌ Error checking orphaned user Hemawat: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 04:25:49] ❌ Error checking orphaned user test6: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 04:25:49] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 04:25:49] ❌ Error checking orphaned user test8: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 04:25:49] === Expiration Check Completed ===
[2025-07-06 04:25:49] Processed: 0 expired subscriptions
[2025-07-06 04:25:49] Disabled: 0 Jellyfin accounts
[2025-07-06 04:25:49] Errors: 4
[2025-07-06 04:25:49] === End of Expiration Check ===
[2025-07-06 05:25:51] === Expiration Check Started ===
[2025-07-06 05:25:51] Found 0 expired subscriptions
[2025-07-06 05:25:51] --- Checking for inconsistencies ---
[2025-07-06 05:25:52] === Expiration Check Completed ===
[2025-07-06 05:25:52] Processed: 0 expired subscriptions
[2025-07-06 05:25:52] Disabled: 0 Jellyfin accounts
[2025-07-06 05:25:52] Errors: 0
[2025-07-06 05:25:52] === End of Expiration Check ===
[2025-07-06 06:25:53] === Expiration Check Started ===
[2025-07-06 06:25:53] Found 0 expired subscriptions
[2025-07-06 06:25:53] --- Checking for inconsistencies ---
[2025-07-06 06:25:54] === Expiration Check Completed ===
[2025-07-06 06:25:54] Processed: 0 expired subscriptions
[2025-07-06 06:25:54] Disabled: 0 Jellyfin accounts
[2025-07-06 06:25:54] Errors: 0
[2025-07-06 06:25:54] === End of Expiration Check ===
[2025-07-06 07:25:56] === Expiration Check Started ===
[2025-07-06 07:25:56] Found 0 expired subscriptions
[2025-07-06 07:25:56] --- Checking for inconsistencies ---
[2025-07-06 07:25:57] === Expiration Check Completed ===
[2025-07-06 07:25:57] Processed: 0 expired subscriptions
[2025-07-06 07:25:57] Disabled: 0 Jellyfin accounts
[2025-07-06 07:25:57] Errors: 0
[2025-07-06 07:25:57] === End of Expiration Check ===
[2025-07-06 08:25:59] === Expiration Check Started ===
[2025-07-06 08:25:59] Found 0 expired subscriptions
[2025-07-06 08:25:59] --- Checking for inconsistencies ---
[2025-07-06 08:26:00] === Expiration Check Completed ===
[2025-07-06 08:26:00] Processed: 0 expired subscriptions
[2025-07-06 08:26:00] Disabled: 0 Jellyfin accounts
[2025-07-06 08:26:00] Errors: 0
[2025-07-06 08:26:00] === End of Expiration Check ===
[2025-07-06 09:26:01] === Expiration Check Started ===
[2025-07-06 09:26:01] Found 0 expired subscriptions
[2025-07-06 09:26:01] --- Checking for inconsistencies ---
[2025-07-06 09:26:02] === Expiration Check Completed ===
[2025-07-06 09:26:02] Processed: 0 expired subscriptions
[2025-07-06 09:26:02] Disabled: 0 Jellyfin accounts
[2025-07-06 09:26:02] Errors: 0
[2025-07-06 09:26:02] === End of Expiration Check ===
[2025-07-06 10:26:04] === Expiration Check Started ===
[2025-07-06 10:26:04] Found 0 expired subscriptions
[2025-07-06 10:26:04] --- Checking for inconsistencies ---
[2025-07-06 10:26:04] ❌ Error checking orphaned user Hemawat: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 10:26:04] ❌ Error checking orphaned user test6: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 10:26:04] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 10:26:04] ❌ Error checking orphaned user test8: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 10:26:04] === Expiration Check Completed ===
[2025-07-06 10:26:04] Processed: 0 expired subscriptions
[2025-07-06 10:26:04] Disabled: 0 Jellyfin accounts
[2025-07-06 10:26:04] Errors: 4
[2025-07-06 10:26:04] === End of Expiration Check ===
[2025-07-06 11:26:06] === Expiration Check Started ===
[2025-07-06 11:26:06] Found 0 expired subscriptions
[2025-07-06 11:26:06] --- Checking for inconsistencies ---
[2025-07-06 11:26:06] ❌ Error checking orphaned user Hemawat: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 11:26:06] ❌ Error checking orphaned user test6: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 11:26:06] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 11:26:06] ❌ Error checking orphaned user test8: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 11:26:06] === Expiration Check Completed ===
[2025-07-06 11:26:06] Processed: 0 expired subscriptions
[2025-07-06 11:26:06] Disabled: 0 Jellyfin accounts
[2025-07-06 11:26:06] Errors: 4
[2025-07-06 11:26:06] === End of Expiration Check ===
[2025-07-06 12:26:09] === Expiration Check Started ===
[2025-07-06 12:26:09] Found 0 expired subscriptions
[2025-07-06 12:26:09] --- Checking for inconsistencies ---
[2025-07-06 12:26:09] ❌ Error checking orphaned user Hemawat: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 12:26:09] ❌ Error checking orphaned user test6: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 12:26:09] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 12:26:09] ❌ Error checking orphaned user test8: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 12:26:09] === Expiration Check Completed ===
[2025-07-06 12:26:09] Processed: 0 expired subscriptions
[2025-07-06 12:26:09] Disabled: 0 Jellyfin accounts
[2025-07-06 12:26:09] Errors: 4
[2025-07-06 12:26:09] === End of Expiration Check ===
[2025-07-06 13:26:11] === Expiration Check Started ===
[2025-07-06 13:26:11] Found 0 expired subscriptions
[2025-07-06 13:26:11] --- Checking for inconsistencies ---
[2025-07-06 13:26:11] ❌ Error checking orphaned user Hemawat: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 13:26:11] ❌ Error checking orphaned user test6: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 13:26:11] ❌ Error checking orphaned user test7: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 13:26:11] ❌ Error checking orphaned user test8: cURL Error: Could not resolve host: jellyfin.embyjames.xyz
[2025-07-06 13:26:11] === Expiration Check Completed ===
[2025-07-06 13:26:11] Processed: 0 expired subscriptions
[2025-07-06 13:26:11] Disabled: 0 Jellyfin accounts
[2025-07-06 13:26:11] Errors: 4
[2025-07-06 13:26:11] === End of Expiration Check ===
[2025-07-06 14:26:13] === Expiration Check Started ===
[2025-07-06 14:26:13] Found 0 expired subscriptions
[2025-07-06 14:26:13] --- Checking for inconsistencies ---
[2025-07-06 14:26:14] === Expiration Check Completed ===
[2025-07-06 14:26:14] Processed: 0 expired subscriptions
[2025-07-06 14:26:14] Disabled: 0 Jellyfin accounts
[2025-07-06 14:26:14] Errors: 0
[2025-07-06 14:26:14] === End of Expiration Check ===
[2025-07-06 15:26:16] === Expiration Check Started ===
[2025-07-06 15:26:16] Found 0 expired subscriptions
[2025-07-06 15:26:16] --- Checking for inconsistencies ---
[2025-07-06 15:26:17] === Expiration Check Completed ===
[2025-07-06 15:26:17] Processed: 0 expired subscriptions
[2025-07-06 15:26:17] Disabled: 0 Jellyfin accounts
[2025-07-06 15:26:17] Errors: 0
[2025-07-06 15:26:17] === End of Expiration Check ===
[2025-07-06 16:26:19] === Expiration Check Started ===
[2025-07-06 16:26:19] Found 0 expired subscriptions
[2025-07-06 16:26:19] --- Checking for inconsistencies ---
[2025-07-06 16:26:20] === Expiration Check Completed ===
[2025-07-06 16:26:20] Processed: 0 expired subscriptions
[2025-07-06 16:26:20] Disabled: 0 Jellyfin accounts
[2025-07-06 16:26:20] Errors: 0
[2025-07-06 16:26:20] === End of Expiration Check ===
[2025-07-06 17:26:23] === Expiration Check Started ===
[2025-07-06 17:26:23] Found 0 expired subscriptions
[2025-07-06 17:26:23] --- Checking for inconsistencies ---
[2025-07-06 17:26:23] === Expiration Check Completed ===
[2025-07-06 17:26:23] Processed: 0 expired subscriptions
[2025-07-06 17:26:23] Disabled: 0 Jellyfin accounts
[2025-07-06 17:26:23] Errors: 0
[2025-07-06 17:26:23] === End of Expiration Check ===
[2025-07-06 18:26:27] === Expiration Check Started ===
[2025-07-06 18:26:27] Found 0 expired subscriptions
[2025-07-06 18:26:27] --- Checking for inconsistencies ---
[2025-07-06 18:26:27] === Expiration Check Completed ===
[2025-07-06 18:26:27] Processed: 0 expired subscriptions
[2025-07-06 18:26:27] Disabled: 0 Jellyfin accounts
[2025-07-06 18:26:27] Errors: 0
[2025-07-06 18:26:27] === End of Expiration Check ===
[2025-07-06 19:26:29] === Expiration Check Started ===
[2025-07-06 19:26:29] Found 0 expired subscriptions
[2025-07-06 19:26:29] --- Checking for inconsistencies ---
[2025-07-06 19:26:30] === Expiration Check Completed ===
[2025-07-06 19:26:30] Processed: 0 expired subscriptions
[2025-07-06 19:26:30] Disabled: 0 Jellyfin accounts
[2025-07-06 19:26:30] Errors: 0
[2025-07-06 19:26:30] === End of Expiration Check ===
[2025-07-06 20:26:32] === Expiration Check Started ===
[2025-07-06 20:26:32] Found 0 expired subscriptions
[2025-07-06 20:26:32] --- Checking for inconsistencies ---
[2025-07-06 20:26:32] === Expiration Check Completed ===
[2025-07-06 20:26:32] Processed: 0 expired subscriptions
[2025-07-06 20:26:32] Disabled: 0 Jellyfin accounts
[2025-07-06 20:26:32] Errors: 0
[2025-07-06 20:26:32] === End of Expiration Check ===
[2025-07-06 21:26:34] === Expiration Check Started ===
[2025-07-06 21:26:34] Found 0 expired subscriptions
[2025-07-06 21:26:34] --- Checking for inconsistencies ---
[2025-07-06 21:26:35] === Expiration Check Completed ===
[2025-07-06 21:26:35] Processed: 0 expired subscriptions
[2025-07-06 21:26:35] Disabled: 0 Jellyfin accounts
[2025-07-06 21:26:35] Errors: 0
[2025-07-06 21:26:35] === End of Expiration Check ===
[2025-07-06 22:26:37] === Expiration Check Started ===
[2025-07-06 22:26:37] Found 0 expired subscriptions
[2025-07-06 22:26:37] --- Checking for inconsistencies ---
[2025-07-06 22:26:37] === Expiration Check Completed ===
[2025-07-06 22:26:37] Processed: 0 expired subscriptions
[2025-07-06 22:26:37] Disabled: 0 Jellyfin accounts
[2025-07-06 22:26:37] Errors: 0
[2025-07-06 22:26:37] === End of Expiration Check ===
[2025-07-06 23:26:40] === Expiration Check Started ===
[2025-07-06 23:26:40] Found 0 expired subscriptions
[2025-07-06 23:26:40] --- Checking for inconsistencies ---
[2025-07-06 23:26:40] === Expiration Check Completed ===
[2025-07-06 23:26:40] Processed: 0 expired subscriptions
[2025-07-06 23:26:40] Disabled: 0 Jellyfin accounts
[2025-07-06 23:26:40] Errors: 0
[2025-07-06 23:26:40] === End of Expiration Check ===
[2025-07-07 00:26:42] === Expiration Check Started ===
[2025-07-07 00:26:42] Found 0 expired subscriptions
[2025-07-07 00:26:42] --- Checking for inconsistencies ---
[2025-07-07 00:26:43] === Expiration Check Completed ===
[2025-07-07 00:26:43] Processed: 0 expired subscriptions
[2025-07-07 00:26:43] Disabled: 0 Jellyfin accounts
[2025-07-07 00:26:43] Errors: 0
[2025-07-07 00:26:43] === End of Expiration Check ===
[2025-07-07 01:26:45] === Expiration Check Started ===
[2025-07-07 01:26:45] Found 0 expired subscriptions
[2025-07-07 01:26:45] --- Checking for inconsistencies ---
[2025-07-07 01:26:45] === Expiration Check Completed ===
[2025-07-07 01:26:45] Processed: 0 expired subscriptions
[2025-07-07 01:26:45] Disabled: 0 Jellyfin accounts
[2025-07-07 01:26:45] Errors: 0
[2025-07-07 01:26:45] === End of Expiration Check ===
[2025-07-07 02:26:46] === Expiration Check Started ===
[2025-07-07 02:26:46] Found 0 expired subscriptions
[2025-07-07 02:26:46] --- Checking for inconsistencies ---
[2025-07-07 02:26:46] === Expiration Check Completed ===
[2025-07-07 02:26:46] Processed: 0 expired subscriptions
[2025-07-07 02:26:46] Disabled: 0 Jellyfin accounts
[2025-07-07 02:26:46] Errors: 0
[2025-07-07 02:26:46] === End of Expiration Check ===
[2025-07-07 03:26:49] === Expiration Check Started ===
[2025-07-07 03:26:49] Found 0 expired subscriptions
[2025-07-07 03:26:49] --- Checking for inconsistencies ---
[2025-07-07 03:26:50] === Expiration Check Completed ===
[2025-07-07 03:26:50] Processed: 0 expired subscriptions
[2025-07-07 03:26:50] Disabled: 0 Jellyfin accounts
[2025-07-07 03:26:50] Errors: 0
[2025-07-07 03:26:50] === End of Expiration Check ===
[2025-07-07 04:26:53] === Expiration Check Started ===
[2025-07-07 04:26:53] Found 0 expired subscriptions
[2025-07-07 04:26:53] --- Checking for inconsistencies ---
[2025-07-07 04:26:54] === Expiration Check Completed ===
[2025-07-07 04:26:54] Processed: 0 expired subscriptions
[2025-07-07 04:26:54] Disabled: 0 Jellyfin accounts
[2025-07-07 04:26:54] Errors: 0
[2025-07-07 04:26:54] === End of Expiration Check ===
[2025-07-07 05:26:57] === Expiration Check Started ===
[2025-07-07 05:26:57] Found 0 expired subscriptions
[2025-07-07 05:26:57] --- Checking for inconsistencies ---
[2025-07-07 05:26:58] === Expiration Check Completed ===
[2025-07-07 05:26:58] Processed: 0 expired subscriptions
[2025-07-07 05:26:58] Disabled: 0 Jellyfin accounts
[2025-07-07 05:26:58] Errors: 0
[2025-07-07 05:26:58] === End of Expiration Check ===
[2025-07-07 06:27:01] === Expiration Check Started ===
[2025-07-07 06:27:01] Found 0 expired subscriptions
[2025-07-07 06:27:01] --- Checking for inconsistencies ---
[2025-07-07 06:27:02] === Expiration Check Completed ===
[2025-07-07 06:27:02] Processed: 0 expired subscriptions
[2025-07-07 06:27:02] Disabled: 0 Jellyfin accounts
[2025-07-07 06:27:02] Errors: 0
[2025-07-07 06:27:02] === End of Expiration Check ===
[2025-07-07 07:27:05] === Expiration Check Started ===
[2025-07-07 07:27:05] Found 0 expired subscriptions
[2025-07-07 07:27:05] --- Checking for inconsistencies ---
[2025-07-07 07:27:06] === Expiration Check Completed ===
[2025-07-07 07:27:06] Processed: 0 expired subscriptions
[2025-07-07 07:27:06] Disabled: 0 Jellyfin accounts
[2025-07-07 07:27:06] Errors: 0
[2025-07-07 07:27:06] === End of Expiration Check ===
[2025-07-07 09:56:32] === Expiration Check Started ===
[2025-07-07 09:56:32] Found 0 expired subscriptions
[2025-07-07 09:56:32] --- Checking for inconsistencies ---
[2025-07-07 09:56:33] === Expiration Check Completed ===
[2025-07-07 09:56:33] Processed: 0 expired subscriptions
[2025-07-07 09:56:33] Disabled: 0 Jellyfin accounts
[2025-07-07 09:56:33] Errors: 0
[2025-07-07 09:56:33] === End of Expiration Check ===
[2025-07-07 10:56:35] === Expiration Check Started ===
[2025-07-07 10:56:35] Found 0 expired subscriptions
[2025-07-07 10:56:35] --- Checking for inconsistencies ---
[2025-07-07 10:56:36] === Expiration Check Completed ===
[2025-07-07 10:56:36] Processed: 0 expired subscriptions
[2025-07-07 10:56:36] Disabled: 0 Jellyfin accounts
[2025-07-07 10:56:36] Errors: 0
[2025-07-07 10:56:36] === End of Expiration Check ===
[2025-07-07 11:56:38] === Expiration Check Started ===
[2025-07-07 11:56:38] Found 0 expired subscriptions
[2025-07-07 11:56:38] --- Checking for inconsistencies ---
[2025-07-07 11:56:39] === Expiration Check Completed ===
[2025-07-07 11:56:39] Processed: 0 expired subscriptions
[2025-07-07 11:56:39] Disabled: 0 Jellyfin accounts
[2025-07-07 11:56:39] Errors: 0
[2025-07-07 11:56:39] === End of Expiration Check ===
[2025-07-07 12:56:41] === Expiration Check Started ===
[2025-07-07 12:56:41] Found 0 expired subscriptions
[2025-07-07 12:56:41] --- Checking for inconsistencies ---
[2025-07-07 12:56:43] === Expiration Check Completed ===
[2025-07-07 12:56:43] Processed: 0 expired subscriptions
[2025-07-07 12:56:43] Disabled: 0 Jellyfin accounts
[2025-07-07 12:56:43] Errors: 0
[2025-07-07 12:56:43] === End of Expiration Check ===
[2025-07-07 13:11:20] === Expiration Check Started ===
[2025-07-07 13:11:20] Found 0 expired subscriptions
[2025-07-07 13:11:20] --- Checking for inconsistencies ---
[2025-07-07 13:11:21] === Expiration Check Completed ===
[2025-07-07 13:11:21] Processed: 0 expired subscriptions
[2025-07-07 13:11:21] Disabled: 0 Jellyfin accounts
[2025-07-07 13:11:21] Errors: 0
[2025-07-07 13:11:21] === End of Expiration Check ===
[2025-07-07 13:13:37] === Expiration Check Started ===
[2025-07-07 13:13:37] Found 0 expired subscriptions
[2025-07-07 13:13:37] --- Checking for inconsistencies ---
[2025-07-07 13:13:37] ❌ Error checking orphaned user test6: HTTP Error 530
[2025-07-07 13:13:37] ❌ Error checking orphaned user test7: HTTP Error 530
[2025-07-07 13:13:37] ❌ Error checking orphaned user test8: HTTP Error 530
[2025-07-07 13:13:37] ❌ Error checking orphaned user Hemawat: HTTP Error 530
[2025-07-07 13:13:37] === Expiration Check Completed ===
[2025-07-07 13:13:37] Processed: 0 expired subscriptions
[2025-07-07 13:13:37] Disabled: 0 Jellyfin accounts
[2025-07-07 13:13:37] Errors: 4
[2025-07-07 13:13:37] === End of Expiration Check ===
[2025-07-07 13:28:01] === Expiration Check Started ===
[2025-07-07 13:28:01] Found 0 expired subscriptions
[2025-07-07 13:28:01] --- Checking for inconsistencies ---
[2025-07-07 13:28:02] === Expiration Check Completed ===
[2025-07-07 13:28:02] Processed: 0 expired subscriptions
[2025-07-07 13:28:02] Disabled: 0 Jellyfin accounts
[2025-07-07 13:28:02] Errors: 0
[2025-07-07 13:28:02] === End of Expiration Check ===
[2025-07-07 13:30:47] === Expiration Check Started ===
[2025-07-07 13:30:47] Found 0 expired subscriptions
[2025-07-07 13:30:47] --- Checking for inconsistencies ---
[2025-07-07 13:30:48] === Expiration Check Completed ===
[2025-07-07 13:30:48] Processed: 0 expired subscriptions
[2025-07-07 13:30:48] Disabled: 0 Jellyfin accounts
[2025-07-07 13:30:48] Errors: 0
[2025-07-07 13:30:48] === End of Expiration Check ===
[2025-07-07 14:30:12] === Expiration Check Started ===
[2025-07-07 14:30:12] Found 0 expired subscriptions
[2025-07-07 14:30:12] --- Checking for inconsistencies ---
[2025-07-07 14:30:13] === Expiration Check Completed ===
[2025-07-07 14:30:13] Processed: 0 expired subscriptions
[2025-07-07 14:30:13] Disabled: 0 Jellyfin accounts
[2025-07-07 14:30:13] Errors: 0
[2025-07-07 14:30:13] === End of Expiration Check ===
[2025-07-07 15:48:41] === Expiration Check Started ===
[2025-07-07 15:48:41] Found 0 expired subscriptions
[2025-07-07 15:48:41] --- Checking for inconsistencies ---
[2025-07-07 15:48:45] === Expiration Check Completed ===
[2025-07-07 15:48:45] Processed: 0 expired subscriptions
[2025-07-07 15:48:45] Disabled: 0 Jellyfin accounts
[2025-07-07 15:48:45] Errors: 0
[2025-07-07 15:48:45] === End of Expiration Check ===
[2025-07-08 02:42:16] === Expiration Check Started ===
[2025-07-08 02:42:16] Found 0 expired subscriptions
[2025-07-08 02:42:16] --- Checking for inconsistencies ---
[2025-07-08 02:42:16] === Expiration Check Completed ===
[2025-07-08 02:42:16] Processed: 0 expired subscriptions
[2025-07-08 02:42:16] Disabled: 0 Jellyfin accounts
[2025-07-08 02:42:16] Errors: 0
[2025-07-08 02:42:16] === End of Expiration Check ===
[2025-07-08 02:47:16] === Expiration Check Started ===
[2025-07-08 02:47:16] Found 0 expired subscriptions
[2025-07-08 02:47:16] --- Checking for inconsistencies ---
[2025-07-08 02:47:17] === Expiration Check Completed ===
[2025-07-08 02:47:17] Processed: 0 expired subscriptions
[2025-07-08 02:47:17] Disabled: 0 Jellyfin accounts
[2025-07-08 02:47:17] Errors: 0
[2025-07-08 02:47:17] === End of Expiration Check ===
[2025-07-08 02:52:17] === Expiration Check Started ===
[2025-07-08 02:52:17] Found 0 expired subscriptions
[2025-07-08 02:52:17] --- Checking for inconsistencies ---
[2025-07-08 02:52:18] === Expiration Check Completed ===
[2025-07-08 02:52:18] Processed: 0 expired subscriptions
[2025-07-08 02:52:18] Disabled: 0 Jellyfin accounts
[2025-07-08 02:52:18] Errors: 0
[2025-07-08 02:52:18] === End of Expiration Check ===
[2025-07-08 02:57:19] === Expiration Check Started ===
[2025-07-08 02:57:19] Found 0 expired subscriptions
[2025-07-08 02:57:19] --- Checking for inconsistencies ---
[2025-07-08 02:57:20] === Expiration Check Completed ===
[2025-07-08 02:57:20] Processed: 0 expired subscriptions
[2025-07-08 02:57:20] Disabled: 0 Jellyfin accounts
[2025-07-08 02:57:20] Errors: 0
[2025-07-08 02:57:20] === End of Expiration Check ===
[2025-07-08 03:02:20] === Expiration Check Started ===
[2025-07-08 03:02:20] Found 0 expired subscriptions
[2025-07-08 03:02:20] --- Checking for inconsistencies ---
[2025-07-08 03:02:21] === Expiration Check Completed ===
[2025-07-08 03:02:21] Processed: 0 expired subscriptions
[2025-07-08 03:02:21] Disabled: 0 Jellyfin accounts
[2025-07-08 03:02:21] Errors: 0
[2025-07-08 03:02:21] === End of Expiration Check ===
[2025-07-08 03:07:21] === Expiration Check Started ===
[2025-07-08 03:07:21] Found 0 expired subscriptions
[2025-07-08 03:07:21] --- Checking for inconsistencies ---
[2025-07-08 03:07:22] === Expiration Check Completed ===
[2025-07-08 03:07:22] Processed: 0 expired subscriptions
[2025-07-08 03:07:22] Disabled: 0 Jellyfin accounts
[2025-07-08 03:07:22] Errors: 0
[2025-07-08 03:07:22] === End of Expiration Check ===
[2025-07-08 03:12:22] === Expiration Check Started ===
[2025-07-08 03:12:22] Found 0 expired subscriptions
[2025-07-08 03:12:22] --- Checking for inconsistencies ---
[2025-07-08 03:12:23] === Expiration Check Completed ===
[2025-07-08 03:12:23] Processed: 0 expired subscriptions
[2025-07-08 03:12:23] Disabled: 0 Jellyfin accounts
[2025-07-08 03:12:23] Errors: 0
[2025-07-08 03:12:23] === End of Expiration Check ===
[2025-07-08 03:17:23] === Expiration Check Started ===
[2025-07-08 03:17:23] Found 0 expired subscriptions
[2025-07-08 03:17:23] --- Checking for inconsistencies ---
[2025-07-08 03:17:24] === Expiration Check Completed ===
[2025-07-08 03:17:24] Processed: 0 expired subscriptions
[2025-07-08 03:17:24] Disabled: 0 Jellyfin accounts
[2025-07-08 03:17:24] Errors: 0
[2025-07-08 03:17:24] === End of Expiration Check ===
[2025-07-08 03:22:25] === Expiration Check Started ===
[2025-07-08 03:22:25] Found 0 expired subscriptions
[2025-07-08 03:22:25] --- Checking for inconsistencies ---
[2025-07-08 03:22:26] === Expiration Check Completed ===
[2025-07-08 03:22:26] Processed: 0 expired subscriptions
[2025-07-08 03:22:26] Disabled: 0 Jellyfin accounts
[2025-07-08 03:22:26] Errors: 0
[2025-07-08 03:22:26] === End of Expiration Check ===
[2025-07-08 03:27:26] === Expiration Check Started ===
[2025-07-08 03:27:26] Found 0 expired subscriptions
[2025-07-08 03:27:26] --- Checking for inconsistencies ---
[2025-07-08 03:27:27] === Expiration Check Completed ===
[2025-07-08 03:27:27] Processed: 0 expired subscriptions
[2025-07-08 03:27:27] Disabled: 0 Jellyfin accounts
[2025-07-08 03:27:27] Errors: 0
[2025-07-08 03:27:27] === End of Expiration Check ===
[2025-07-08 03:32:27] === Expiration Check Started ===
[2025-07-08 03:32:27] Found 0 expired subscriptions
[2025-07-08 03:32:27] --- Checking for inconsistencies ---
[2025-07-08 03:32:28] === Expiration Check Completed ===
[2025-07-08 03:32:28] Processed: 0 expired subscriptions
[2025-07-08 03:32:28] Disabled: 0 Jellyfin accounts
[2025-07-08 03:32:28] Errors: 0
[2025-07-08 03:32:28] === End of Expiration Check ===
[2025-07-08 03:37:29] === Expiration Check Started ===
[2025-07-08 03:37:29] Found 0 expired subscriptions
[2025-07-08 03:37:29] --- Checking for inconsistencies ---
[2025-07-08 03:37:30] === Expiration Check Completed ===
[2025-07-08 03:37:30] Processed: 0 expired subscriptions
[2025-07-08 03:37:30] Disabled: 0 Jellyfin accounts
[2025-07-08 03:37:30] Errors: 0
[2025-07-08 03:37:30] === End of Expiration Check ===
[2025-07-08 03:42:30] === Expiration Check Started ===
[2025-07-08 03:42:30] Found 0 expired subscriptions
[2025-07-08 03:42:30] --- Checking for inconsistencies ---
[2025-07-08 03:42:31] === Expiration Check Completed ===
[2025-07-08 03:42:31] Processed: 0 expired subscriptions
[2025-07-08 03:42:31] Disabled: 0 Jellyfin accounts
[2025-07-08 03:42:31] Errors: 0
[2025-07-08 03:42:31] === End of Expiration Check ===
[2025-07-08 03:47:31] === Expiration Check Started ===
[2025-07-08 03:47:31] Found 0 expired subscriptions
[2025-07-08 03:47:31] --- Checking for inconsistencies ---
[2025-07-08 03:47:32] === Expiration Check Completed ===
[2025-07-08 03:47:32] Processed: 0 expired subscriptions
[2025-07-08 03:47:32] Disabled: 0 Jellyfin accounts
[2025-07-08 03:47:32] Errors: 0
[2025-07-08 03:47:32] === End of Expiration Check ===
[2025-07-08 03:52:32] === Expiration Check Started ===
[2025-07-08 03:52:32] Found 0 expired subscriptions
[2025-07-08 03:52:32] --- Checking for inconsistencies ---
[2025-07-08 03:52:33] === Expiration Check Completed ===
[2025-07-08 03:52:33] Processed: 0 expired subscriptions
[2025-07-08 03:52:33] Disabled: 0 Jellyfin accounts
[2025-07-08 03:52:33] Errors: 0
[2025-07-08 03:52:33] === End of Expiration Check ===
[2025-07-08 03:57:33] === Expiration Check Started ===
[2025-07-08 03:57:33] Found 0 expired subscriptions
[2025-07-08 03:57:33] --- Checking for inconsistencies ---
[2025-07-08 03:57:34] ⚠️  Found enabled Jellyfin user without active subscription: test88
[2025-07-08 03:57:34] ✅ Disabled orphaned Jellyfin user: test88
[2025-07-08 03:57:34] === Expiration Check Completed ===
[2025-07-08 03:57:34] Processed: 0 expired subscriptions
[2025-07-08 03:57:34] Disabled: 1 Jellyfin accounts
[2025-07-08 03:57:34] Errors: 0
[2025-07-08 03:57:34] === End of Expiration Check ===
[2025-07-08 04:02:35] === Expiration Check Started ===
[2025-07-08 04:02:35] Found 0 expired subscriptions
[2025-07-08 04:02:35] --- Checking for inconsistencies ---
[2025-07-08 04:02:35] ⚠️  Found enabled Jellyfin user without active subscription: test88
[2025-07-08 04:02:35] ✅ Disabled orphaned Jellyfin user: test88
[2025-07-08 04:02:35] === Expiration Check Completed ===
[2025-07-08 04:02:35] Processed: 0 expired subscriptions
[2025-07-08 04:02:35] Disabled: 1 Jellyfin accounts
[2025-07-08 04:02:35] Errors: 0
[2025-07-08 04:02:35] === End of Expiration Check ===
[2025-07-08 04:07:36] === Expiration Check Started ===
[2025-07-08 04:07:36] Found 0 expired subscriptions
[2025-07-08 04:07:36] --- Checking for inconsistencies ---
[2025-07-08 04:07:37] === Expiration Check Completed ===
[2025-07-08 04:07:37] Processed: 0 expired subscriptions
[2025-07-08 04:07:37] Disabled: 0 Jellyfin accounts
[2025-07-08 04:07:37] Errors: 0
[2025-07-08 04:07:37] === End of Expiration Check ===
[2025-07-08 04:12:37] === Expiration Check Started ===
[2025-07-08 04:12:37] Found 0 expired subscriptions
[2025-07-08 04:12:37] --- Checking for inconsistencies ---
[2025-07-08 04:12:38] === Expiration Check Completed ===
[2025-07-08 04:12:38] Processed: 0 expired subscriptions
[2025-07-08 04:12:38] Disabled: 0 Jellyfin accounts
[2025-07-08 04:12:38] Errors: 0
[2025-07-08 04:12:38] === End of Expiration Check ===
[2025-07-08 04:17:38] === Expiration Check Started ===
[2025-07-08 04:17:38] Found 0 expired subscriptions
[2025-07-08 04:17:38] --- Checking for inconsistencies ---
[2025-07-08 04:17:39] === Expiration Check Completed ===
[2025-07-08 04:17:39] Processed: 0 expired subscriptions
[2025-07-08 04:17:39] Disabled: 0 Jellyfin accounts
[2025-07-08 04:17:39] Errors: 0
[2025-07-08 04:17:39] === End of Expiration Check ===
[2025-07-08 04:22:39] === Expiration Check Started ===
[2025-07-08 04:22:40] Found 0 expired subscriptions
[2025-07-08 04:22:40] --- Checking for inconsistencies ---
[2025-07-08 04:22:40] === Expiration Check Completed ===
[2025-07-08 04:22:40] Processed: 0 expired subscriptions
[2025-07-08 04:22:40] Disabled: 0 Jellyfin accounts
[2025-07-08 04:22:40] Errors: 0
[2025-07-08 04:22:40] === End of Expiration Check ===
[2025-07-08 04:27:41] === Expiration Check Started ===
[2025-07-08 04:27:41] Found 0 expired subscriptions
[2025-07-08 04:27:41] --- Checking for inconsistencies ---
[2025-07-08 04:27:42] === Expiration Check Completed ===
[2025-07-08 04:27:42] Processed: 0 expired subscriptions
[2025-07-08 04:27:42] Disabled: 0 Jellyfin accounts
[2025-07-08 04:27:42] Errors: 0
[2025-07-08 04:27:42] === End of Expiration Check ===
