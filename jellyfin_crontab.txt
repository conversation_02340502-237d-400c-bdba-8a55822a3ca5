# Jellyfin by <PERSON> - Production Cron Jobs
# For Linux server ************* / embyjames.xyz
# 
# Installation instructions:
# 1. Copy production_cron.py to /opt/jellyfin/
# 2. Make it executable: chmod +x /opt/jellyfin/production_cron.py
# 3. Install this crontab: crontab jellyfin_crontab.txt
# 4. Check logs: tail -f /var/log/jellyfin/payment_cron.log

# Payment processing every 5 seconds (using while loop script)
# This will be handled by the continuous script below

# Full system check every 5 minutes
*/5 * * * * /usr/bin/python3 /opt/jellyfin/production_cron.py full >> /var/log/jellyfin/cron_full.log 2>&1

# Health check every minute
* * * * * /usr/bin/python3 /opt/jellyfin/production_cron.py health >> /var/log/jellyfin/cron_health.log 2>&1

# Payment-only check every 30 seconds (using multiple cron entries)
* * * * * /usr/bin/python3 /opt/jellyfin/production_cron.py payment >> /var/log/jellyfin/cron_payment.log 2>&1
* * * * * sleep 30; /usr/bin/python3 /opt/jellyfin/production_cron.py payment >> /var/log/jellyfin/cron_payment.log 2>&1

# Daily log rotation at midnight
0 0 * * * /usr/bin/find /var/log/jellyfin -name "*.log" -mtime +7 -delete

# Weekly system cleanup (remove old temp files)
0 2 * * 0 /usr/bin/find /tmp -name "jellyfin_*" -mtime +1 -delete

# Monthly database optimization (if needed)
0 3 1 * * /usr/bin/mysql -u root -p'Wxmujwsofu@1234' jellyfin_registration -e "OPTIMIZE TABLE users, payment_transaction;" >> /var/log/jellyfin/db_optimize.log 2>&1
