<?php
// Test file to check if mod_rewrite is working
echo "<h1>Rewrite Test</h1>";
echo "<p>If you can see this page by accessing /test-rewrite (without .php), then mod_rewrite is working!</p>";
echo "<p>Current URL: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

// Check if mod_rewrite is loaded
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite is loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite is NOT loaded</p>";
    }
} else {
    echo "<p style='color: orange;'>? Cannot check if mod_rewrite is loaded (function not available)</p>";
}

// Show all server variables for debugging
echo "<h2>Server Variables:</h2>";
echo "<pre>";
foreach ($_SERVER as $key => $value) {
    if (strpos($key, 'HTTP_') === 0 || strpos($key, 'REQUEST_') === 0 || strpos($key, 'SCRIPT_') === 0) {
        echo "$key = $value\n";
    }
}
echo "</pre>";
?>
