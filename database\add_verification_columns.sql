-- Add verification columns to payment_transactions table
-- This adds verified_at and admin_notes columns for manual slip verification

USE jellyfin_registration;

-- Add verified_at column if it doesn't exist
ALTER TABLE payment_transactions 
ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP NULL AFTER updated_at;

-- Add admin_notes column if it doesn't exist  
ALTER TABLE payment_transactions 
ADD COLUMN IF NOT EXISTS admin_notes TEXT NULL AFTER verified_at;

-- Add index for verified_at for better performance
ALTER TABLE payment_transactions 
ADD INDEX IF NOT EXISTS idx_verified_at (verified_at);

-- Update status enum to include 'verified' if not already present
ALTER TABLE payment_transactions 
MODIFY COLUMN status ENUM('pending', 'pending_verification', 'completed', 'failed', 'cancelled', 'manual_review', 'verified') DEFAULT 'pending';

-- Show updated table structure
DESCRIBE payment_transactions;

-- Show current payment transactions with new columns
SELECT id, user_id, amount, status, payment_method, slip_image, 
       created_at, updated_at, verified_at, admin_notes
FROM payment_transactions 
ORDER BY created_at DESC 
LIMIT 5;
