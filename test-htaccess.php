<?php
// Test .htaccess and mod_rewrite functionality
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test .htaccess - Jellyfin by <PERSON></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        .info { color: #17a2b8; }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .result-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        .result-error {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #FFD700; text-align: center;">🔧 Test .htaccess & mod_rewrite</h1>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📋 Server Information</h2>
            <?php
            echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
            echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
            echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";
            
            // Check if mod_rewrite is loaded
            if (function_exists('apache_get_modules')) {
                $modules = apache_get_modules();
                if (in_array('mod_rewrite', $modules)) {
                    echo "<p class='success'>✅ mod_rewrite is loaded</p>";
                } else {
                    echo "<p class='error'>❌ mod_rewrite is NOT loaded</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ Cannot check mod_rewrite status (not Apache or function disabled)</p>";
            }
            
            // Check if .htaccess is readable
            if (file_exists('.htaccess') && is_readable('.htaccess')) {
                echo "<p class='success'>✅ .htaccess file exists and is readable</p>";
                $htaccess_size = filesize('.htaccess');
                echo "<p><strong>.htaccess size:</strong> " . number_format($htaccess_size) . " bytes</p>";
            } else {
                echo "<p class='error'>❌ .htaccess file not found or not readable</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🧪 Clean URL Tests</h2>
            <p style="color: #ccc;">คลิกลิงก์เหล่านี้เพื่อทดสอบ Clean URLs:</p>
            
            <h3 style="color: #FFD700;">Main Pages:</h3>
            <a href="/login" class="test-link" target="_blank">Login</a>
            <a href="/register" class="test-link" target="_blank">Register</a>
            <a href="/dashboard" class="test-link" target="_blank">Dashboard</a>
            <a href="/packages" class="test-link" target="_blank">Packages</a>
            <a href="/profile" class="test-link" target="_blank">Profile</a>
            <a href="/guide" class="test-link" target="_blank">Guide</a>
            <a href="/referral" class="test-link" target="_blank">Referral</a>
            
            <h3 style="color: #FFD700;">Admin Pages:</h3>
            <a href="/admin" class="test-link" target="_blank">Admin Console</a>
            <a href="/admin/users" class="test-link" target="_blank">Admin Users</a>
            <a href="/admin/packages" class="test-link" target="_blank">Admin Packages</a>
            <a href="/admin/transactions" class="test-link" target="_blank">Admin Transactions</a>
            
            <h3 style="color: #FFD700;">Direct PHP Files (for comparison):</h3>
            <a href="/login.php" class="test-link" target="_blank">login.php</a>
            <a href="/register.php" class="test-link" target="_blank">register.php</a>
            <a href="/dashboard.php" class="test-link" target="_blank">dashboard.php</a>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🔍 Debugging Information</h2>
            
            <h3 style="color: #FFD700;">Environment Variables:</h3>
            <div style="background: #000; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                <?php
                $env_vars = [
                    'REQUEST_METHOD',
                    'REQUEST_URI',
                    'SCRIPT_NAME',
                    'QUERY_STRING',
                    'HTTP_HOST',
                    'SERVER_NAME',
                    'DOCUMENT_ROOT',
                    'SCRIPT_FILENAME',
                    'PATH_INFO',
                    'REDIRECT_STATUS',
                    'REDIRECT_URL'
                ];
                
                foreach ($env_vars as $var) {
                    $value = $_SERVER[$var] ?? 'Not set';
                    echo "<strong style='color: #FFD700;'>$var:</strong> <span style='color: #ccc;'>$value</span><br>";
                }
                ?>
            </div>
            
            <h3 style="color: #FFD700;">File Existence Check:</h3>
            <?php
            $files_to_check = [
                'index.php',
                'login.php',
                'register.php',
                'dashboard.php',
                'packages.php',
                'profile.php',
                'guide.php',
                'admin/index.php',
                'admin/users.php',
                'admin/packages.php'
            ];
            
            foreach ($files_to_check as $file) {
                if (file_exists($file)) {
                    echo "<p class='success'>✅ $file exists</p>";
                } else {
                    echo "<p class='error'>❌ $file NOT found</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📝 Troubleshooting Steps</h2>
            
            <h3 style="color: #FFD700;">If Clean URLs don't work:</h3>
            <ol style="color: #ccc; line-height: 1.8;">
                <li><strong>Check Apache Configuration:</strong>
                    <ul>
                        <li>Ensure <code>mod_rewrite</code> is enabled</li>
                        <li>Check <code>AllowOverride All</code> in virtual host</li>
                        <li>Restart Apache after changes</li>
                    </ul>
                </li>
                <li><strong>Check File Permissions:</strong>
                    <ul>
                        <li>.htaccess should be 644</li>
                        <li>PHP files should be 644</li>
                        <li>Directories should be 755</li>
                    </ul>
                </li>
                <li><strong>Check .htaccess Location:</strong>
                    <ul>
                        <li>Must be in document root</li>
                        <li>Must be readable by web server</li>
                    </ul>
                </li>
                <li><strong>Enable Apache Error Log:</strong>
                    <ul>
                        <li>Check <code>/var/log/apache2/error.log</code></li>
                        <li>Look for rewrite errors</li>
                    </ul>
                </li>
            </ol>
            
            <h3 style="color: #FFD700;">Apache Commands (run as root):</h3>
            <div style="background: #000; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                <span style="color: #4CAF50;"># Enable mod_rewrite</span><br>
                sudo a2enmod rewrite<br><br>
                
                <span style="color: #4CAF50;"># Restart Apache</span><br>
                sudo systemctl restart apache2<br><br>
                
                <span style="color: #4CAF50;"># Check Apache status</span><br>
                sudo systemctl status apache2<br><br>
                
                <span style="color: #4CAF50;"># Check error logs</span><br>
                sudo tail -f /var/log/apache2/error.log
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                🏠 กลับหน้าแรก
            </a>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds for testing
        // setTimeout(() => location.reload(), 30000);
        
        console.log('Test .htaccess page loaded');
        console.log('Current URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
    </script>
</body>
</html>
