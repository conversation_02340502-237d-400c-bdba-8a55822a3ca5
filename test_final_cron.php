<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test Final Cron Job</h2>";

try {
    echo "<h3>🔍 Step 1: Check Manual Payments</h3>";
    
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at ASC
        LIMIT 5
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    echo "<p>Found " . count($payments) . " pending manual payments</p>";
    
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Age</th><th>Auto-Verify</th></tr>";
        
        foreach ($payments as $payment) {
            $age = time() - strtotime($payment['created_at']);
            $ageHours = round($age / 3600, 1);
            $isSmallAmount = $payment['amount'] <= 100;
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>{$ageHours}h</td>";
            echo "<td>" . ($isSmallAmount ? '✅ Yes' : '❌ No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🔧 Step 2: Test Auto-Verification Logic</h3>";
    
    if ($payments) {
        foreach ($payments as $payment) {
            echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 10px;'>";
            echo "<h4>Payment ID: {$payment['id']} - {$payment['username']} - {$payment['amount']} THB</h4>";
            
            // Check slip file
            $slipImage = $payment['slip_image'];
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $slipImage = 'uploads/slips/' . $slipImage;
            }
            
            $fileExists = file_exists($slipImage);
            echo "<p>File exists: " . ($fileExists ? '✅' : '❌') . " ({$slipImage})</p>";
            
            if ($fileExists) {
                $fileSize = filesize($slipImage);
                $isValidSize = $fileSize > 10000 && $fileSize < 10000000;
                echo "<p>Valid size: " . ($isValidSize ? '✅' : '❌') . " ({$fileSize} bytes)</p>";
                
                // Auto-verification criteria (NEW LOGIC - no time restriction)
                $autoVerify = false;
                
                if ($payment['amount'] <= 100) {
                    $autoVerify = true;
                    echo "<p>✅ <strong>Auto-verify: Small amount (≤100 THB)</strong></p>";
                } else {
                    // Check user history
                    $historyStmt = $pdo->prepare("
                        SELECT COUNT(*) as verified_count FROM payment_transactions
                        WHERE user_id = ? AND status = 'completed'
                    ");
                    $historyStmt->execute([$payment['user_id']]);
                    $verifiedCount = $historyStmt->fetch()['verified_count'];
                    
                    echo "<p>Previous completed payments: {$verifiedCount}</p>";
                    
                    if ($verifiedCount >= 2) {
                        $autoVerify = true;
                        echo "<p>✅ <strong>Auto-verify: Trusted user (≥2 previous payments)</strong></p>";
                    }
                }
                
                if (!$autoVerify) {
                    echo "<p>❌ Manual review required</p>";
                }
                
                // Test verification
                if ($autoVerify && isset($_POST['execute_verification'])) {
                    echo "<p><strong>🔄 Executing verification...</strong></p>";
                    
                    try {
                        $pdo->beginTransaction();
                        
                        $updateStmt = $pdo->prepare("
                            UPDATE payment_transactions
                            SET status = 'completed',
                                paid_at = NOW()
                            WHERE id = ?
                        ");
                        $result = $updateStmt->execute([$payment['id']]);
                        
                        if ($result && $updateStmt->rowCount() > 0) {
                            echo "<p>✅ <strong>Payment verified successfully!</strong></p>";
                            
                            // Check updated record
                            $checkStmt = $pdo->prepare("SELECT status, paid_at FROM payment_transactions WHERE id = ?");
                            $checkStmt->execute([$payment['id']]);
                            $updated = $checkStmt->fetch();
                            echo "<p>New status: {$updated['status']}, paid_at: {$updated['paid_at']}</p>";
                            
                        } else {
                            echo "<p>❌ Failed to update payment</p>";
                        }
                        
                        $pdo->commit();
                        
                    } catch (Exception $e) {
                        $pdo->rollback();
                        echo "<p>❌ Error: " . $e->getMessage() . "</p>";
                    }
                }
            }
            
            echo "</div>";
        }
        
        if (!isset($_POST['execute_verification'])) {
            echo "<form method='POST'>";
            echo "<input type='hidden' name='execute_verification' value='1'>";
            echo "<button type='submit' style='background: green; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px;'>🚀 Execute Auto-Verification</button>";
            echo "</form>";
            echo "<p><strong>⚠️ This will actually update the database!</strong></p>";
        }
    }
    
    echo "<h3>📊 Step 3: Run Actual Cron Job</h3>";
    
    if (isset($_POST['run_cron'])) {
        echo "<h4>🔄 Running cron job...</h4>";
        
        $output = shell_exec('cd /var/www/html && php cron/linux_manual_check.php 2>&1');
        
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
        
        // Show recent log entries
        $logFile = '/var/log/jellyfin/manual_cron.log';
        if (file_exists($logFile)) {
            $lines = file($logFile);
            $recentLines = array_slice($lines, -10);
            
            echo "<h4>Recent Log Entries:</h4>";
            echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
            echo htmlspecialchars(implode('', $recentLines));
            echo "</pre>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='run_cron' value='1'>";
        echo "<button type='submit' style='background: blue; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px;'>🔄 Run Cron Job</button>";
        echo "</form>";
    }
    
    echo "<h3>📈 Step 4: Statistics</h3>";
    
    $stats = [];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'pending' AND payment_method = 'manual'");
    $stmt->execute();
    $stats['pending_manual'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'completed' AND paid_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $stmt->execute();
    $stats['completed_hour'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payment_transactions WHERE status = 'completed' AND paid_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stmt->execute();
    $stats['completed_day'] = $stmt->fetchColumn();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Pending Manual</td><td style='background: yellow;'>{$stats['pending_manual']}</td></tr>";
    echo "<tr><td>Completed (Last Hour)</td><td style='background: lightgreen;'>{$stats['completed_hour']}</td></tr>";
    echo "<tr><td>Completed (Last 24h)</td><td style='background: lightblue;'>{$stats['completed_day']}</td></tr>";
    echo "</table>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/check_cron_duplicate.php'>🔍 Check Cron Duplicates</a></p>";
?>
