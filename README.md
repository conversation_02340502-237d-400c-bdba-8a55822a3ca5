# Jellyfin Registration System

A comprehensive web-based registration and management system for Jellyfin media server with a film noir aesthetic design.

## Features

### 🎬 Film Noir Design
- **Dramatic CSS animations** with film grain overlay
- **Interactive hover effects** for buttons and elements
- **Fade-in animations** triggered by scrolling
- **Typewriter effects** and spotlight animations
- **Black and white aesthetic** with strong contrasts

### 👥 User Management
- User registration with email validation
- Secure login with "Remember Me" functionality
- Profile management and password changes
- Admin panel for user administration
- Activity logging and tracking

### 💰 Package & Payment System
- **Package Selection**: 15-day (60 THB) and 30-day (100 THB) packages
- **PromptPay Integration**: Automatic QR code generation with random decimal amounts
- **Payment Workflow**: Users are disabled in Jellyfin until payment is confirmed
- **Admin Approval**: <PERSON><PERSON> can approve/reject payments and enable user accounts
- **Subscription Management**: Track active, pending, and expired subscriptions

### 🖥️ Server Control GUI
- **Start/Stop/Restart** Jellyfin server controls
- Real-time server status monitoring
- Server logs and activity tracking
- Cross-platform support (Windows/Linux)

### 🔗 Jellyfin API Integration
- Automatic Jellyfin user creation
- User policy management
- Server status checking
- User synchronization between systems

### 🛡️ Security Features
- Password hashing with <PERSON><PERSON>'s password_hash()
- SQL injection prevention with prepared statements
- Session management and CSRF protection
- Input sanitization and validation

## Requirements

- **PHP 7.4+** with extensions:
  - PDO and PDO_MySQL
  - cURL
  - JSON
  - OpenSSL
- **MySQL 5.7+** or **MariaDB 10.2+**
- **Web server** (Apache, Nginx, or similar)
- **Jellyfin server** (local or remote)

## Installation

### 1. Download and Setup
```bash
# Clone or download the files to your web server directory
# For example, in Laragon: c:\laragon\www\jellyfin-registration
```

### 2. Database Setup
1. Create a MySQL database for the application
2. Run the installation script by visiting: `http://your-domain/install.php`
3. Fill in the configuration form:
   - Database connection details
   - Jellyfin server URL and API key (optional)
   - Admin account credentials

### 3. Jellyfin API Key (Optional but Recommended)
1. Log into your Jellyfin admin panel
2. Go to Dashboard → API Keys
3. Create a new API key
4. Add the key to your installation or update it later in the admin panel

### 4. Server Control Setup (Optional)
For server control functionality to work, ensure your web server has appropriate permissions:

**Windows:**
- Run web server as administrator, or
- Grant permissions to control Windows services

**Linux:**
- Add web server user to appropriate groups
- Configure sudo permissions for systemctl commands

## Usage

### User Registration & Package Purchase
1. Visit the registration page
2. Fill in user details and create account
3. **Select a package** (15-day or 30-day)
4. **Pay via PromptPay** using the generated QR code
5. **Confirm payment** and wait for admin approval
6. Account is enabled in Jellyfin after payment confirmation

**Note**: New users are disabled in Jellyfin until they purchase and pay for a package.

### Admin Functions
- **User Management**: View, activate/deactivate, promote/demote users
- **Server Control**: Start, stop, restart Jellyfin server
- **Package Management**: Approve/reject payments, manage subscriptions
- **Payment Approval**: Review and approve PromptPay payments
- **System Monitoring**: View logs and user activity

### Film Noir CSS Animations

The system includes several CSS animation classes you can use:

```css
/* Fade-in animations */
.fade-in-up        /* Fade in from bottom */
.fade-in-left      /* Fade in from left */
.fade-in-right     /* Fade in from right */
.fade-in-scroll    /* Fade in when scrolling into view */

/* Delays for staggered animations */
.delay-1, .delay-2, .delay-3

/* Special effects */
.noir-button       /* Film noir button with hover effects */
.play-trailer-btn  /* Special play button with dramatic hover */
.dramatic-text     /* Text with shadow effects */
.typewriter        /* Typewriter animation */
.spotlight         /* Spotlight hover effect */
.venetian-blind    /* Venetian blind reveal effect */
```

### Button Hover Effects
The system includes several button styles with dramatic hover effects:
- **Noir buttons** with sliding light effects
- **Play trailer buttons** with scaling and glow
- **Server control buttons** with color transitions

### Film Grain Overlay
An animated film grain overlay provides authentic film noir atmosphere:
- Subtle grain animation
- Blend mode effects
- Performance optimized

## Configuration

### Database Configuration
Edit `config/database.php` to modify database settings.

### Jellyfin Settings
Update Jellyfin server URL and API key in the admin panel or database:
```sql
UPDATE server_config SET config_value = 'your-api-key' WHERE config_key = 'jellyfin_api_key';
UPDATE server_config SET config_value = 'http://your-jellyfin-url:8096' WHERE config_key = 'jellyfin_server_url';
```

### User Policies
Default user policies can be modified in the `server_config` table under the `default_user_policy` key.

## File Structure

```
jellyfin-registration/
├── admin/                  # Admin panel files
│   ├── server-control.php  # Server management GUI
│   └── users.php          # User management
├── assets/                # Static assets
│   ├── css/
│   │   ├── style.css      # Main stylesheet
│   │   └── film-noir.css  # Film noir animations
│   └── js/
│       └── main.js        # JavaScript functionality
├── config/                # Configuration files
│   └── database.php       # Database configuration
├── database/              # Database files
│   └── schema.sql         # Database schema
├── includes/              # PHP includes
│   ├── functions.php      # Common functions
│   └── JellyfinAPI.php    # Jellyfin API integration
├── index.php              # Homepage
├── register.php           # User registration
├── login.php              # User login
├── dashboard.php          # User dashboard
├── profile.php            # Profile management
├── logout.php             # Logout handler
├── install.php            # Installation script
└── README.md              # This file
```

## API Integration

The system integrates with Jellyfin's REST API for:
- User creation and management
- Server status monitoring
- Authentication and authorization
- User policy enforcement

## Security Considerations

- Change default admin credentials after installation
- Use HTTPS in production
- Regularly update the system and dependencies
- Monitor server logs for suspicious activity
- Implement proper firewall rules

## Troubleshooting

### Common Issues

**Server control not working:**
- Check web server permissions
- Verify Jellyfin service names
- Review server logs for errors

**Jellyfin API errors:**
- Verify API key is correct
- Check Jellyfin server URL
- Ensure Jellyfin server is accessible

**Database connection errors:**
- Verify database credentials
- Check database server status
- Review PHP error logs

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the system.

## License

This project is open source. Please check individual components for their respective licenses.

## Support

For support and questions:
1. Check the troubleshooting section
2. Review server and PHP error logs
3. Verify Jellyfin server connectivity
4. Check database connectivity and permissions
