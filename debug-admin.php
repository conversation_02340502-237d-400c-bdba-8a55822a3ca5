<?php
// Debug admin URLs and file structure
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Admin URLs - Je<PERSON>fin by <PERSON></title>
    <style>
        body { 
            background: #0a0a0a; 
            color: #fff; 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            margin: 0; 
        }
        .debug-section { 
            background: #1a1a1a; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
            border: 1px solid #333; 
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        pre { 
            background: #000; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 8px;
            border: 1px solid #555;
            text-align: left;
        }
        th {
            background: #333;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <h1 style="color: #FFD700; text-align: center;">🔍 Debug Admin URLs</h1>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">1. Admin Directory Structure</h2>
        <?php
        $adminPath = __DIR__ . '/admin/';
        echo "<p><strong>Admin directory:</strong> " . $adminPath . "</p>";
        
        if (is_dir($adminPath)) {
            echo "<p class='success'>✅ Admin directory exists</p>";
            
            $files = scandir($adminPath);
            echo "<table>";
            echo "<tr><th>File</th><th>Size</th><th>Readable</th><th>URL Test</th></tr>";
            foreach ($files as $file) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                    $fullPath = $adminPath . $file;
                    $size = filesize($fullPath);
                    $readable = is_readable($fullPath) ? '✅' : '❌';
                    $fileName = pathinfo($file, PATHINFO_FILENAME);
                    
                    echo "<tr>";
                    echo "<td>$file</td>";
                    echo "<td>" . number_format($size) . " bytes</td>";
                    echo "<td>$readable</td>";
                    echo "<td><a href='/admin/$fileName' target='_blank' style='color: #FFD700;'>/admin/$fileName</a></td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ Admin directory does not exist</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">2. .htaccess Rules Check</h2>
        <?php
        $htaccessPath = __DIR__ . '/.htaccess';
        if (file_exists($htaccessPath)) {
            echo "<p class='success'>✅ .htaccess file exists</p>";
            
            $content = file_get_contents($htaccessPath);
            $adminRules = [];
            $lines = explode("\n", $content);
            
            foreach ($lines as $lineNum => $line) {
                if (strpos($line, 'admin') !== false && strpos($line, 'RewriteRule') !== false) {
                    $adminRules[] = ($lineNum + 1) . ": " . trim($line);
                }
            }
            
            if (!empty($adminRules)) {
                echo "<p class='success'>✅ Found admin rewrite rules:</p>";
                echo "<pre>" . implode("\n", $adminRules) . "</pre>";
            } else {
                echo "<p class='error'>❌ No admin rewrite rules found</p>";
            }
        } else {
            echo "<p class='error'>❌ .htaccess file not found</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">3. Server Configuration</h2>
        <p><strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
        <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
        <p><strong>Script Path:</strong> <?php echo __FILE__; ?></p>
        <p><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        
        <?php
        // Check if mod_rewrite is enabled
        if (function_exists('apache_get_modules')) {
            $modules = apache_get_modules();
            if (in_array('mod_rewrite', $modules)) {
                echo "<p class='success'>✅ mod_rewrite is enabled</p>";
            } else {
                echo "<p class='error'>❌ mod_rewrite is not enabled</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Cannot check Apache modules</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">4. URL Mapping Test</h2>
        <p>ทดสอบการแมป URL ไปยังไฟล์จริง:</p>
        
        <table>
            <tr><th>Clean URL</th><th>Target File</th><th>File Exists</th><th>Expected Result</th></tr>
            <tr>
                <td>/admin</td>
                <td>admin/index.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/index.php') ? '✅' : '❌'; ?></td>
                <td>Admin Dashboard</td>
            </tr>
            <tr>
                <td>/admin/server</td>
                <td>admin/server-control.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/server-control.php') ? '✅' : '❌'; ?></td>
                <td>Server Control</td>
            </tr>
            <tr>
                <td>/admin/server-control</td>
                <td>admin/server-control.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/server-control.php') ? '✅' : '❌'; ?></td>
                <td>Server Control</td>
            </tr>
            <tr>
                <td>/admin/users</td>
                <td>admin/users.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/users.php') ? '✅' : '❌'; ?></td>
                <td>User Management</td>
            </tr>
            <tr>
                <td>/admin/packages</td>
                <td>admin/packages.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/packages.php') ? '✅' : '❌'; ?></td>
                <td>Package List</td>
            </tr>
            <tr>
                <td>/admin/manage-packages</td>
                <td>admin/manage-packages.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/manage-packages.php') ? '✅' : '❌'; ?></td>
                <td>Package Management</td>
            </tr>
            <tr>
                <td>/admin/add-package</td>
                <td>admin/add-package.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/add-package.php') ? '✅' : '❌'; ?></td>
                <td>Add Package</td>
            </tr>
            <tr>
                <td>/admin/transactions</td>
                <td>admin/transaction-check.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/transaction-check.php') ? '✅' : '❌'; ?></td>
                <td>Transaction Check</td>
            </tr>
            <tr>
                <td>/admin/transaction-check</td>
                <td>admin/transaction-check.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/transaction-check.php') ? '✅' : '❌'; ?></td>
                <td>Transaction Check</td>
            </tr>
            <tr>
                <td>/admin/paynoi</td>
                <td>admin/paynoi-transactions.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/paynoi-transactions.php') ? '✅' : '❌'; ?></td>
                <td>PayNoi Transactions</td>
            </tr>
            <tr>
                <td>/admin/paynoi-transactions</td>
                <td>admin/paynoi-transactions.php</td>
                <td><?php echo file_exists(__DIR__ . '/admin/paynoi-transactions.php') ? '✅' : '❌'; ?></td>
                <td>PayNoi Transactions</td>
            </tr>
        </table>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">5. Direct File Access Test</h2>
        <p>ทดสอบการเข้าถึงไฟล์โดยตรง (ควรทำงานแม้ Clean URLs ไม่ทำงาน):</p>
        
        <ul>
            <li><a href="admin/index.php" target="_blank" style="color: #FFD700;">admin/index.php</a></li>
            <li><a href="admin/server-control.php" target="_blank" style="color: #FFD700;">admin/server-control.php</a></li>
            <li><a href="admin/users.php" target="_blank" style="color: #FFD700;">admin/users.php</a></li>
            <li><a href="admin/packages.php" target="_blank" style="color: #FFD700;">admin/packages.php</a></li>
            <li><a href="admin/manage-packages.php" target="_blank" style="color: #FFD700;">admin/manage-packages.php</a></li>
            <li><a href="admin/add-package.php" target="_blank" style="color: #FFD700;">admin/add-package.php</a></li>
            <li><a href="admin/transaction-check.php" target="_blank" style="color: #FFD700;">admin/transaction-check.php</a></li>
            <li><a href="admin/paynoi-transactions.php" target="_blank" style="color: #FFD700;">admin/paynoi-transactions.php</a></li>
        </ul>
    </div>
    
    <div style="text-align: center; margin-top: 40px;">
        <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px; margin-right: 10px;">
            ← กลับไปหน้าแรก
        </a>
        <a href="/test-admin-urls.html" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
            ทดสอบ URLs
        </a>
    </div>
</body>
</html>
