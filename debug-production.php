<?php
// Debug production issues
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Production - Jellyfin by <PERSON></title>
    <style>
        body { 
            background: #0a0a0a; 
            color: #fff; 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            margin: 0; 
        }
        .debug-section { 
            background: #1a1a1a; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px; 
            border: 1px solid #333; 
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        pre { 
            background: #000; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px;
        }
        .test-btn {
            background: #FFD700;
            color: #000;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1 style="color: #FFD700; text-align: center;">🚨 Debug Production Issues</h1>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">1. Server Environment</h2>
        <p><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></p>
        <p><strong>Current Script:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?></p>
        <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?></p>
        <p><strong>HTTP Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Unknown'; ?></p>
        
        <?php
        // Check if mod_rewrite is enabled
        if (function_exists('apache_get_modules')) {
            $modules = apache_get_modules();
            if (in_array('mod_rewrite', $modules)) {
                echo "<p class='success'>✅ mod_rewrite is enabled</p>";
            } else {
                echo "<p class='error'>❌ mod_rewrite is NOT enabled</p>";
            }
            
            // Show other relevant modules
            $relevant_modules = ['mod_headers', 'mod_expires', 'mod_deflate'];
            foreach ($relevant_modules as $module) {
                if (in_array($module, $modules)) {
                    echo "<p class='success'>✅ $module is enabled</p>";
                } else {
                    echo "<p class='warning'>⚠️ $module is not enabled</p>";
                }
            }
        } else {
            echo "<p class='warning'>⚠️ Cannot check Apache modules (function not available)</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">2. .htaccess File Check</h2>
        <?php
        $htaccessPath = __DIR__ . '/.htaccess';
        if (file_exists($htaccessPath)) {
            echo "<p class='success'>✅ .htaccess file exists</p>";
            echo "<p><strong>File size:</strong> " . filesize($htaccessPath) . " bytes</p>";
            echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($htaccessPath)) . "</p>";
            echo "<p><strong>Readable:</strong> " . (is_readable($htaccessPath) ? 'Yes' : 'No') . "</p>";
            
            $content = file_get_contents($htaccessPath);
            echo "<p><strong>Content preview (first 500 chars):</strong></p>";
            echo "<pre>" . htmlspecialchars(substr($content, 0, 500)) . "...</pre>";
            
            // Count rewrite rules
            $rewriteRules = substr_count($content, 'RewriteRule');
            echo "<p><strong>Number of RewriteRule entries:</strong> $rewriteRules</p>";
            
        } else {
            echo "<p class='error'>❌ .htaccess file does not exist</p>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">3. File Structure Check</h2>
        <?php
        $files_to_check = [
            'index.php',
            'login.php',
            'register.php',
            'dashboard.php',
            'packages.php',
            'profile.php',
            'guide.php',
            'payment.php',
            'referral.php',
            'logout.php',
            'admin/index.php',
            'admin/server-control.php',
            'admin/users.php',
            'admin/packages.php',
            'test-simple.php'
        ];
        
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #333;'><th style='padding: 8px; border: 1px solid #555;'>File</th><th style='padding: 8px; border: 1px solid #555;'>Exists</th><th style='padding: 8px; border: 1px solid #555;'>Size</th></tr>";
        
        foreach ($files_to_check as $file) {
            $fullPath = __DIR__ . '/' . $file;
            $exists = file_exists($fullPath);
            $size = $exists ? filesize($fullPath) : 0;
            
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #555;'>$file</td>";
            echo "<td style='padding: 8px; border: 1px solid #555;'>" . ($exists ? '✅' : '❌') . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #555;'>" . ($exists ? number_format($size) . ' bytes' : '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">4. URL Testing</h2>
        <p>Click the buttons below to test different URLs:</p>
        
        <button class="test-btn" onclick="testUrl('/test-simple')">Test /test-simple</button>
        <button class="test-btn" onclick="testUrl('/home')">Test /home</button>
        <button class="test-btn" onclick="testUrl('/login')">Test /login</button>
        <button class="test-btn" onclick="testUrl('/dashboard')">Test /dashboard</button>
        <button class="test-btn" onclick="testUrl('/admin')">Test /admin</button>
        
        <div id="testResults" style="margin-top: 15px; padding: 10px; background: #000; border-radius: 5px; min-height: 50px;">
            <p>Test results will appear here...</p>
        </div>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">5. Direct File Access</h2>
        <p>These links should work regardless of .htaccess:</p>
        <ul>
            <li><a href="index.php" target="_blank" style="color: #FFD700;">index.php</a></li>
            <li><a href="login.php" target="_blank" style="color: #FFD700;">login.php</a></li>
            <li><a href="dashboard.php" target="_blank" style="color: #FFD700;">dashboard.php</a></li>
            <li><a href="admin/index.php" target="_blank" style="color: #FFD700;">admin/index.php</a></li>
            <li><a href="test-simple.php" target="_blank" style="color: #FFD700;">test-simple.php</a></li>
        </ul>
    </div>
    
    <div class="debug-section">
        <h2 style="color: #FFD700;">6. Troubleshooting Steps</h2>
        <ol style="color: #ccc; line-height: 1.8;">
            <li><strong>Check mod_rewrite:</strong> Make sure mod_rewrite is enabled in Apache</li>
            <li><strong>Check AllowOverride:</strong> Must be set to "All" in Apache virtual host configuration</li>
            <li><strong>Check .htaccess permissions:</strong> File should be readable by web server</li>
            <li><strong>Check Apache error logs:</strong> Look for any .htaccess syntax errors</li>
            <li><strong>Test simple rewrite:</strong> Try accessing /test-simple to see if basic rewrite works</li>
            <li><strong>Check file paths:</strong> Make sure all target PHP files exist</li>
        </ol>
        
        <h3 style="color: #FFD700;">Common Solutions:</h3>
        <ul style="color: #ccc; line-height: 1.6;">
            <li><strong>Enable mod_rewrite:</strong> <code>sudo a2enmod rewrite && sudo systemctl restart apache2</code></li>
            <li><strong>Update Apache config:</strong> Add <code>AllowOverride All</code> to your virtual host</li>
            <li><strong>Check .htaccess syntax:</strong> Use <code>apache2ctl configtest</code></li>
        </ul>
    </div>
    
    <div style="text-align: center; margin-top: 40px;">
        <a href="index.php" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
            ← กลับไปหน้าแรก (Direct)
        </a>
    </div>
    
    <script>
        function testUrl(url) {
            const results = document.getElementById('testResults');
            results.innerHTML = `<p style="color: #ffc107;">Testing ${url}...</p>`;
            
            fetch(url, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        results.innerHTML += `<p style="color: #4CAF50;">✅ ${url} - Status: ${response.status} (OK)</p>`;
                    } else {
                        results.innerHTML += `<p style="color: #ff6b6b;">❌ ${url} - Status: ${response.status} (Error)</p>`;
                    }
                })
                .catch(error => {
                    results.innerHTML += `<p style="color: #ff6b6b;">❌ ${url} - Failed to connect: ${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
