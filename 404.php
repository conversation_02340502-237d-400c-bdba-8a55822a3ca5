<?php
http_response_code(404);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>หน้าไม่พบ - Jellyfin by <PERSON></title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        .error-content {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.05);
            padding: 3rem;
            border-radius: 12px;
            border: 1px solid #333;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #ffc107;
            text-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
            margin-bottom: 1rem;
            line-height: 1;
        }
        .error-title {
            font-size: 2rem;
            color: #fff;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.1rem;
            color: #ccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn-home {
            background: linear-gradient(45deg, #007cba, #0056b3);
            color: #fff;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.3);
        }
        .btn-back {
            background: transparent;
            color: #ccc;
            padding: 12px 30px;
            text-decoration: none;
            border: 2px solid #666;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-back:hover {
            background: #666;
            color: #fff;
        }
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            .error-title {
                font-size: 1.5rem;
            }
            .error-content {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <div class="error-container">
        <div class="error-content fade-in-up">
            <div class="error-code">404</div>
            <h1 class="error-title">หน้าไม่พบ</h1>
            <p class="error-message">
                ขออภัย หน้าที่คุณกำลังมองหาไม่มีอยู่ในระบบ<br>
                อาจเป็นเพราะลิงก์ผิดพลาด หรือหน้านี้ถูกย้ายไปแล้ว
            </p>
            <div class="error-actions">
                <a href="/" class="btn-home">กลับหน้าหลัก</a>
                <a href="javascript:history.back()" class="btn-back">ย้อนกลับ</a>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const errorCode = document.querySelector('.error-code');
            
            // Add glitch effect on hover
            errorCode.addEventListener('mouseenter', function() {
                this.style.animation = 'glitch 0.3s ease-in-out';
            });
            
            errorCode.addEventListener('animationend', function() {
                this.style.animation = '';
            });
        });
        
        // Add glitch animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes glitch {
                0% { transform: translate(0); }
                20% { transform: translate(-2px, 2px); }
                40% { transform: translate(-2px, -2px); }
                60% { transform: translate(2px, 2px); }
                80% { transform: translate(2px, -2px); }
                100% { transform: translate(0); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
