<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown - Jellyfin by <PERSON></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        .test-content {
            padding: 100px 20px 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .test-info {
            color: #FFD700;
            margin-bottom: 15px;
        }
        .test-note {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        .overflow-test {
            height: 200px;
            overflow: hidden;
            background: rgba(255, 0, 0, 0.1);
            border: 2px dashed #ff6b6b;
            margin: 20px 0;
            padding: 20px;
            position: relative;
        }
        .overflow-test::before {
            content: 'Overflow: Hidden Container';
            position: absolute;
            top: 10px;
            left: 10px;
            color: #ff6b6b;
            font-size: 0.8rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <!-- Test Header with Dropdowns -->
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                    🎬
                </span>
                <span class="logo-text">
                    Jellyfin by James
                    <small class="cinema-subtitle">Cinema</small>
                </span>
            </h1>
            <nav class="main-nav">
                <a href="/" class="nav-link">หน้าแรก</a>
                <a href="/packages" class="nav-link">แพ็คเกจ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                
                <!-- Admin Dropdown Test -->
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="#" class="dropdown-item">ผู้ใช้</a>
                        <a href="#" class="dropdown-item">แพ็คเกจ</a>
                        <a href="#" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="#" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                        <a href="#" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="#" class="dropdown-item">PayNoi</a>
                    </div>
                </div>
                
                <!-- Profile Dropdown Test -->
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 ทดสอบผู้ใช้</a>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">โปรไฟล์</a>
                        <a href="#" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="#" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <div class="test-content">
        <h1 style="color: #FFD700; text-align: center; margin-bottom: 30px;">🧪 Dropdown Test Page</h1>
        
        <div class="test-section">
            <h3 class="test-info">📋 การทดสอบ Dropdown</h3>
            <div class="test-note">
                <p><strong>วิธีทดสอบ:</strong></p>
                <ol>
                    <li>คลิกที่ "⚙️ Admin Console" ใน navbar ด้านบน</li>
                    <li>คลิกที่ "👤 ทดสอบผู้ใช้" ใน navbar ด้านบน</li>
                    <li>ตรวจสอบว่า dropdown แสดงออกมานอก navbar ได้หรือไม่</li>
                    <li>ลองเปลี่ยนขนาดหน้าจอ (responsive test)</li>
                    <li>ทดสอบบนมือถือ</li>
                </ol>
                
                <p><strong>สิ่งที่ควรเกิดขึ้น:</strong></p>
                <ul>
                    <li>✅ Dropdown แสดงออกมานอก navbar</li>
                    <li>✅ ไม่ถูกตัดโดย overflow: hidden</li>
                    <li>✅ แสดงเหนือเนื้อหาอื่นๆ (z-index สูง)</li>
                    <li>✅ ปิดเมื่อคลิกข้างนอก</li>
                    <li>✅ ทำงานบนมือถือ</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="test-info">🔍 Overflow Test Container</h3>
            <div class="test-note">
                <p>Container ด้านล่างมี <code>overflow: hidden</code> เพื่อทดสอบว่า dropdown สามารถแสดงออกมาได้หรือไม่:</p>
            </div>
            
            <div class="overflow-test">
                <p>นี่คือ container ที่มี overflow: hidden</p>
                <p>Dropdown ควรแสดงออกมานอก container นี้ได้</p>
                <p>ถ้า dropdown ถูกตัดหรือไม่แสดง แสดงว่ายังมีปัญหา</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="test-info">📱 Responsive Test</h3>
            <div class="test-note">
                <p><strong>ทดสอบในขนาดหน้าจอต่างๆ:</strong></p>
                <ul>
                    <li><strong>Desktop (> 768px):</strong> Dropdown ใช้ position: absolute</li>
                    <li><strong>Mobile (≤ 768px):</strong> Dropdown ใช้ position: fixed</li>
                    <li><strong>Admin Dropdown:</strong> จัดชิดซ้าย</li>
                    <li><strong>Profile Dropdown:</strong> จัดชิดขวา</li>
                </ul>
                
                <p><strong>วิธีทดสอบ:</strong></p>
                <ol>
                    <li>เปิด Developer Tools (F12)</li>
                    <li>เปลี่ยนเป็น Mobile view</li>
                    <li>ทดสอบ dropdown อีกครั้ง</li>
                    <li>ตรวจสอบว่า dropdown ไม่หลุดออกจากหน้าจอ</li>
                </ol>
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="test-info">🐛 Debug Information</h3>
            <div class="test-note">
                <p><strong>CSS Properties ที่สำคัญ:</strong></p>
                <ul>
                    <li><code>z-index: 999999</code> - ให้แสดงเหนือทุกอย่าง</li>
                    <li><code>position: fixed</code> - แยกออกจาก navbar</li>
                    <li><code>overflow: visible</code> - ไม่ตัด dropdown</li>
                    <li><code>clip: unset</code> - ไม่จำกัดพื้นที่แสดงผล</li>
                </ul>
                
                <p><strong>หากยังมีปัญหา:</strong></p>
                <ol>
                    <li>ตรวจสอบ Console (F12) หา JavaScript errors</li>
                    <li>ตรวจสอบ CSS ว่า dropdown มี z-index สูงพอ</li>
                    <li>ตรวจสอบว่า parent containers ไม่มี overflow: hidden</li>
                    <li>ตรวจสอบว่า JavaScript ทำงานถูกต้อง</li>
                </ol>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                ← กลับไปหน้าแรก
            </a>
        </div>
        
        <!-- Extra content to test scrolling -->
        <div style="height: 500px; background: rgba(255, 255, 255, 0.02); margin: 40px 0; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #666;">
            <p>Extra content for scroll testing</p>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
    
    <script>
        // Additional debug script
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dropdown test page loaded');
            
            // Log dropdown elements
            const adminDropdown = document.getElementById('adminDropdown');
            const profileDropdown = document.getElementById('profileDropdown');
            
            console.log('Admin dropdown:', adminDropdown);
            console.log('Profile dropdown:', profileDropdown);
            
            if (adminDropdown) {
                console.log('Admin dropdown menu:', adminDropdown.querySelector('.dropdown-menu'));
            }
            
            if (profileDropdown) {
                console.log('Profile dropdown menu:', profileDropdown.querySelector('.dropdown-menu'));
            }
        });
    </script>
</body>
</html>
