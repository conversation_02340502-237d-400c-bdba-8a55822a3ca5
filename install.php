<?php
/**
 * Installation script for Jellyfin Registration System
 * This script sets up the database and initial configuration
 */

// Prevent running if already installed
if (file_exists('config/.installed')) {
    die('System is already installed. Delete config/.installed to reinstall.');
}

$errors = [];
$success_messages = [];

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    $errors[] = 'PHP 7.4 or higher is required. Current version: ' . PHP_VERSION;
}

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'openssl'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $errors[] = "Required PHP extension '{$ext}' is not loaded.";
    }
}

// Handle form submission
if ($_POST) {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'jellyfin_registration';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    $jellyfin_url = $_POST['jellyfin_url'] ?? 'http://localhost:8096';
    $jellyfin_api_key = $_POST['jellyfin_api_key'] ?? '';
    $admin_username = $_POST['admin_username'] ?? 'admin';
    $admin_email = $_POST['admin_email'] ?? 'admin@localhost';
    $admin_password = $_POST['admin_password'] ?? '';
    
    // Validate inputs
    if (empty($admin_password)) {
        $errors[] = 'Admin password is required.';
    }
    
    if (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Valid admin email is required.';
    }
    
    if (empty($errors)) {
        try {
            // Test database connection
            $pdo = new PDO("mysql:host={$db_host};charset=utf8mb4", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `{$db_name}`");
            
            // Read and execute schema
            $schema = file_get_contents('database/schema.sql');
            $schema = str_replace('CREATE DATABASE IF NOT EXISTS jellyfin_registration CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $schema);
            $schema = str_replace('USE jellyfin_registration;', '', $schema);
            
            // Execute schema in parts
            $statements = explode(';', $schema);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            // Update admin user with provided credentials
            $admin_password_hash = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password_hash = ? WHERE id = 1");
            $stmt->execute([$admin_username, $admin_email, $admin_password_hash]);
            
            // Update server configuration
            $stmt = $pdo->prepare("UPDATE server_config SET config_value = ? WHERE config_key = 'jellyfin_server_url'");
            $stmt->execute([$jellyfin_url]);
            
            if (!empty($jellyfin_api_key)) {
                $stmt = $pdo->prepare("UPDATE server_config SET config_value = ? WHERE config_key = 'jellyfin_api_key'");
                $stmt->execute([$jellyfin_api_key]);
            }
            
            // Create config file
            $config_content = "<?php\n";
            $config_content .= "// Database configuration\n";
            $config_content .= "define('DB_HOST', '{$db_host}');\n";
            $config_content .= "define('DB_NAME', '{$db_name}');\n";
            $config_content .= "define('DB_USER', '{$db_user}');\n";
            $config_content .= "define('DB_PASS', '{$db_pass}');\n\n";
            $config_content .= "// Jellyfin API configuration\n";
            $config_content .= "define('JELLYFIN_SERVER_URL', '{$jellyfin_url}');\n";
            $config_content .= "define('JELLYFIN_API_KEY', '{$jellyfin_api_key}');\n\n";
            $config_content .= "try {\n";
            $config_content .= "    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);\n";
            $config_content .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
            $config_content .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
            $config_content .= "} catch(PDOException \$e) {\n";
            $config_content .= "    die(\"Connection failed: \" . \$e->getMessage());\n";
            $config_content .= "}\n";
            $config_content .= "?>";
            
            file_put_contents('config/database.php', $config_content);
            
            // Create installation marker
            if (!is_dir('config')) {
                mkdir('config', 0755, true);
            }
            file_put_contents('config/.installed', date('Y-m-d H:i:s'));
            
            $success_messages[] = 'Installation completed successfully!';
            $success_messages[] = "Admin login: {$admin_username} / {$admin_password}";
            $success_messages[] = '<a href="index.php">Go to main page</a>';
            
        } catch (Exception $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Jellyfin by James Registration System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .install-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            border: 1px solid #333;
        }
        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .error-list, .success-list {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 4px;
        }
        .error-list {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        .success-list {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>Install Jellyfin by James Registration System</h1>
            <p>Please configure your system settings below</p>
        </div>

        <?php if (!empty($errors)): ?>
            <div class="error-list">
                <h3>Errors:</h3>
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($success_messages)): ?>
            <div class="success-list">
                <h3>Success:</h3>
                <ul>
                    <?php foreach ($success_messages as $message): ?>
                        <li><?php echo $message; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php else: ?>
            <form method="POST">
                <h3>Database Configuration</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="db_host">Database Host:</label>
                        <input type="text" id="db_host" name="db_host" class="form-control" value="<?php echo htmlspecialchars($_POST['db_host'] ?? 'localhost'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name">Database Name:</label>
                        <input type="text" id="db_name" name="db_name" class="form-control" value="<?php echo htmlspecialchars($_POST['db_name'] ?? 'jellyfin_registration'); ?>" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="db_user">Database User:</label>
                        <input type="text" id="db_user" name="db_user" class="form-control" value="<?php echo htmlspecialchars($_POST['db_user'] ?? 'root'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass">Database Password:</label>
                        <input type="password" id="db_pass" name="db_pass" class="form-control" value="<?php echo htmlspecialchars($_POST['db_pass'] ?? ''); ?>">
                    </div>
                </div>

                <h3>Jellyfin Configuration</h3>
                <div class="form-group">
                    <label for="jellyfin_url">Jellyfin Server URL:</label>
                    <input type="url" id="jellyfin_url" name="jellyfin_url" class="form-control" value="<?php echo htmlspecialchars($_POST['jellyfin_url'] ?? 'http://localhost:8096'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="jellyfin_api_key">Jellyfin API Key (optional):</label>
                    <input type="text" id="jellyfin_api_key" name="jellyfin_api_key" class="form-control" value="<?php echo htmlspecialchars($_POST['jellyfin_api_key'] ?? ''); ?>">
                    <small style="color: #ccc;">You can set this later in the admin panel</small>
                </div>

                <h3>Admin Account</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="admin_username">Admin Username:</label>
                        <input type="text" id="admin_username" name="admin_username" class="form-control" value="<?php echo htmlspecialchars($_POST['admin_username'] ?? 'admin'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="admin_email">Admin Email:</label>
                        <input type="email" id="admin_email" name="admin_email" class="form-control" value="<?php echo htmlspecialchars($_POST['admin_email'] ?? 'admin@localhost'); ?>" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="admin_password">Admin Password:</label>
                    <input type="password" id="admin_password" name="admin_password" class="form-control" required>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary noir-button" style="width: 100%;">Install System</button>
                </div>
            </form>
        <?php endif; ?>
    </div>
</body>
</html>
