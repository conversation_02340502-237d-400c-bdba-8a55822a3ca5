<?php
http_response_code(500);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เซิร์ฟเวอร์ขัดข้อง - Jellyfin by <PERSON></title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        .error-content {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.05);
            padding: 3rem;
            border-radius: 12px;
            border: 1px solid #333;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #ff6b6b;
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
            margin-bottom: 1rem;
            line-height: 1;
        }
        .error-title {
            font-size: 2rem;
            color: #fff;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.1rem;
            color: #ccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn-home {
            background: linear-gradient(45deg, #007cba, #0056b3);
            color: #fff;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 124, 186, 0.3);
        }
        .btn-refresh {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: #fff;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-refresh:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }
        .error-details {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
            color: #ffc107;
            text-align: left;
        }
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            .error-title {
                font-size: 1.5rem;
            }
            .error-content {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <div class="error-container">
        <div class="error-content fade-in-up">
            <div class="error-code">500</div>
            <h1 class="error-title">เซิร์ฟเวอร์ขัดข้อง</h1>
            <p class="error-message">
                ขออภัย เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์<br>
                ทีมงานได้รับแจ้งแล้วและกำลังแก้ไขปัญหา
            </p>
            
            <div class="error-details">
                <strong>รายละเอียดข้อผิดพลาด:</strong><br>
                เวลา: <?php echo date('Y-m-d H:i:s'); ?><br>
                รหัสข้อผิดพลาด: SRV-<?php echo strtoupper(substr(md5(time()), 0, 8)); ?><br>
                สถานะ: Internal Server Error
            </div>
            
            <div class="error-actions">
                <a href="javascript:location.reload()" class="btn-refresh">รีเฟรชหน้า</a>
                <a href="/" class="btn-home">กลับหน้าหลัก</a>
            </div>
        </div>
    </div>

    <script>
        // Add pulse effect to error code
        document.addEventListener('DOMContentLoaded', function() {
            const errorCode = document.querySelector('.error-code');
            
            // Add pulse effect
            setInterval(function() {
                errorCode.style.animation = 'pulse 1s ease-in-out';
                setTimeout(function() {
                    errorCode.style.animation = '';
                }, 1000);
            }, 3000);
        });
        
        // Add pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.05); opacity: 0.8; }
                100% { transform: scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // Auto-refresh after 30 seconds
        setTimeout(function() {
            if (confirm('ต้องการรีเฟรชหน้าเพื่อลองใหม่หรือไม่?')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
