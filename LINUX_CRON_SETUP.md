# 🎬 Jellyfin by <PERSON> - <PERSON> Cron Setup Guide

## 🚀 **ระบบ Cron Jobs สำหรับ Production Linux**

### 📋 **ไฟล์ที่สร้างขึ้น:**

#### 🔧 **Cron Scripts (Linux-Optimized):**
1. **`cron/linux_payment_check.php`** - ตรวจสอบ PayNoi payments ทุก 5 นาที
2. **`cron/linux_expiration_check.php`** - ตรวจสอบ user หมดอายุทุกชั่วโมง  
3. **`cron/linux_manual_check.php`** - ตรวจสอบ manual slips ทุก 10 นาที
4. **`cron/linux_health_check.php`** - ตรวจสอบระบบทุก 15 นาที

#### 📦 **Installation & Monitoring:**
1. **`install_linux_cron.sh`** - สคริปต์ติดตั้งอัตโนมัติ
2. **`monitor_linux_cron.sh`** - สคริปต์ตรวจสอบสถานะ
3. **`cron/linux_crontab.txt`** - ตัวอย่าง crontab configuration

---

## 🛠️ **การติดตั้งบน Production Server (*************)**

### **Step 1: อัปโหลดไฟล์ไป Production**
```bash
# อัปโหลดไฟล์ทั้งหมด
scp install_linux_cron.sh monitor_linux_cron.sh cron/linux_*.php cron/linux_crontab.txt root@jellyfin:/tmp/
```

### **Step 2: รันการติดตั้ง**
```bash
# เข้าสู่ production server
ssh root@*************

# ไปที่โฟลเดอร์ที่อัปโหลด
cd /tmp

# ให้สิทธิ์ execute
chmod +x install_linux_cron.sh
chmod +x monitor_linux_cron.sh

# รันการติดตั้ง
./install_linux_cron.sh
```

### **Step 3: ตรวจสอบการติดตั้ง**
```bash
# ตรวจสอบสถานะ cron jobs
./monitor_linux_cron.sh

# ดู crontab ที่ติดตั้งแล้ว
crontab -u www-data -l

# ตรวจสอบ log files
ls -la /var/log/jellyfin/
```

---

## ⏰ **Cron Jobs Schedule**

| **งาน** | **ความถี่** | **คำอธิบาย** |
|---------|-------------|--------------|
| **PayNoi Payment Check** | ทุก 5 นาที | ตรวจสอบ PayNoi API และ auto-verify payments |
| **User Expiration Check** | ทุกชั่วโมง | ปิด Jellyfin users ที่หมดอายุ |
| **Manual Payment Check** | ทุก 10 นาที | Auto-verify manual slips (จำนวนเล็ก + trusted users) |
| **System Health Check** | ทุก 15 นาที | ตรวจสอบ database, API, disk space, performance |
| **Log Cleanup** | ทุกวันเวลา 02:00 | ลบ log files ที่ใหญ่เกิน 100MB |
| **Weekly Maintenance** | อาทิตย์เวลา 03:00 | รัน maintenance tasks |

---

## 📊 **การตรวจสอบและ Monitoring**

### **คำสั่งพื้นฐาน:**
```bash
# ตรวจสอบสถานะทั้งหมด
./monitor_linux_cron.sh

# ดู log files แบบ real-time
./monitor_linux_cron.sh watch

# รัน health check แบบ manual
./monitor_linux_cron.sh test

# ดู log entries ล่าสุด
./monitor_linux_cron.sh logs
```

### **ตรวจสอบ Cron Service:**
```bash
# สถานะ cron service
systemctl status cron

# restart cron service
systemctl restart cron

# ดู cron logs
journalctl -u cron -f
```

### **ตรวจสอบ Log Files:**
```bash
# PayNoi payment verification
tail -f /var/log/jellyfin/payment_cron.log

# User expiration check
tail -f /var/log/jellyfin/expiration_cron.log

# Manual payment verification
tail -f /var/log/jellyfin/manual_cron.log

# System health monitoring
tail -f /var/log/jellyfin/health_cron.log
```

---

## 🔧 **การแก้ไขปัญหา**

### **ปัญหาที่พบบ่อย:**

#### **1. Cron Jobs ไม่ทำงาน**
```bash
# ตรวจสอบ crontab
crontab -u www-data -l

# ตรวจสอบ cron service
systemctl status cron

# ดู error logs
tail -f /var/log/syslog | grep CRON
```

#### **2. Database Connection Error**
```bash
# ตรวจสอบ MySQL service
systemctl status mysql

# ทดสอบ database connection
sudo -u www-data php -r "
require_once '/var/www/html/config/database.php';
try { \$pdo->query('SELECT 1'); echo 'OK'; } catch(Exception \$e) { echo \$e->getMessage(); }
"
```

#### **3. Jellyfin API Error**
```bash
# ตรวจสอบ Jellyfin service
systemctl status jellyfin

# ทดสอบ API connection
sudo -u www-data php -r "
require_once '/var/www/html/includes/JellyfinAPI.php';
try { \$api = new JellyfinAPI(); \$users = \$api->getUsers(); echo 'OK - ' . count(\$users) . ' users'; } catch(Exception \$e) { echo \$e->getMessage(); }
"
```

#### **4. Permission Issues**
```bash
# แก้ไข permissions
chown -R www-data:www-data /var/www/html/cron
chown -R www-data:www-data /var/log/jellyfin
chmod 644 /var/www/html/cron/*.php
chmod 755 /var/log/jellyfin
```

---

## 📈 **Performance & Optimization**

### **Resource Usage:**
- **CPU**: แต่ละ script ใช้เวลา 10-30 วินาที
- **Memory**: ใช้ RAM น้อยกว่า 50MB ต่อ script
- **Database**: Queries มี LIMIT เพื่อป้องกัน overload
- **Disk**: Log files มีการ cleanup อัตโนมัติ

### **Scaling Guidelines:**
- **1-100 users**: ใช้ default schedule
- **100-500 users**: ลด health check เป็น 30 นาที
- **500+ users**: เพิ่ม database indexes และ optimize queries

---

## 🔒 **Security Features**

### **Built-in Security:**
- ✅ **Web Access Protection** - ทุก script ปฏิเสธการเข้าถึงผ่าน web
- ✅ **User Isolation** - รันด้วย www-data user (ไม่ใช่ root)
- ✅ **Log Rotation** - ป้องกัน disk space attacks
- ✅ **Input Validation** - ตรวจสอบข้อมูลก่อนประมวลผล
- ✅ **Database Transactions** - ป้องกัน data corruption

### **Recommended Security:**
```bash
# ตั้งค่า firewall
ufw allow ssh
ufw allow http
ufw allow https
ufw enable

# อัปเดต system
apt update && apt upgrade -y

# ตรวจสอบ log files เป็นประจำ
tail -f /var/log/jellyfin/*.log
```

---

## 🎯 **Success Criteria**

### **ระบบทำงานปกติเมื่อ:**
- ✅ **Cron Jobs**: ทุก job รันตาม schedule
- ✅ **Database**: เชื่อมต่อได้และ response time < 100ms
- ✅ **Jellyfin API**: เชื่อมต่อได้และ response time < 500ms
- ✅ **Log Files**: มีการอัปเดตเป็นประจำ
- ✅ **Disk Space**: ใช้งานน้อยกว่า 80%
- ✅ **Memory**: ใช้งานน้อยกว่า 80%

### **การแจ้งเตือนเมื่อมีปัญหา:**
- ❌ **Database Connection Failed**
- ❌ **Jellyfin API Timeout**
- ❌ **Disk Space > 90%**
- ❌ **Log Files ไม่อัปเดตเกิน 1 ชั่วโมง**

---

## 🎬 **Production Ready!**

**ระบบ Cron Jobs สำหรับ Jellyfin by James พร้อมใช้งานบน Production Linux แล้ว!**

### **Next Steps:**
1. ✅ Deploy ไฟล์ไป production server
2. ✅ รัน `install_linux_cron.sh`
3. ✅ ตรวจสอบด้วย `monitor_linux_cron.sh`
4. ✅ ตั้งค่า PayNoi API keys
5. ✅ ตั้งค่า Jellyfin API credentials
6. ✅ Monitor logs เป็นประจำ

**🚀 ระบบจะทำงานอัตโนมัติ 24/7 และดูแลตัวเองได้!**
