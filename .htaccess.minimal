# Minimal .htaccess for Linux Production Testing
RewriteEngine On

# Basic rewrite test
RewriteRule ^test/?$ test-rewrite.php [L]

# Essential pages only
RewriteRule ^login/?$ login.php [L]
RewriteRule ^dashboard/?$ dashboard.php [L]
RewriteRule ^admin/?$ admin/index.php [L]

# Prevent access to sensitive files
<Files ".htaccess">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# Custom Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php
