<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🧪 Test Fixed Cron Job</h2>";

try {
    echo "<h3>🔍 Step 1: Simulate Cron Logic</h3>";
    
    // Get pending manual payments (same as cron)
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.status = 'pending'
        AND pt.payment_method = 'manual'
        AND pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at ASC
        LIMIT 5
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    echo "<p>Found " . count($payments) . " pending manual payments</p>";
    
    if (!$payments) {
        echo "<p>❌ No manual payments to test</p>";
        echo "<p><a href='/payment.php'>Upload a test slip</a></p>";
        return;
    }
    
    echo "<h3>🔍 Step 2: Process Each Payment</h3>";
    
    $checkedCount = 0;
    $autoVerifiedCount = 0;
    $duplicateCount = 0;
    $errorCount = 0;
    
    foreach ($payments as $payment) {
        echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 10px;'>";
        echo "<h4>Payment ID: {$payment['id']} - User: {$payment['username']} - Amount: {$payment['amount']} THB</h4>";
        
        $checkedCount++;
        
        try {
            // Fix slip path (same as cron)
            $slipImage = $payment['slip_image'];
            if (strpos($slipImage, 'uploads/slips/uploads/slips/') !== false) {
                $slipImage = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $slipImage);
            } elseif (strpos($slipImage, 'uploads/slips/') !== 0) {
                $slipImage = 'uploads/slips/' . $slipImage;
            }
            
            echo "<p>Slip path: {$slipImage}</p>";
            
            // Check if file exists
            if (!file_exists($slipImage)) {
                echo "<p>❌ Slip image not found</p>";
                $errorCount++;
                continue;
            }
            
            $fileSize = filesize($slipImage);
            echo "<p>✅ File found ({$fileSize} bytes)</p>";
            
            // Check for duplicates (same as cron)
            if (!empty($payment['slip_hash'])) {
                $dupStmt = $pdo->prepare("
                    SELECT COUNT(*) as count FROM payment_transactions
                    WHERE slip_hash = ? AND status = 'completed' AND id != ?
                ");
                $dupStmt->execute([$payment['slip_hash'], $payment['id']]);
                $duplicateCount_check = $dupStmt->fetch()['count'];
                
                if ($duplicateCount_check > 0) {
                    echo "<p>❌ Duplicate slip detected</p>";
                    $duplicateCount++;
                    continue;
                }
            }
            
            // Auto-verification criteria (same as cron)
            $autoVerify = false;
            $isRecent = (time() - strtotime($payment['created_at'])) < 86400; // 24 hours
            
            echo "<p>Recent payment (< 24h): " . ($isRecent ? '✅' : '❌') . "</p>";
            
            // Small amount check
            if ($payment['amount'] <= 100) {
                $autoVerify = true;
                echo "<p>✅ Auto-verifying small amount: {$payment['amount']} THB</p>";
            }
            
            // Trusted user check
            if (!$autoVerify) {
                $historyStmt = $pdo->prepare("
                    SELECT COUNT(*) as verified_count FROM payment_transactions
                    WHERE user_id = ? AND status = 'completed'
                ");
                $historyStmt->execute([$payment['user_id']]);
                $verifiedCount = $historyStmt->fetch()['verified_count'];
                
                echo "<p>Previous completed payments: {$verifiedCount}</p>";
                
                if ($verifiedCount >= 2) {
                    $autoVerify = true;
                    echo "<p>✅ Auto-verifying trusted user ({$verifiedCount} previous payments)</p>";
                }
            }
            
            // Perform verification if criteria met
            if ($autoVerify && $isRecent) {
                echo "<p><strong>🔄 Performing auto-verification...</strong></p>";
                
                // Test the update query (same as fixed cron)
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET status = 'completed',
                        paid_at = NOW()
                    WHERE id = ?
                ");
                
                echo "<p>SQL Query: UPDATE payment_transactions SET status = 'completed', paid_at = NOW() WHERE id = {$payment['id']}</p>";
                
                // Execute in test mode
                if (isset($_POST['execute_test'])) {
                    $result = $updateStmt->execute([$payment['id']]);
                    
                    if ($result) {
                        echo "<p>✅ <strong>Payment verified successfully!</strong></p>";
                        echo "<p>Rows affected: " . $updateStmt->rowCount() . "</p>";
                        $autoVerifiedCount++;
                        
                        // Show updated record
                        $checkStmt = $pdo->prepare("SELECT status, paid_at FROM payment_transactions WHERE id = ?");
                        $checkStmt->execute([$payment['id']]);
                        $updated = $checkStmt->fetch();
                        echo "<p>New status: {$updated['status']}, paid_at: {$updated['paid_at']}</p>";
                        
                    } else {
                        echo "<p>❌ Failed to update payment</p>";
                        $errorCount++;
                    }
                } else {
                    echo "<p>⚠️ Test mode - not executing update</p>";
                }
                
            } else {
                echo "<p>❌ Auto-verification criteria not met</p>";
                if (!$isRecent) echo "<p>Reason: Payment too old</p>";
                if (!$autoVerify) echo "<p>Reason: Not small amount and not trusted user</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Error processing payment: " . $e->getMessage() . "</p>";
            $errorCount++;
        }
        
        echo "</div>";
    }
    
    echo "<h3>📊 Summary</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Checked</td><td>{$checkedCount}</td></tr>";
    echo "<tr><td>Auto-verified</td><td style='background: lightgreen;'>{$autoVerifiedCount}</td></tr>";
    echo "<tr><td>Duplicates</td><td style='background: yellow;'>{$duplicateCount}</td></tr>";
    echo "<tr><td>Errors</td><td style='background: lightcoral;'>{$errorCount}</td></tr>";
    echo "</table>";
    
    if (!isset($_POST['execute_test'])) {
        echo "<form method='POST'>";
        echo "<input type='hidden' name='execute_test' value='1'>";
        echo "<button type='submit' style='background: red; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px;'>🚀 Execute Test (Actually Update Database)</button>";
        echo "</form>";
        echo "<p><strong>⚠️ Warning:</strong> This will actually update the database!</p>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
echo "<p><a href='/test_existing_schema.php'>🔍 Test Existing Schema</a></p>";
?>
