<?php
// Test direct file access
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct Access - Jellyfin by <PERSON></title>
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #333;
        }
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
        .warning { color: #FFA500; }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #FFD700; text-align: center;">🔗 Test Direct File Access</h1>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📋 File Information</h2>
            <?php
            $lineFile = 'line/line.jpg';
            $fullPath = __DIR__ . '/' . $lineFile;
            
            echo "<p><strong>File path:</strong> $lineFile</p>";
            echo "<p><strong>Full system path:</strong> $fullPath</p>";
            echo "<p><strong>Current directory:</strong> " . __DIR__ . "</p>";
            echo "<p><strong>Document root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
            
            if (file_exists($fullPath)) {
                echo "<p class='success'>✅ File exists</p>";
                echo "<p><strong>File size:</strong> " . number_format(filesize($fullPath)) . " bytes</p>";
                echo "<p><strong>MIME type:</strong> " . mime_content_type($fullPath) . "</p>";
                echo "<p><strong>Readable:</strong> " . (is_readable($fullPath) ? 'Yes' : 'No') . "</p>";
                echo "<p><strong>Permissions:</strong> " . substr(sprintf('%o', fileperms($fullPath)), -4) . "</p>";
                
                // Check if it's a valid image
                $imageInfo = getimagesize($fullPath);
                if ($imageInfo) {
                    echo "<p class='success'>✅ Valid image file</p>";
                    echo "<p><strong>Dimensions:</strong> {$imageInfo[0]} x {$imageInfo[1]} pixels</p>";
                    echo "<p><strong>Image type:</strong> " . image_type_to_mime_type($imageInfo[2]) . "</p>";
                } else {
                    echo "<p class='error'>❌ Not a valid image file</p>";
                }
            } else {
                echo "<p class='error'>❌ File does not exist</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🌐 Direct URL Access Test</h2>
            <p style="color: #ccc;">คลิกลิงก์เหล่านี้เพื่อทดสอบการเข้าถึงไฟล์โดยตรง:</p>
            
            <?php
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
            $testUrls = [
                'line/line.jpg' => 'Relative path',
                '/line/line.jpg' => 'Absolute path',
                $baseUrl . '/line/line.jpg' => 'Full URL'
            ];
            
            foreach ($testUrls as $url => $description) {
                echo "<div class='test-result'>";
                echo "<p><strong>$description:</strong></p>";
                echo "<a href='$url' target='_blank' class='test-link'>$url</a>";
                echo "<p style='font-size: 0.9rem; color: #ccc; margin-top: 10px;'>คลิกเพื่อเปิดในแท็บใหม่</p>";
                echo "</div>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🖼️ Inline Image Test</h2>
            <p style="color: #ccc;">ทดสอบการแสดงรูปภาพในหน้าเว็บ:</p>
            
            <div style="text-align: center; padding: 20px; background: rgba(0, 195, 0, 0.1); border: 1px solid #00c300; border-radius: 8px;">
                <h3 style="color: #00c300;">LINE QR Code</h3>
                <div style="display: inline-block; background: white; padding: 1rem; border-radius: 8px;">
                    <img id="test-qr-image"
                         src="line/line.jpg"
                         alt="LINE QR Code"
                         style="width: 200px; height: 200px; object-fit: contain; border: 1px solid #ddd;"
                         onload="document.getElementById('image-status').innerHTML='<span class=success>✅ Image loaded successfully</span>'; console.log('Image loaded:', this.src, 'Size:', this.naturalWidth + 'x' + this.naturalHeight);"
                         onerror="document.getElementById('image-status').innerHTML='<span class=error>❌ Failed to load image</span>'; console.error('Image failed to load:', this.src);">
                </div>
                <div id="image-status" style="margin-top: 1rem; font-weight: bold;">
                    🔄 Loading image...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">🔧 HTTP Headers Test</h2>
            <p style="color: #ccc;">ตรวจสอบ HTTP response headers:</p>
            
            <div style="background: #000; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                <?php
                $testUrl = $baseUrl . '/line/line.jpg';
                echo "<span style='color: #4CAF50;'>Testing URL: $testUrl</span><br><br>";
                
                // Test HTTP headers
                $headers = @get_headers($testUrl, 1);
                if ($headers) {
                    echo "<span style='color: #FFD700;'>HTTP Response:</span><br>";
                    foreach ($headers as $key => $value) {
                        if (is_array($value)) {
                            foreach ($value as $v) {
                                echo "<span style='color: #ccc;'>$key: $v</span><br>";
                            }
                        } else {
                            echo "<span style='color: #ccc;'>$key: $value</span><br>";
                        }
                    }
                } else {
                    echo "<span style='color: #ff6b6b;'>❌ Failed to get HTTP headers</span><br>";
                }
                ?>
            </div>
        </div>
        
        <div class="test-section">
            <h2 style="color: #00c300;">📝 Troubleshooting Steps</h2>
            
            <h3 style="color: #FFD700;">หากไฟล์ยังเข้าถึงไม่ได้:</h3>
            <ol style="color: #ccc; line-height: 1.8;">
                <li><strong>ตรวจสอบ .htaccess:</strong>
                    <ul>
                        <li>ปิด hotlink protection ชั่วคราว</li>
                        <li>ตรวจสอบ RewriteRule ที่อาจบล็อก</li>
                        <li>ลองเปลี่ยนชื่อ .htaccess เป็น .htaccess.bak</li>
                    </ul>
                </li>
                <li><strong>ตรวจสอบ Permissions (Linux):</strong>
                    <ul>
                        <li><code>chmod 755 line/</code></li>
                        <li><code>chmod 644 line/line.jpg</code></li>
                        <li><code>chown -R www-data:www-data line/</code></li>
                    </ul>
                </li>
                <li><strong>ตรวจสอบ Apache Configuration:</strong>
                    <ul>
                        <li>AllowOverride All</li>
                        <li>Directory permissions</li>
                        <li>Virtual host configuration</li>
                    </ul>
                </li>
                <li><strong>ตรวจสอบ Error Log:</strong>
                    <ul>
                        <li><code>tail -f /var/log/apache2/error.log</code></li>
                        <li>หา 403 Forbidden errors</li>
                        <li>หา rewrite errors</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/test-qr-line.php" style="color: #00c300; text-decoration: none; padding: 10px 20px; border: 1px solid #00c300; border-radius: 5px; margin-right: 10px;">
                🧪 QR Test
            </a>
            <a href="/guide#line-group" style="color: #FFA500; text-decoration: none; padding: 10px 20px; border: 1px solid #FFA500; border-radius: 5px; margin-right: 10px;">
                📖 Guide
            </a>
            <a href="/" style="color: #FFD700; text-decoration: none; padding: 10px 20px; border: 1px solid #FFD700; border-radius: 5px;">
                🏠 หน้าแรก
            </a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Direct access test page loaded');
            console.log('Current URL:', window.location.href);
            
            // Test image loading
            const testImage = document.getElementById('test-qr-image');
            if (testImage) {
                console.log('Testing image src:', testImage.src);
                
                // Additional debugging
                testImage.addEventListener('load', function() {
                    console.log('✅ Image loaded successfully');
                    console.log('Natural dimensions:', this.naturalWidth + 'x' + this.naturalHeight);
                    console.log('Complete:', this.complete);
                });
                
                testImage.addEventListener('error', function() {
                    console.error('❌ Image failed to load');
                    console.error('Src:', this.src);
                    console.error('Complete:', this.complete);
                });
            }
        });
    </script>
</body>
</html>
