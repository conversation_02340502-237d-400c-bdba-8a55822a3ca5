<?php
/**
 * Linux Cron Job: User Expiration Check
 * Optimized for production Linux environment (*************)
 * 
 * Usage: php /var/www/html/cron/linux_expiration_check.php
 * Cron: every 1 minute - /usr/bin/php /var/www/html/cron/linux_expiration_check.php >> /var/log/jellyfin/expiration_cron.log 2>&1
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory to web root
$webRoot = '/var/www/html';
chdir($webRoot);

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Setup logging
$logFile = '/var/log/jellyfin/expiration_cron.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

writeLog("=== User Expiration Check Started ===");

try {
    // Test database connection
    $pdo->query("SELECT 1");
    writeLog("✅ Database connection successful");
    
    // Get expired subscriptions
    $stmt = $pdo->prepare("
        SELECT us.*, u.username, u.id as user_id, p.name as package_name
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN packages p ON us.package_id = p.id
        WHERE us.status = 'active'
        AND us.end_date < NOW()
        ORDER BY us.end_date ASC
        LIMIT 100
    ");
    $stmt->execute();
    $expiredSubscriptions = $stmt->fetchAll();
    
    writeLog("Found " . count($expiredSubscriptions) . " expired subscriptions");
    
    if (empty($expiredSubscriptions)) {
        writeLog("No expired subscriptions to process");
        
        // Still check for orphaned users
        writeLog("--- Checking for orphaned users ---");
        
        $orphanStmt = $pdo->prepare("
            SELECT u.username, u.id as user_id
            FROM users u
            WHERE u.id NOT IN (
                SELECT DISTINCT user_id FROM user_subscriptions 
                WHERE status = 'active' AND end_date > NOW()
            )
            AND u.created_at < DATE_SUB(NOW(), INTERVAL 1 DAY)
            LIMIT 50
        ");
        $orphanStmt->execute();
        $orphanUsers = $orphanStmt->fetchAll();
        
        if (!empty($orphanUsers)) {
            writeLog("Found " . count($orphanUsers) . " orphaned users");
            
            $jellyfin = new JellyfinAPI();
            $orphanDisabledCount = 0;
            
            foreach ($orphanUsers as $user) {
                try {
                    // Find Jellyfin user
                    $jellyfinUsers = $jellyfin->getUsers();
                    $jellyfinUser = null;
                    
                    foreach ($jellyfinUsers as $ju) {
                        if ($ju['Name'] === $user['username']) {
                            $jellyfinUser = $ju;
                            break;
                        }
                    }
                    
                    if ($jellyfinUser && !$jellyfinUser['Policy']['IsDisabled']) {
                        $result = $jellyfin->setUserEnabled($jellyfinUser['Id'], false);
                        if ($result) {
                            writeLog("✅ Disabled orphaned user: {$user['username']}");
                            $orphanDisabledCount++;
                        }
                    }
                } catch (Exception $e) {
                    writeLog("❌ Error checking orphaned user {$user['username']}: " . $e->getMessage());
                }
            }
            
            writeLog("Disabled {$orphanDisabledCount} orphaned users");
        }
        
        exit(0);
    }
    
    $jellyfin = new JellyfinAPI();
    $disabledCount = 0;
    $errorCount = 0;
    $alreadyDisabledCount = 0;
    
    foreach ($expiredSubscriptions as $subscription) {
        writeLog("Processing expired subscription for user: {$subscription['username']} (expired: {$subscription['end_date']})");
        
        try {
            $pdo->beginTransaction();
            
            // Update subscription status
            $updateStmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET status = 'expired', updated_at = NOW() 
                WHERE id = ?
            ");
            $updateStmt->execute([$subscription['id']]);
            
            // Find Jellyfin user
            $jellyfinUsers = $jellyfin->getUsers();
            $jellyfinUser = null;
            
            foreach ($jellyfinUsers as $ju) {
                if ($ju['Name'] === $subscription['username']) {
                    $jellyfinUser = $ju;
                    break;
                }
            }
            
            if ($jellyfinUser) {
                // Check if already disabled
                if (!$jellyfinUser['Policy']['IsDisabled']) {
                    // Disable the user
                    $result = $jellyfin->setUserEnabled($jellyfinUser['Id'], false);
                    
                    if ($result) {
                        writeLog("✅ Disabled Jellyfin user: {$subscription['username']}");
                        $disabledCount++;
                        
                        // Log activity
                        $activityStmt = $pdo->prepare("
                            INSERT INTO activity_logs (user_id, action, details, created_at)
                            VALUES (?, 'subscription_expired', ?, NOW())
                        ");
                        $activityStmt->execute([
                            $subscription['user_id'],
                            "Subscription expired and Jellyfin account disabled - Package: {$subscription['package_name']}, End date: {$subscription['end_date']}"
                        ]);
                        
                    } else {
                        writeLog("❌ Failed to disable Jellyfin user: {$subscription['username']}");
                        $errorCount++;
                    }
                } else {
                    writeLog("ℹ️  Jellyfin user already disabled: {$subscription['username']}");
                    $alreadyDisabledCount++;
                }
            } else {
                writeLog("⚠️  Jellyfin user not found: {$subscription['username']}");
            }
            
            $pdo->commit();
            
        } catch (Exception $e) {
            $pdo->rollback();
            writeLog("❌ Error processing expiration for user {$subscription['username']}: " . $e->getMessage());
            $errorCount++;
        }
    }
    
    // Check for users without active subscriptions but still enabled in Jellyfin
    writeLog("--- Checking for inconsistencies ---");
    
    $inconsistentStmt = $pdo->prepare("
        SELECT u.username, u.id as user_id
        FROM users u
        WHERE u.id NOT IN (
            SELECT DISTINCT user_id FROM user_subscriptions 
            WHERE status = 'active' AND end_date > NOW()
        )
        AND u.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
        LIMIT 30
    ");
    $inconsistentStmt->execute();
    $inconsistentUsers = $inconsistentStmt->fetchAll();
    
    $inconsistentDisabledCount = 0;
    
    foreach ($inconsistentUsers as $user) {
        try {
            // Find Jellyfin user
            $jellyfinUsers = $jellyfin->getUsers();
            $jellyfinUser = null;
            
            foreach ($jellyfinUsers as $ju) {
                if ($ju['Name'] === $user['username']) {
                    $jellyfinUser = $ju;
                    break;
                }
            }
            
            if ($jellyfinUser && !$jellyfinUser['Policy']['IsDisabled']) {
                writeLog("⚠️  Found enabled Jellyfin user without active subscription: {$user['username']}");
                
                $result = $jellyfin->setUserEnabled($jellyfinUser['Id'], false);
                if ($result) {
                    writeLog("✅ Disabled inconsistent user: {$user['username']}");
                    $inconsistentDisabledCount++;
                    
                    // Log activity
                    $activityStmt = $pdo->prepare("
                        INSERT INTO activity_logs (user_id, action, details, created_at)
                        VALUES (?, 'account_disabled', ?, NOW())
                    ");
                    $activityStmt->execute([
                        $user['user_id'],
                        "Account disabled due to no active subscription"
                    ]);
                }
            }
            
        } catch (Exception $e) {
            writeLog("❌ Error checking inconsistent user {$user['username']}: " . $e->getMessage());
        }
    }
    
    writeLog("=== User Expiration Check Completed ===");
    writeLog("Expired subscriptions: " . count($expiredSubscriptions));
    writeLog("Disabled users: {$disabledCount}");
    writeLog("Already disabled: {$alreadyDisabledCount}");
    writeLog("Inconsistent users disabled: {$inconsistentDisabledCount}");
    writeLog("Errors: {$errorCount}");
    
} catch (Exception $e) {
    writeLog("❌ Fatal error: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
