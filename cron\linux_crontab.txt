# Jellyfin by James - Linux Production Crontab
# Optimized for production environment: ************* / embyjames.xyz
# 
# Installation Instructions:
# 1. Copy this file to production server
# 2. Run: sudo ./install_linux_cron.sh
# 
# Manual Installation:
# 1. sudo crontab -u www-data -e
# 2. Copy the cron jobs below into the crontab editor
# 3. Save and exit

# ============================================
# AUTOMATED CRON JOBS FOR JELLYFIN BY JAMES
# ============================================

# PayNoi Payment Verification - Every 5 seconds
# Checks PayNoi API for new transactions and auto-verifies payments
* * * * * /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/cron/linux_payment_check.php >> /var/log/jellyfin/payment_cron.log 2>&1

# User Expiration Check - Every 1 minute
# Disables expired Jellyfin users and updates subscription status
* * * * * /usr/bin/php /var/www/html/cron/linux_expiration_check.php >> /var/log/jellyfin/expiration_cron.log 2>&1

# Manual Payment Slip Verification - Every 5 seconds
# Auto-verifies small amounts and trusted users' manual payment slips
* * * * * /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 5; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 10; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 15; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 20; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 25; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 30; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 35; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 40; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 45; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 50; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1
* * * * * sleep 55; /usr/bin/php /var/www/html/cron/linux_manual_check.php >> /var/log/jellyfin/manual_cron.log 2>&1

# System Health Check - Every 15 minutes
# Monitors database, Jellyfin API, disk space, and system performance
*/15 * * * * /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/health_cron.log 2>&1

# Log File Cleanup - Daily at 2:00 AM
# Truncates log files larger than 100MB to prevent disk space issues
0 2 * * * find /var/log/jellyfin -name "*.log" -size +100M -exec truncate -s 10M {} \; >> /var/log/jellyfin/cleanup.log 2>&1

# Weekly Database Maintenance - Sunday at 3:00 AM
# Runs comprehensive system health check and maintenance tasks
0 3 * * 0 /usr/bin/php /var/www/html/cron/linux_health_check.php >> /var/log/jellyfin/weekly_maintenance.log 2>&1

# ============================================
# OPTIONAL ADVANCED CRON JOBS
# ============================================

# Backup Database - Daily at 1:00 AM (uncomment to enable)
# 0 1 * * * mysqldump -u root -p jellyfin_registration > /var/backups/jellyfin_$(date +\%Y\%m\%d).sql 2>> /var/log/jellyfin/backup.log

# Clean Old Slip Images - Weekly on Monday at 4:00 AM (uncomment to enable)
# 0 4 * * 1 find /var/www/html/uploads/slips -name "*.jpg" -o -name "*.png" -mtime +30 -delete >> /var/log/jellyfin/cleanup.log 2>&1

# System Resource Monitor - Every 5 minutes (uncomment for detailed monitoring)
# */5 * * * * echo "$(date): Load: $(cat /proc/loadavg), Memory: $(free -m | grep Mem | awk '{print $3"/"$2" MB"}')" >> /var/log/jellyfin/resources.log

# ============================================
# CRON JOB SCHEDULE EXPLANATION
# ============================================
#
# Format: minute hour day month weekday command
#
# minute:   0-59
# hour:     0-23
# day:      1-31
# month:    1-12
# weekday:  0-7 (0 and 7 are Sunday)
#
# Special characters:
# *    = any value
# ,    = value list separator
# -    = range of values
# /    = step values
# 
# Examples:
# */5 * * * *     = every 5 minutes
# 0 * * * *       = every hour at minute 0
# 0 2 * * *       = daily at 2:00 AM
# 0 3 * * 0       = weekly on Sunday at 3:00 AM
# 0 1 1 * *       = monthly on 1st day at 1:00 AM

# ============================================
# MONITORING AND TROUBLESHOOTING
# ============================================
#
# View current crontab:
# sudo crontab -u www-data -l
#
# Check cron service status:
# sudo systemctl status cron
#
# View cron logs:
# sudo tail -f /var/log/jellyfin/payment_cron.log
# sudo tail -f /var/log/jellyfin/expiration_cron.log
# sudo tail -f /var/log/jellyfin/manual_cron.log
# sudo tail -f /var/log/jellyfin/health_cron.log
#
# Check system cron logs:
# sudo journalctl -u cron -f
# sudo tail -f /var/log/syslog | grep CRON
#
# Test cron script manually:
# sudo -u www-data /usr/bin/php /var/www/html/cron/linux_health_check.php
#
# Restart cron service:
# sudo systemctl restart cron

# ============================================
# PRODUCTION ENVIRONMENT NOTES
# ============================================
#
# Server: ************* / embyjames.xyz
# User: www-data (web server user)
# PHP: /usr/bin/php (CLI version)
# Web Root: /var/www/html
# Log Directory: /var/log/jellyfin
#
# Required PHP Extensions:
# - php-mysql (database connection)
# - php-curl (API calls)
# - php-json (JSON processing)
# - php-mbstring (string handling)
#
# Required Permissions:
# - www-data must have read/write access to /var/log/jellyfin
# - www-data must have read access to /var/www/html
# - Database user must have SELECT, INSERT, UPDATE permissions
#
# Security Considerations:
# - All cron scripts check for web access and deny it
# - Scripts run as www-data user (not root)
# - Log files are rotated to prevent disk space issues
# - Database credentials are stored securely in config files

# ============================================
# PERFORMANCE OPTIMIZATION
# ============================================
#
# Frequency Guidelines:
# - PayNoi check: 5 seconds (real-time payment processing)
# - Expiration check: 1 minute (timely user management)
# - Manual verification: 5 seconds (instant user experience)
# - Health check: 15 minutes (system monitoring)
#
# Resource Usage:
# - Each script runs for 10-30 seconds typically
# - Database queries are optimized with LIMIT clauses
# - Log files are automatically cleaned up
# - Memory usage is minimal (<50MB per script)
#
# Scaling Considerations:
# - Scripts can handle 1000+ users efficiently
# - Database indexes should be maintained
# - Log rotation prevents disk space issues
# - Monitoring alerts can be added for failures
