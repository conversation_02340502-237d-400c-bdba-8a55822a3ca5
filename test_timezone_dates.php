<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🕐 Test Timezone และ Package Dates</h2>";

// Test timezone settings
echo "<h3>🌏 Timezone Settings</h3>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>PHP Default Timezone</td><td>" . date_default_timezone_get() . "</td></tr>";
echo "<tr><td>Current PHP Time</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
echo "<tr><td>Current PHP Time (Thai Format)</td><td>" . date('d/m/Y H:i:s') . "</td></tr>";

// Get MySQL timezone
try {
    $stmt = $pdo->query("SELECT NOW() as mysql_time, @@session.time_zone as mysql_timezone");
    $mysql_info = $stmt->fetch();
    echo "<tr><td>MySQL Time</td><td>" . $mysql_info['mysql_time'] . "</td></tr>";
    echo "<tr><td>MySQL Timezone</td><td>" . $mysql_info['mysql_timezone'] . "</td></tr>";
} catch (Exception $e) {
    echo "<tr><td>MySQL Error</td><td>" . $e->getMessage() . "</td></tr>";
}

echo "</table>";

// Test user subscriptions
echo "<h3>📦 User Subscriptions</h3>";

try {
    $stmt = $pdo->prepare("
        SELECT us.*, p.name as package_name, p.duration_days, u.username
        FROM user_subscriptions us
        JOIN packages p ON us.package_id = p.id
        JOIN users u ON us.user_id = u.id
        ORDER BY us.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $subscriptions = $stmt->fetchAll();
    
    if ($subscriptions) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>User</th>";
        echo "<th>Package</th>";
        echo "<th>Status</th>";
        echo "<th>Created At</th>";
        echo "<th>Start Date</th>";
        echo "<th>End Date</th>";
        echo "<th>Actions</th>";
        echo "</tr>";
        
        foreach ($subscriptions as $sub) {
            echo "<tr>";
            echo "<td>" . $sub['id'] . "</td>";
            echo "<td>" . htmlspecialchars($sub['username']) . "</td>";
            echo "<td>" . htmlspecialchars($sub['package_name']) . "</td>";
            echo "<td>";
            
            $statusColors = [
                'pending' => 'orange',
                'active' => 'green',
                'expired' => 'red',
                'cancelled' => 'gray'
            ];
            $color = $statusColors[$sub['status']] ?? 'black';
            echo "<span style='color: {$color};'>" . $sub['status'] . "</span>";
            echo "</td>";
            
            // Created At
            echo "<td>" . date('d/m/Y H:i', strtotime($sub['created_at'])) . "</td>";
            
            // Start Date
            echo "<td>";
            if ($sub['start_date']) {
                echo date('d/m/Y H:i', strtotime($sub['start_date']));
            } else {
                echo "<span style='color: orange;'>ไม่ได้ตั้งค่า</span>";
            }
            echo "</td>";
            
            // End Date
            echo "<td>";
            if ($sub['end_date']) {
                echo date('d/m/Y H:i', strtotime($sub['end_date']));
                
                // Calculate days left
                $days_left = ceil((strtotime($sub['end_date']) - time()) / (60 * 60 * 24));
                if ($sub['status'] === 'active') {
                    if ($days_left > 0) {
                        echo "<br><small style='color: green;'>เหลือ {$days_left} วัน</small>";
                    } else {
                        echo "<br><small style='color: red;'>หมดอายุแล้ว</small>";
                    }
                }
            } else {
                echo "<span style='color: orange;'>ไม่ได้ตั้งค่า</span>";
            }
            echo "</td>";
            
            // Actions
            echo "<td>";
            if (!$sub['start_date'] && $sub['status'] === 'pending') {
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='fix_start_date' value='{$sub['id']}'>";
                echo "<button type='submit' style='background: blue; color: white; padding: 3px 6px; border: none; cursor: pointer; font-size: 0.8rem;'>Fix Start Date</button>";
                echo "</form>";
            }
            echo "</td>";
            
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>ไม่พบ subscriptions</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle form submissions
if ($_POST) {
    echo "<h3>🔧 Action Results</h3>";
    
    if (isset($_POST['fix_start_date'])) {
        $subscription_id = (int)$_POST['fix_start_date'];
        
        try {
            $stmt = $pdo->prepare("
                UPDATE user_subscriptions 
                SET start_date = COALESCE(start_date, created_at)
                WHERE id = ? AND start_date IS NULL
            ");
            $result = $stmt->execute([$subscription_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                echo "<p>✅ <strong>Fixed start_date for subscription ID {$subscription_id}</strong></p>";
            } else {
                echo "<p>⚠️ No changes made for subscription ID {$subscription_id} (already has start_date or not found)</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error fixing start_date: " . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='?'>🔄 Refresh Page</a></p>";
    }
}

// Test date calculations
echo "<h3>📅 Date Calculation Tests</h3>";

$test_durations = [15, 30, 60, 90];
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Duration (Days)</th><th>Start Date</th><th>End Date</th><th>Days Difference</th></tr>";

foreach ($test_durations as $days) {
    $start_date = date('Y-m-d H:i:s');
    $end_date = date('Y-m-d H:i:s', strtotime("+{$days} days"));
    $calculated_days = ceil((strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24));
    
    echo "<tr>";
    echo "<td>{$days}</td>";
    echo "<td>" . date('d/m/Y H:i', strtotime($start_date)) . "</td>";
    echo "<td>" . date('d/m/Y H:i', strtotime($end_date)) . "</td>";
    echo "<td style='color: " . ($calculated_days == $days ? 'green' : 'red') . ";'>{$calculated_days}</td>";
    echo "</tr>";
}
echo "</table>";

// Test payment transactions
echo "<h3>💰 Recent Payment Transactions</h3>";

try {
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username, us.package_id
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        ORDER BY pt.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>User</th>";
        echo "<th>Amount</th>";
        echo "<th>Status</th>";
        echo "<th>Created At</th>";
        echo "<th>Paid At</th>";
        echo "<th>Time Diff</th>";
        echo "</tr>";
        
        foreach ($payments as $payment) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . number_format($payment['amount'], 2) . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($payment['created_at'])) . "</td>";
            echo "<td>";
            if ($payment['paid_at']) {
                echo date('d/m/Y H:i', strtotime($payment['paid_at']));
            } else {
                echo "<span style='color: orange;'>-</span>";
            }
            echo "</td>";
            echo "<td>";
            if ($payment['paid_at']) {
                $time_diff = strtotime($payment['paid_at']) - strtotime($payment['created_at']);
                $minutes = round($time_diff / 60);
                echo "{$minutes} นาที";
            } else {
                echo "-";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>ไม่พบ payment transactions</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Summary and recommendations
echo "<h3>📋 Summary & Recommendations</h3>";

echo "<div style='background: #f0f0f0; padding: 15px; border: 1px solid #ccc; margin: 10px 0;'>";
echo "<h4>Current Status:</h4>";
echo "<ul>";
echo "<li><strong>PHP Timezone:</strong> " . date_default_timezone_get() . "</li>";
echo "<li><strong>Current Time:</strong> " . date('d/m/Y H:i:s') . " (Thai format)</li>";
echo "<li><strong>Date Format:</strong> d/m/Y H:i (Thai standard)</li>";
echo "</ul>";

echo "<h4>Fixed Issues:</h4>";
echo "<ul>";
echo "<li>✅ Added start_date setting in cron jobs</li>";
echo "<li>✅ Fixed packages.php to set start_date when creating subscription</li>";
echo "<li>✅ Updated dashboard.php to handle null start_date/end_date</li>";
echo "<li>✅ All dates now display in Thai format (d/m/Y H:i)</li>";
echo "</ul>";

echo "<h4>Next Steps:</h4>";
echo "<ul>";
echo "<li>🔄 Deploy updated files to production</li>";
echo "<li>🧪 Test new package purchases</li>";
echo "<li>✅ Verify timezone consistency across all pages</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='/dashboard'>← กลับไปหน้า Dashboard</a></p>";
echo "<p><a href='/test_jellyfin_libraries.php'>🧪 Test Jellyfin Libraries</a></p>";
?>
