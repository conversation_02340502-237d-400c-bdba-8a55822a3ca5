$api_key = "6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446";
$record_key = "100568";
$api_url = "https://paynoi.com/api_line?api_key=" . urlencode($api_key) . "&record_key=" . urlencode($record_key);

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($response === false || $http_code != 200) {
    echo "Error: Failed to fetch data (HTTP $http_code)";
} else {
    $data = json_decode($response, true);
    if ($data['status'] === 'success') {
        foreach ($data['data'] as $transaction_group) {
            foreach ($transaction_group as $transaction) {
                echo "บัญชี: " . $transaction['bankaccount'] . "";
                echo "จำนวน: " . $transaction['amount'] . " " . $transaction['currency'] . "";
                echo "วันที่: " . $transaction['date'] . "";
                echo "ยอดคงเหลือ: " . $transaction['balance'] . "";
                echo "ประเภท: " . $transaction['type'] . "";
            }
        }
    } else {
        echo "Error: " . $data['status'];
    }
}