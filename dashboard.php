<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/JellyfinAPI.php';

// Require login
require_login();

// Get user information
$stmt = $pdo->prepare("
    SELECT u.*, ju.jellyfin_user_id, ju.jellyfin_username, ju.max_simultaneous_sessions, ju.jellyfin_last_activity
    FROM users u
    LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
    WHERE u.id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Get current subscription with slip_image from payment_transactions
$stmt = $pdo->prepare("
    SELECT us.*, p.name as package_name, p.duration_days, pt.slip_image
    FROM user_subscriptions us
    JOIN packages p ON us.package_id = p.id
    LEFT JOIN payment_transactions pt ON us.id = pt.subscription_id
    WHERE us.user_id = ? AND us.status IN ('active', 'pending')
    ORDER BY us.created_at DESC
    LIMIT 1
");
$stmt->execute([$_SESSION['user_id']]);
$current_subscription = $stmt->fetch();

// Get user's recent activity
$stmt = $pdo->prepare("
    SELECT action, details, created_at 
    FROM activity_logs 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id']]);
$recent_activity = $stmt->fetchAll();

// Get Jellyfin server status
$jellyfin_status = ['status' => 'unknown', 'version' => null, 'name' => 'Jellyfin Server'];
try {
    $jellyfin = new JellyfinAPI();
    $jellyfin_status = $jellyfin->getServerStatus();
} catch (Exception $e) {
    error_log('Failed to get Jellyfin status: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ด - Jellyfin by James</title>
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .dashboard-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #333;
            transition: all 0.3s ease;
        }
        .dashboard-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }
        .dashboard-card h3 {
            margin-bottom: 1rem;
            color: #fff;
            border-bottom: 1px solid #444;
            padding-bottom: 0.5rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-unknown { background-color: #ffc107; }
        .activity-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #333;
            font-size: 0.9rem;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-time {
            color: #888;
            font-size: 0.8rem;
        }
        .jellyfin-link {
            display: inline-block;
            margin-top: 1rem;
            padding: 10px 20px;
            background: linear-gradient(45deg, #00a4dc, #0078d4);
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .jellyfin-link:hover {
            background: linear-gradient(45deg, #0078d4, #005a9e);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 120, 212, 0.3);
        }
    </style>
</head>
<body>
        <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="/dashboard" class="nav-link active">หน้าหลัก</a>
                <a href="/packages" class="nav-link">แพ็คเกจ</a>
                <a href="/guide" class="nav-link">คู่มือ</a>
                <a href="/guide#line-group" class="nav-link" style="color: #00c300;">💬 LINE กลุ่ม</a>
                <?php if (is_admin()): ?>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="/admin/server" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="/admin/users" class="dropdown-item">ผู้ใช้</a>
                            <a href="/admin/packages" class="dropdown-item">แพ็คเกจ</a>
                            <a href="/admin/manage-packages" class="dropdown-item">จัดการแพ็คเกจ</a>
                            <a href="/admin/add-package" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="/admin/transactions" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="/admin/paynoi" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="/profile" class="dropdown-item">โปรไฟล์</a>
                        <a href="/referral" class="dropdown-item">แนะนำเพื่อน</a>
                        <a href="/logout" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">ยินดีต้อนรับ, <?php echo htmlspecialchars($user['username']); ?>!</h2>
            </div>

            <?php display_flash_message(); ?>

            <!-- Subscription Status -->
            <?php if (!$current_subscription): ?>
                <div class="dashboard-card fade-in-scroll" style="background: rgba(255, 193, 7, 0.1); border-color: #ffc107; text-align: center;">
                    <h3 style="color: #ffc107;">ยังไม่มีแพ็คเกจ</h3>
                    <p>บัญชีของคุณยังไม่ได้เปิดใช้งาน กรุณาเลือกแพ็คเกจเพื่อเริ่มใช้งาน</p>
                    <a href="/packages" class="btn btn-primary noir-button" style="margin-top: 1rem;">
                        เลือกแพ็คเกจ
                    </a>
                </div>
            <?php elseif ($current_subscription['status'] === 'pending'): ?>
                <div class="dashboard-card fade-in-scroll" style="background: rgba(255, 193, 7, 0.1); border-color: #ffc107; text-align: center;">
                    <h3 style="color: #ffc107;">รอการชำระเงิน</h3>
                    <p><strong><?php echo htmlspecialchars($current_subscription['package_name']); ?></strong></p>
                    <p>จำนวนเงิน: <?php echo number_format($current_subscription['payment_amount'], 2); ?> บาท</p>
                    <a href="/payment?subscription_id=<?php echo $current_subscription['id']; ?>" class="btn btn-primary noir-button" style="margin-top: 1rem;">
                        <?php echo !empty($current_subscription['slip_image']) ? 'ตรวจสอบสถานะ' : 'ดำเนินการชำระเงิน'; ?>
                    </a>
                </div>
            <?php else: ?>
                <div class="dashboard-card fade-in-scroll" style="background: rgba(40, 167, 69, 0.1); border-color: #28a745; text-align: center;">
                    <h3 style="color: #28a745;">แพ็คเกจปัจจุบัน</h3>
                    <p><strong><?php echo htmlspecialchars($current_subscription['package_name']); ?></strong></p>
                    <p>เริ่มต้น: <?php echo date('d/m/Y H:i', strtotime($current_subscription['start_date'])); ?></p>
                    <p>หมดอายุ: <?php echo date('d/m/Y H:i', strtotime($current_subscription['end_date'])); ?></p>
                    <?php
                    $days_left = ceil((strtotime($current_subscription['end_date']) - time()) / (60 * 60 * 24));
                    if ($days_left > 0):
                    ?>
                        <p><strong>เหลือเวลา: <?php echo $days_left; ?> วัน</strong></p>
                    <?php else: ?>
                        <p style="color: #dc3545;"><strong>หมดอายุแล้ว</strong></p>
                    <?php endif; ?>
                    <a href="/packages" class="btn btn-primary noir-button" style="margin-top: 1rem;">
                        ต่ออายุ
                    </a>
                </div>
            <?php endif; ?>

            <div class="dashboard-grid">
                <!-- Account Information -->
                <div class="dashboard-card fade-in-scroll">
                    <h3>ข้อมูลบัญชี</h3>
                    <p><strong>ชื่อผู้ใช้:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                    <p><strong>เบอร์โทรศัพท์:</strong> <?php echo htmlspecialchars($user['phone'] ?? 'ไม่ระบุ'); ?></p>
                    <p><strong>ประเภทบัญชี:</strong> <?php echo $user['is_admin'] ? 'ผู้ดูแลระบบ' : 'ผู้ใช้งาน'; ?></p>
                    <p><strong>สมาชิกตั้งแต่:</strong> <?php echo date('d/m/Y', strtotime($user['created_at'])); ?></p>
                    <?php if ($user['last_login']): ?>
                        <p><strong>เข้าสู่ระบบล่าสุด:</strong> <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?></p>
                    <?php endif; ?>
                </div>

                <!-- Jellyfin Status -->
                <div class="dashboard-card fade-in-scroll">
                    <h3>สถานะเซิร์ฟเวอร์ Jellyfin</h3>
                    <p>
                        <span class="status-indicator status-<?php echo $jellyfin_status['status']; ?>"></span>
                        <strong>สถานะ:</strong> <?php echo ucfirst($jellyfin_status['status']); ?>
                    </p>
                    <p><strong>ชื่อเซิร์ฟเวอร์:</strong> <?php echo htmlspecialchars($jellyfin_status['name']); ?></p>

                    <?php if ($jellyfin_status['status'] === 'running' && $current_subscription && $current_subscription['status'] === 'active'): ?>
                        <a href="<?php echo JELLYFIN_SERVER_URL; ?>" target="_blank" class="jellyfin-link play-trailer-btn">
                            เปิด Jellyfin Web Client
                        </a>
                    <?php elseif (!$current_subscription || $current_subscription['status'] !== 'active'): ?>
                        <p style="color: #ffc107; font-size: 0.9rem;">กรุณาเลือกแพ็คเกจและชำระเงินเพื่อเข้าใช้งาน Jellyfin</p>
                    <?php endif; ?>
                </div>

                <!-- Jellyfin Account -->
                <?php if ($user['jellyfin_user_id']): ?>
                <div class="dashboard-card fade-in-scroll">
                    <h3>บัญชี Jellyfin</h3>
                    <p><strong>ชื่อผู้ใช้ Jellyfin:</strong> <?php echo htmlspecialchars($user['jellyfin_username']); ?></p>
                    <p><strong>จำนวนเซสชันสูงสุด:</strong> <?php echo $user['max_simultaneous_sessions']; ?></p>
                    <?php if ($user['jellyfin_last_activity']): ?>
                        <p><strong>กิจกรรมล่าสุด:</strong> <?php echo date('d/m/Y H:i', strtotime($user['jellyfin_last_activity'])); ?></p>
                    <?php endif; ?>
                    <p><strong>สร้างบัญชีเมื่อ:</strong> <?php echo date('d/m/Y', strtotime($user['created_at'])); ?></p>
                </div>
                <?php else: ?>
                <div class="dashboard-card fade-in-scroll">
                    <h3>บัญชี Jellyfin</h3>
                    <p style="color: #ffc107;">บัญชี Jellyfin ของคุณกำลังถูกตั้งค่า กรุณาติดต่อผู้ดูแลระบบหากปัญหานี้ยังคงอยู่</p>
                </div>
                <?php endif; ?>

                <!-- Recent Activity -->
                <div class="dashboard-card fade-in-scroll">
                    <h3>กิจกรรมล่าสุด</h3>
                    <?php if (!empty($recent_activity)): ?>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <?php foreach ($recent_activity as $activity): ?>
                                <div class="activity-item">
                                    <div><?php echo htmlspecialchars($activity['action']); ?></div>
                                    <?php if ($activity['details']): ?>
                                        <div style="color: #ccc; font-size: 0.8rem;"><?php echo htmlspecialchars($activity['details']); ?></div>
                                    <?php endif; ?>
                                    <div class="activity-time"><?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p style="color: #888;">ไม่มีกิจกรรมล่าสุด</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="hero-section" style="padding: 2rem 0;">
                <h3 class="fade-in-up">การดำเนินการด่วน</h3>
                <div class="hero-actions fade-in-up delay-1">
                    <a href="/profile" class="btn btn-secondary noir-button">แก้ไขโปรไฟล์</a>
                    <?php if ($jellyfin_status['status'] === 'running'): ?>
                        <a href="<?php echo JELLYFIN_SERVER_URL; ?>" target="_blank" class="btn btn-primary noir-button">เปิด Jellyfin</a>
                    <?php endif; ?>
                    <?php if (is_admin()): ?>
                        <a href="admin/server-control.php" class="btn btn-primary noir-button">จัดการเซิร์ฟเวอร์</a>
                        <a href="admin/paynoi-transactions.php" class="btn btn-secondary noir-button">ธุรกรรม PayNoi</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/dropdown.js"></script>
</body>
</html>
