<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require admin access
require_login();
require_admin();

// Connect to database
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

$errors = [];
$success = false;
$package_id = (int)($_GET['id'] ?? 0);

// Get package details
if ($package_id > 0) {
    $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
    $stmt->execute([$package_id]);
    $package = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$package) {
        header('Location: packages.php?error=Package not found');
        exit();
    }
} else {
    header('Location: packages.php?error=Invalid package ID');
    exit();
}

// Handle form submission
if ($_POST) {
    $name = sanitize_input($_POST['name']);
    $duration_days = (int)$_POST['duration_days'];
    $price = (float)$_POST['price'];
    $max_sessions = (int)$_POST['max_sessions'];
    $description = sanitize_input($_POST['description']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validation
    if (empty($name)) {
        $errors[] = 'ชื่อแพ็คเกจจำเป็นต้องใส่';
    }
    
    if ($duration_days <= 0) {
        $errors[] = 'ระยะเวลาต้องมากกว่า 0 วัน';
    }
    
    if ($price <= 0) {
        $errors[] = 'ราคาต้องมากกว่า 0 บาท';
    }
    
    if ($max_sessions <= 0) {
        $errors[] = 'จำนวน Max Sessions ต้องมากกว่า 0';
    }
    
    // Check if price already exists for other packages
    if (empty($errors)) {
        $stmt = $pdo->prepare("SELECT id FROM packages WHERE price = ? AND id != ?");
        $stmt->execute([$price, $package_id]);
        if ($stmt->fetch()) {
            $errors[] = 'ราคานี้มีอยู่ในแพ็คเกจอื่นแล้ว';
        }
    }
    
    // Update package if no errors
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                UPDATE packages 
                SET name = ?, duration_days = ?, price = ?, max_sessions = ?, description = ?, is_active = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$name, $duration_days, $price, $max_sessions, $description, $is_active, $package_id]);
            
            // Log activity
            log_activity($_SESSION['user_id'], 'package_updated', "Updated package: {$name} (ID: {$package_id})");
            
            $success = true;
            
            // Refresh package data
            $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
            $stmt->execute([$package_id]);
            $package = $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $errors[] = 'เกิดข้อผิดพลาดในการอัปเดตแพ็คเกจ: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขแพ็คเกจ - Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .edit-form {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #fff;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            font-size: 1rem;
        }
        .form-control:focus {
            outline: none;
            border-color: #ffc107;
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .btn-group {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #ffc107;
            color: #000;
        }
        .btn-primary:hover {
            background: #e0a800;
        }
        .btn-secondary {
            background: #6c757d;
            color: #fff;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        .package-info {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="main-header">
            <div class="container">
                <div class="logo">Jellyfin Admin</div>
                <nav class="main-nav">
                    <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                    <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                    <div class="admin-dropdown" id="adminDropdown">
                        <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                        <div class="dropdown-menu">
                            <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                            <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                            <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                            <a href="manage-packages.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">จัดการแพ็คเกจ</a>
                            <a href="add-package.php" class="dropdown-item">เพิ่มแพ็คเกจ</a>
                            <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                            <a href="paynoi-transactions.php" class="dropdown-item">PayNoi</a>
                        </div>
                    </div>
                    <div class="profile-dropdown" id="profileDropdown">
                        <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                        <div class="dropdown-menu">
                            <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                            <a href="../referral.php" class="dropdown-item">แนะนำเพื่อน</a>
                            <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                        </div>
                    </div>
                </nav>
            </div>
        </header>

        <main class="main-content">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">แก้ไขแพ็คเกจ</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">แก้ไขรายละเอียดแพ็คเกจ</p>
            </div>

            <div class="fade-in-scroll">
                <div class="package-info">
                    <h4 style="color: #ffc107; margin-bottom: 0.5rem;">📦 แพ็คเกจปัจจุบัน</h4>
                    <p style="margin: 0; color: #ccc;">
                        <strong>ID:</strong> <?php echo $package['id']; ?> | 
                        <strong>สร้างเมื่อ:</strong> <?php echo date('d/m/Y H:i', strtotime($package['created_at'])); ?> |
                        <strong>อัปเดตล่าสุด:</strong> <?php echo date('d/m/Y H:i', strtotime($package['updated_at'])); ?>
                    </p>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        ✅ อัปเดตแพ็คเกจเรียบร้อยแล้ว!
                    </div>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="POST" class="edit-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name">ชื่อแพ็คเกจ *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($package['name']); ?>" 
                                   required placeholder="เช่น 30 วัน">
                        </div>

                        <div class="form-group">
                            <label for="duration_days">ระยะเวลา (วัน) *</label>
                            <input type="number" id="duration_days" name="duration_days" class="form-control" 
                                   value="<?php echo $package['duration_days']; ?>" 
                                   required min="1" placeholder="30">
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="price">ราคา (บาท) *</label>
                            <input type="number" id="price" name="price" class="form-control" 
                                   value="<?php echo $package['price']; ?>" 
                                   required min="0.01" step="0.01" placeholder="100.00">
                        </div>

                        <div class="form-group">
                            <label for="max_sessions">Max Sessions (อุปกรณ์) *</label>
                            <input type="number" id="max_sessions" name="max_sessions" class="form-control" 
                                   value="<?php echo $package['max_sessions']; ?>" 
                                   required min="1" placeholder="1">
                            <small style="color: #888; font-size: 0.8rem;">จำนวนอุปกรณ์ที่สามารถดูพร้อมกันได้</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">คำอธิบาย</label>
                        <textarea id="description" name="description" class="form-control" 
                                  rows="3" placeholder="คำอธิบายแพ็คเกจ"><?php echo htmlspecialchars($package['description']); ?></textarea>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_active" name="is_active" 
                                   <?php echo $package['is_active'] ? 'checked' : ''; ?>>
                            <label for="is_active">เปิดใช้งานแพ็คเกจ</label>
                        </div>
                        <small style="color: #888; font-size: 0.8rem;">ถ้าปิดใช้งาน ผู้ใช้จะไม่เห็นแพ็คเกจนี้</small>
                    </div>

                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">💾 บันทึกการเปลี่ยนแปลง</button>
                        <a href="manage-packages.php" class="btn btn-secondary">❌ ยกเลิก</a>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
