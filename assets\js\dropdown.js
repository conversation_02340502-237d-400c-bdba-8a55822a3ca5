/**
 * Simple Dropdown functionality for navbar
 */

// Global function for inline onclick
function toggleDropdown(dropdownId) {
    console.log('toggleDropdown called with:', dropdownId);

    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) {
        console.error('Dropdown not found:', dropdownId);
        return;
    }

    console.log('Found dropdown element:', dropdown);

    // Close all other dropdowns first
    document.querySelectorAll('.profile-dropdown, .admin-dropdown').forEach(d => {
        const menu = d.querySelector('.dropdown-menu');
        if (d.id !== dropdownId && menu) {
            menu.style.display = 'none';
            d.classList.remove('active');
        }
    });

    // Get the menu for current dropdown
    const menu = dropdown.querySelector('.dropdown-menu');
    if (!menu) {
        console.error('No dropdown menu found in:', dropdown);
        return;
    }

    // Toggle current dropdown
    const isCurrentlyVisible = menu.style.display === 'block';

    if (isCurrentlyVisible) {
        menu.style.display = 'none';
        dropdown.classList.remove('active');
        console.log('Closed dropdown:', dropdownId);
    } else {
        // Enhanced positioning for all screen sizes
        const rect = dropdown.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Force absolute positioning to break out of navbar constraints
        menu.style.position = 'fixed';
        menu.style.zIndex = '999999';
        menu.style.top = (rect.bottom + 8) + 'px';

        // Position based on dropdown type and available space
        if (dropdown.classList.contains('admin-dropdown')) {
            // Admin dropdown - align to left
            const leftPos = Math.max(10, rect.left);
            menu.style.left = leftPos + 'px';
            menu.style.right = 'auto';
        } else {
            // Profile dropdown - align to right
            const rightPos = Math.max(10, viewportWidth - rect.right);
            menu.style.right = rightPos + 'px';
            menu.style.left = 'auto';
        }

        // Ensure dropdown doesn't go off-screen vertically
        const menuHeight = menu.offsetHeight || 200; // estimate if not rendered yet
        if (rect.bottom + menuHeight + 16 > viewportHeight) {
            menu.style.top = (rect.top - menuHeight - 8) + 'px';
        }

        // Force visibility
        menu.style.display = 'block';
        menu.style.visibility = 'visible';
        menu.style.opacity = '1';
        menu.style.transform = 'none';
        menu.style.clip = 'unset';
        menu.style.clipPath = 'none';

        dropdown.classList.add('active');
        console.log('Opened dropdown:', dropdownId, 'at position:', menu.style.top, menu.style.left || menu.style.right);
    }

    console.log('Menu display is now:', menu.style.display);
}

// Close all dropdowns
function closeAllDropdowns() {
    document.querySelectorAll('.profile-dropdown, .admin-dropdown').forEach(d => {
        const menu = d.querySelector('.dropdown-menu');
        if (menu) {
            menu.style.display = 'none';
            menu.style.visibility = 'hidden';
            menu.style.opacity = '0';
            // Reset positioning
            menu.style.position = '';
            menu.style.top = '';
            menu.style.left = '';
            menu.style.right = '';
            menu.style.zIndex = '';
        }
        d.classList.remove('active');
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dropdown script loaded and DOM ready');

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.profile-dropdown') && !e.target.closest('.admin-dropdown')) {
            closeAllDropdowns();
        }
    });

    // Close dropdowns with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllDropdowns();
        }
    });

    // Prevent dropdown menu clicks from closing
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Handle window resize to reset dropdown positioning
    window.addEventListener('resize', function() {
        closeAllDropdowns();
    });

    console.log('Dropdown initialization complete');
});
