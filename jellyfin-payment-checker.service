[Unit]
Description=Jellyfin by James - Continuous Payment Checker
Documentation=https://embyjames.xyz
After=network.target mysql.service
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/jellyfin
ExecStart=/usr/bin/python3 /opt/jellyfin/continuous_payment_checker.py daemon
ExecReload=/bin/kill -HUP $MAINPID
KillMode=process
Restart=always
RestartSec=10
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/jellyfin /var/run

# Resource limits
LimitNOFILE=65536
MemoryMax=256M
CPUQuota=50%

# Environment
Environment=PYTHONUNBUFFERED=1
Environment=TZ=Asia/Bangkok

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=jellyfin-payment-checker

[Install]
WantedBy=multi-user.target
