<?php
/**
 * API Health Check Endpoint
 * สำหรับ AutoPaymentChecker Production Mode
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Include required files - use production config
    if (file_exists('../config/database_production.php')) {
        require_once '../config/database_production.php';
    } else {
        require_once '../includes/config.php';
    }
    
    $health_status = [
        'status' => 'healthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'server_time' => time(),
        'checks' => []
    ];
    
    // Check database connection
    try {
        $stmt = $pdo->query("SELECT 1");
        $health_status['checks']['database'] = [
            'status' => 'ok',
            'message' => 'Database connection successful'
        ];
    } catch (Exception $e) {
        $health_status['checks']['database'] = [
            'status' => 'error',
            'message' => 'Database connection failed: ' . $e->getMessage()
        ];
        $health_status['status'] = 'unhealthy';
    }
    
    // Check if payment_transaction table exists
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'payment_transaction'");
        if ($stmt->rowCount() > 0) {
            $health_status['checks']['payment_table'] = [
                'status' => 'ok',
                'message' => 'Payment transaction table exists'
            ];
        } else {
            $health_status['checks']['payment_table'] = [
                'status' => 'warning',
                'message' => 'Payment transaction table not found'
            ];
        }
    } catch (Exception $e) {
        $health_status['checks']['payment_table'] = [
            'status' => 'error',
            'message' => 'Error checking payment table: ' . $e->getMessage()
        ];
    }
    
    // Check if users table exists
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            $health_status['checks']['users_table'] = [
                'status' => 'ok',
                'message' => 'Users table exists'
            ];
        } else {
            $health_status['checks']['users_table'] = [
                'status' => 'warning',
                'message' => 'Users table not found'
            ];
        }
    } catch (Exception $e) {
        $health_status['checks']['users_table'] = [
            'status' => 'error',
            'message' => 'Error checking users table: ' . $e->getMessage()
        ];
    }
    
    // Check PayNoi API connectivity
    try {
        $paynoi_url = "https://paynoi.com/api_line?api_key=6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446&record_key=100568";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($paynoi_url, false, $context);
        
        if ($response !== false) {
            $health_status['checks']['paynoi_api'] = [
                'status' => 'ok',
                'message' => 'PayNoi API accessible'
            ];
        } else {
            $health_status['checks']['paynoi_api'] = [
                'status' => 'warning',
                'message' => 'PayNoi API not accessible'
            ];
        }
    } catch (Exception $e) {
        $health_status['checks']['paynoi_api'] = [
            'status' => 'error',
            'message' => 'PayNoi API check failed: ' . $e->getMessage()
        ];
    }
    
    // Check uploads directory
    $uploads_dir = '../uploads/slips';
    if (is_dir($uploads_dir) && is_writable($uploads_dir)) {
        $health_status['checks']['uploads_dir'] = [
            'status' => 'ok',
            'message' => 'Uploads directory accessible and writable'
        ];
    } else {
        $health_status['checks']['uploads_dir'] = [
            'status' => 'warning',
            'message' => 'Uploads directory not accessible or not writable'
        ];
    }
    
    // Check PHP version
    $php_version = PHP_VERSION;
    if (version_compare($php_version, '7.4.0', '>=')) {
        $health_status['checks']['php_version'] = [
            'status' => 'ok',
            'message' => "PHP version {$php_version} is supported"
        ];
    } else {
        $health_status['checks']['php_version'] = [
            'status' => 'warning',
            'message' => "PHP version {$php_version} may not be fully supported"
        ];
    }
    
    // Add system info
    $health_status['system_info'] = [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown'
    ];
    
    echo json_encode($health_status, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Health check failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
