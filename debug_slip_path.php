<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
require_login();

echo "<h2>🔍 Debug Slip Path Issue</h2>";

try {
    // Get payment with slip image
    $stmt = $pdo->prepare("
        SELECT pt.*, u.username
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        WHERE pt.slip_image IS NOT NULL
        AND pt.slip_image != ''
        ORDER BY pt.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $payments = $stmt->fetchAll();
    
    echo "<h3>📋 Recent Payments with Slips:</h3>";
    
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Slip Image (Database)</th><th>File Exists</th><th>Corrected Path</th></tr>";
        
        foreach ($payments as $payment) {
            $dbSlipPath = $payment['slip_image'];
            
            // Test different path combinations
            $testPaths = [
                $dbSlipPath,                                    // As stored in DB
                "uploads/slips/" . $dbSlipPath,                // Add prefix
                str_replace("uploads/slips/uploads/slips/", "uploads/slips/", $dbSlipPath), // Remove duplicate
                basename($dbSlipPath),                          // Just filename
                "uploads/slips/" . basename($dbSlipPath)        // Prefix + filename only
            ];
            
            $foundPath = null;
            foreach ($testPaths as $testPath) {
                if (file_exists($testPath)) {
                    $foundPath = $testPath;
                    break;
                }
            }
            
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['username']) . "</td>";
            echo "<td>" . htmlspecialchars($dbSlipPath) . "</td>";
            echo "<td>" . ($foundPath ? '✅ ' . $foundPath : '❌ Not found') . "</td>";
            
            // Suggest corrected path
            $correctPath = basename($dbSlipPath);
            echo "<td>uploads/slips/" . $correctPath . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>🔧 Fix Slip Paths:</h3>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='fix_slip_paths' value='1'>";
        echo "<button type='submit' style='background: orange; color: white; padding: 10px; border: none; cursor: pointer;'>Fix All Slip Paths</button>";
        echo "</form>";
        
    } else {
        echo "<p>❌ No payments with slip images found</p>";
    }
    
    echo "<h3>📁 Directory Check:</h3>";
    $uploadDir = "uploads/slips/";
    if (is_dir($uploadDir)) {
        $files = scandir($uploadDir);
        $imageFiles = array_filter($files, function($file) {
            return preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
        });
        
        echo "<p>✅ Upload directory exists: {$uploadDir}</p>";
        echo "<p>📁 Image files found: " . count($imageFiles) . "</p>";
        
        if (count($imageFiles) > 0) {
            echo "<h4>Recent files:</h4>";
            echo "<ul>";
            $recentFiles = array_slice($imageFiles, -10); // Last 10 files
            foreach ($recentFiles as $file) {
                $filePath = $uploadDir . $file;
                $fileSize = filesize($filePath);
                $fileTime = date('Y-m-d H:i:s', filemtime($filePath));
                echo "<li>{$file} ({$fileSize} bytes, {$fileTime})</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ Upload directory not found: {$uploadDir}</p>";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle fix slip paths
if ($_POST && isset($_POST['fix_slip_paths'])) {
    try {
        echo "<h3>🔧 Fixing Slip Paths:</h3>";
        
        // Get all payments with slip images
        $stmt = $pdo->prepare("
            SELECT id, slip_image FROM payment_transactions
            WHERE slip_image IS NOT NULL AND slip_image != ''
        ");
        $stmt->execute();
        $allPayments = $stmt->fetchAll();
        
        $fixedCount = 0;
        
        foreach ($allPayments as $payment) {
            $currentPath = $payment['slip_image'];
            
            // Check if path has duplicate uploads/slips/
            if (strpos($currentPath, 'uploads/slips/uploads/slips/') !== false) {
                // Remove duplicate prefix
                $newPath = str_replace('uploads/slips/uploads/slips/', 'uploads/slips/', $currentPath);
                
                // Update database
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET slip_image = ?
                    WHERE id = ?
                ");
                $updateStmt->execute([$newPath, $payment['id']]);
                
                echo "<p>✅ Fixed payment ID {$payment['id']}: {$currentPath} → {$newPath}</p>";
                $fixedCount++;
                
            } elseif (strpos($currentPath, 'uploads/slips/') !== 0) {
                // Add missing prefix if it's just a filename
                $newPath = 'uploads/slips/' . basename($currentPath);
                
                // Update database
                $updateStmt = $pdo->prepare("
                    UPDATE payment_transactions
                    SET slip_image = ?
                    WHERE id = ?
                ");
                $updateStmt->execute([$newPath, $payment['id']]);
                
                echo "<p>✅ Fixed payment ID {$payment['id']}: {$currentPath} → {$newPath}</p>";
                $fixedCount++;
            }
        }
        
        echo "<p><strong>✅ Fixed {$fixedCount} slip paths</strong></p>";
        echo "<p><a href='?'>Refresh Page</a></p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error fixing slip paths: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='/test_slip_verification.php'>← กลับไปหน้า Test Slip Verification</a></p>";
?>
