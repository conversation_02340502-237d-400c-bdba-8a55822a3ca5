<?php
/**
 * Cron Job: Check PayNoi transactions and verify payments
 * Run this script every 5-10 minutes to check for new transactions
 * 
 * Usage: php cron/check_transactions.php
 * Or set up as cron job: */5 * * * * /usr/bin/php /path/to/your/site/cron/check_transactions.php
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

// Set working directory
$rootDir = dirname(__DIR__);
chdir($rootDir);

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/PayNoiAPIClass.php';
require_once 'includes/JellyfinAPI.php';

// Log start
$logFile = 'logs/transaction_check.log';
if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message\n";
}

writeLog("Starting transaction check...");

try {
    // Check if required tables exist
    $tables = ['payment_transactions', 'user_subscriptions', 'users', 'packages'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            writeLog("ERROR: Table '$table' does not exist");
            exit(1);
        }
    }

    // Check if slip_image column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM payment_transactions LIKE 'slip_image'");
    if ($stmt->rowCount() == 0) {
        writeLog("ERROR: Column 'slip_image' does not exist in payment_transactions table");
        exit(1);
    }

    // Initialize PayNoi API
    try {
        $paynoiAPI = new PayNoiAPI();
        writeLog("PayNoi API initialized successfully");
    } catch (Exception $e) {
        writeLog("ERROR: Failed to initialize PayNoi API: " . $e->getMessage());
        exit(1);
    }

    // Get pending payments that have slip uploaded and confirmed by user
    $stmt = $pdo->prepare("
        SELECT pt.*, us.user_id, us.package_id, us.id as subscription_id,
               u.username, ju.jellyfin_user_id, p.duration_days
        FROM payment_transactions pt
        JOIN user_subscriptions us ON pt.subscription_id = us.id
        JOIN users u ON pt.user_id = u.id
        LEFT JOIN jellyfin_users ju ON u.id = ju.user_id
        JOIN packages p ON us.package_id = p.id
        WHERE pt.status = 'pending'
        AND pt.slip_image IS NOT NULL
        AND us.status = 'pending'
        AND pt.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY pt.created_at ASC
    ");
    $stmt->execute();
    $pendingPayments = $stmt->fetchAll();
    
    writeLog("Found " . count($pendingPayments) . " pending payments to check");
    
    if (empty($pendingPayments)) {
        writeLog("No pending payments found. Exiting.");
        exit(0);
    }
    
    // Get recent transactions from PayNoi
    $transactions = $paynoiAPI->getRecentTransactions();
    
    if (!$transactions) {
        writeLog("Failed to fetch transactions from PayNoi API");
        exit(1);
    }
    
    writeLog("Fetched transactions from PayNoi API successfully");
    
    $processedCount = 0;
    $approvedCount = 0;
    
    foreach ($pendingPayments as $payment) {
        writeLog("Checking payment ID {$payment['id']} for user {$payment['username']} - Amount: {$payment['amount']} THB");
        
        // Find matching transaction
        $matchingTransaction = $paynoiAPI->findMatchingTransaction($payment);
        
        if ($matchingTransaction) {
            writeLog("Found matching transaction for payment ID {$payment['id']}");
            
            try {
                $pdo->beginTransaction();
                
                // Update payment transaction status
                $stmt = $pdo->prepare("
                    UPDATE payment_transactions 
                    SET status = 'completed', paid_at = NOW(), updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$payment['id']]);
                
                // Update subscription status
                $startDate = date('Y-m-d H:i:s');
                $endDate = date('Y-m-d H:i:s', strtotime("+{$payment['duration_days']} days"));
                
                $stmt = $pdo->prepare("
                    UPDATE user_subscriptions 
                    SET status = 'active', start_date = ?, end_date = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$startDate, $endDate, $payment['subscription_id']]);
                
                // Enable user in Jellyfin and grant access to all libraries
                if ($payment['jellyfin_user_id']) {
                    try {
                        $jellyfin = new JellyfinAPI();
                        $jellyfin->enableAllLibraries($payment['jellyfin_user_id']);
                        writeLog("Enabled Jellyfin user and granted access to all libraries for {$payment['username']}");
                    } catch (Exception $e) {
                        writeLog("Failed to enable Jellyfin user for {$payment['username']}: " . $e->getMessage());
                    }
                }
                
                // Log activity
                $stmt = $pdo->prepare("
                    INSERT INTO activity_logs (user_id, action, details, created_at) 
                    VALUES (?, 'payment_auto_approved', ?, NOW())
                ");
                $stmt->execute([
                    $payment['user_id'], 
                    "Payment automatically approved via PayNoi API verification - Amount: {$payment['amount']} THB"
                ]);
                
                // Log server activity
                $stmt = $pdo->prepare("
                    INSERT INTO server_logs (action, status, message, user_id, created_at) 
                    VALUES ('payment_auto_approved', 'success', ?, ?, NOW())
                ");
                $stmt->execute([
                    "Auto-approved payment for user {$payment['username']} - Amount: {$payment['amount']} THB",
                    $payment['user_id']
                ]);
                
                $pdo->commit();
                
                writeLog("Successfully approved payment ID {$payment['id']} for user {$payment['username']}");
                $approvedCount++;
                
            } catch (Exception $e) {
                $pdo->rollBack();
                writeLog("Failed to approve payment ID {$payment['id']}: " . $e->getMessage());
            }
        } else {
            writeLog("No matching transaction found for payment ID {$payment['id']}");
            
            // Check if payment is older than 2 hours and mark as needs manual review
            $paymentAge = time() - strtotime($payment['created_at']);
            if ($paymentAge > 7200) { // 2 hours
                try {
                    $stmt = $pdo->prepare("
                        UPDATE payment_transactions 
                        SET status = 'manual_review', updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$payment['id']]);
                    
                    writeLog("Marked payment ID {$payment['id']} for manual review (older than 2 hours)");
                } catch (Exception $e) {
                    writeLog("Failed to mark payment ID {$payment['id']} for manual review: " . $e->getMessage());
                }
            }
        }
        
        $processedCount++;
    }
    
    writeLog("Transaction check completed. Processed: $processedCount, Approved: $approvedCount");
    
} catch (Exception $e) {
    writeLog("Error during transaction check: " . $e->getMessage());
    exit(1);
}

writeLog("Transaction check finished successfully");
exit(0);
?>
