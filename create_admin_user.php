<?php
/**
 * สคริปต์สำหรับสร้าง Admin User ในฐานข้อมูล jellyfin_registration
 */

require_once 'includes/config.php';

echo "<h1>🔧 สร้าง Admin User</h1>";

if ($_POST) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $phone = $_POST['phone'] ?? '';
    
    if (empty($username) || empty($password)) {
        echo "<p style='color: red;'>❌ กรุณาใส่ username และ password</p>";
    } else {
        try {
            // ตรวจสอบว่ามี username นี้แล้วหรือไม่
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            
            if ($stmt->fetch()) {
                echo "<p style='color: orange;'>⚠️ Username นี้มีอยู่แล้ว</p>";
            } else {
                // สร้าง admin user
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("INSERT INTO users (username, password, phone, is_admin, status, created_at) VALUES (?, ?, ?, 1, 'active', NOW())");
                $stmt->execute([$username, $hashed_password, $phone]);
                
                echo "<div style='background: #d4edda; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>";
                echo "<h3 style='color: #155724;'>✅ สร้าง Admin User สำเร็จ!</h3>";
                echo "<p><strong>Username:</strong> {$username}</p>";
                echo "<p><strong>Password:</strong> {$password}</p>";
                echo "<p><strong>Phone:</strong> {$phone}</p>";
                echo "<p><strong>Admin Status:</strong> ✅ Enabled (is_admin = 1)</p>";
                echo "<p><strong>Password Hash:</strong> " . substr($hashed_password, 0, 30) . "...</p>";
                echo "<p style='color: #856404;'>⚠️ กรุณาจดบันทึกข้อมูลนี้ไว้</p>";
                echo "</div>";

                echo "<div style='background: #cce7ff; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>";
                echo "<h3>🚀 ขั้นตอนถัดไป:</h3>";
                echo "<ol>";
                echo "<li>เปิด Admin Tools (JellyfinAdminTools.exe)</li>";
                echo "<li>ใช้ username: <strong>{$username}</strong></li>";
                echo "<li>ใช้ password: <strong>{$password}</strong></li>";
                echo "<li>คลิก เข้าสู่ระบบ</li>";
                echo "</ol>";
                echo "</div>";

                echo "<div style='background: #d1ecf1; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>";
                echo "<h3>🔐 ข้อมูลความปลอดภัย:</h3>";
                echo "<ul>";
                echo "<li>Password ถูกเข้ารหัสด้วย PHP password_hash()</li>";
                echo "<li>ใช้ algorithm: " . PASSWORD_DEFAULT . "</li>";
                echo "<li>Password จะถูกตรวจสอบด้วย password_verify()</li>";
                echo "</ul>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
        }
    }
}

// แสดงรายการ admin ที่มีอยู่
echo "<h2>👥 Admin Users ที่มีอยู่</h2>";

try {
    $stmt = $pdo->prepare("SELECT id, username, phone, created_at FROM users WHERE is_admin = 1 ORDER BY created_at DESC");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "<p style='color: orange;'>⚠️ ยังไม่มี Admin User ในระบบ</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>Username</th>";
        echo "<th style='padding: 10px;'>Phone</th>";
        echo "<th style='padding: 10px;'>Created</th>";
        echo "</tr>";
        
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>{$admin['id']}</td>";
            echo "<td style='padding: 10px;'><strong>{$admin['username']}</strong></td>";
            echo "<td style='padding: 10px;'>{$admin['phone']}</td>";
            echo "<td style='padding: 10px;'>{$admin['created_at']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถดึงข้อมูล admin ได้: " . $e->getMessage() . "</p>";
}
?>

<h2>➕ สร้าง Admin User ใหม่</h2>

<form method="POST" style="background: #f8f9fa; padding: 2rem; border-radius: 8px; max-width: 500px;">
    <div style="margin-bottom: 1rem;">
        <label for="username"><strong>Username:</strong></label><br>
        <input type="text" name="username" id="username" required 
               style="width: 100%; padding: 0.5rem; margin-top: 0.5rem; border: 1px solid #ddd; border-radius: 4px;"
               placeholder="เช่น admin, administrator">
    </div>
    
    <div style="margin-bottom: 1rem;">
        <label for="password"><strong>Password:</strong></label><br>
        <input type="password" name="password" id="password" required 
               style="width: 100%; padding: 0.5rem; margin-top: 0.5rem; border: 1px solid #ddd; border-radius: 4px;"
               placeholder="รหัสผ่านที่ปลอดภัย">
    </div>
    
    <div style="margin-bottom: 1rem;">
        <label for="phone"><strong>Phone (ไม่บังคับ):</strong></label><br>
        <input type="text" name="phone" id="phone" 
               style="width: 100%; padding: 0.5rem; margin-top: 0.5rem; border: 1px solid #ddd; border-radius: 4px;"
               placeholder="เช่น 0812345678">
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer;">
        🔧 สร้าง Admin User
    </button>
</form>

<div style="background: #fff3cd; padding: 1rem; border-radius: 8px; margin: 2rem 0;">
    <h3>💡 คำแนะนำ:</h3>
    <ul>
        <li><strong>Username:</strong> ใช้ชื่อที่จำง่าย เช่น admin, administrator</li>
        <li><strong>Password:</strong> ใช้รหัสผ่านที่ปลอดภัย อย่างน้อย 8 ตัวอักษร</li>
        <li><strong>Phone:</strong> ไม่บังคับ แต่แนะนำให้ใส่เพื่อการติดต่อ</li>
    </ul>
</div>

<div style="background: #e7f3ff; padding: 1rem; border-radius: 8px; margin: 2rem 0;">
    <h3>🔐 ความปลอดภัย:</h3>
    <ul>
        <li>รหัสผ่านจะถูกเข้ารหัสด้วย PHP password_hash()</li>
        <li>Admin User จะมีสิทธิ์เข้าถึงระบบจัดการทั้งหมด</li>
        <li>ควรลบไฟล์นี้หลังจากสร้าง admin เสร็จแล้ว</li>
    </ul>
</div>

<p style="text-align: center; margin-top: 2rem;">
    <a href="index.php" style="background: #28a745; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">กลับไปหน้าแรก</a>
</p>
