<?php
// Debug slip upload functionality
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Test user ID
}

echo "<h1>🔍 Debug การอัปโหลดสลิป</h1>";

// Check database connection
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p>✅ Database connection: OK</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Check payment_transactions table
try {
    $stmt = $pdo->query("DESCRIBE payment_transactions");
    $columns = $stmt->fetchAll();
    echo "<h3>📋 payment_transactions table structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p>❌ Error checking table structure: " . $e->getMessage() . "</p>";
}

// Check for pending payments
try {
    $stmt = $pdo->prepare("
        SELECT pt.id, pt.user_id, pt.amount, pt.status, pt.slip_image, pt.slip_hash, pt.created_at,
               us.id as subscription_id, p.name as package_name
        FROM payment_transactions pt
        LEFT JOIN user_subscriptions us ON pt.subscription_id = us.id
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE pt.user_id = ?
        ORDER BY pt.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $payments = $stmt->fetchAll();
    
    echo "<h3>💰 Recent payments for user {$_SESSION['user_id']}:</h3>";
    if ($payments) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Amount</th><th>Status</th><th>Package</th><th>Slip Image</th><th>Slip Hash</th><th>Created</th></tr>";
        foreach ($payments as $payment) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . $payment['amount'] . "</td>";
            echo "<td>" . $payment['status'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['package_name'] ?? 'N/A') . "</td>";
            echo "<td>" . ($payment['slip_image'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($payment['slip_hash'] ? substr($payment['slip_hash'], 0, 10) . '...' : '❌') . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>ไม่มีรายการชำระเงิน</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking payments: " . $e->getMessage() . "</p>";
}

// Check user subscriptions
try {
    $stmt = $pdo->prepare("
        SELECT us.id, us.status, us.payment_amount, p.name as package_name, us.created_at
        FROM user_subscriptions us
        LEFT JOIN packages p ON us.package_id = p.id
        WHERE us.user_id = ?
        ORDER BY us.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $subscriptions = $stmt->fetchAll();
    
    echo "<h3>📦 Recent subscriptions for user {$_SESSION['user_id']}:</h3>";
    if ($subscriptions) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Package</th><th>Amount</th><th>Status</th><th>Created</th></tr>";
        foreach ($subscriptions as $sub) {
            echo "<tr>";
            echo "<td>" . $sub['id'] . "</td>";
            echo "<td>" . htmlspecialchars($sub['package_name'] ?? 'N/A') . "</td>";
            echo "<td>" . $sub['payment_amount'] . "</td>";
            echo "<td>" . $sub['status'] . "</td>";
            echo "<td>" . $sub['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>ไม่มีรายการสมัครสมาชิก</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking subscriptions: " . $e->getMessage() . "</p>";
}

// Check uploads directory
$upload_dir = 'uploads/slips/';
echo "<h3>📁 Upload directory check:</h3>";
echo "<ul>";
echo "<li><strong>Directory exists:</strong> " . (is_dir($upload_dir) ? '✅' : '❌') . "</li>";
echo "<li><strong>Directory readable:</strong> " . (is_readable($upload_dir) ? '✅' : '❌') . "</li>";
echo "<li><strong>Directory writable:</strong> " . (is_writable($upload_dir) ? '✅' : '❌') . "</li>";
echo "<li><strong>Full path:</strong> " . realpath($upload_dir) . "</li>";
echo "</ul>";

// List existing files
if (is_dir($upload_dir)) {
    $files = glob($upload_dir . '*');
    echo "<h3>📄 Files in upload directory:</h3>";
    if ($files) {
        echo "<ul>";
        foreach ($files as $file) {
            if (is_file($file)) {
                $filename = basename($file);
                $size = filesize($file);
                $date = date('Y-m-d H:i:s', filemtime($file));
                echo "<li><strong>{$filename}</strong> - " . number_format($size) . " bytes - {$date}</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p>ไม่มีไฟล์ในโฟลเดอร์</p>";
    }
}

// PHP configuration
echo "<h3>⚙️ PHP Upload Configuration:</h3>";
echo "<ul>";
echo "<li><strong>file_uploads:</strong> " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>upload_max_filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>post_max_size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>max_file_uploads:</strong> " . ini_get('max_file_uploads') . "</li>";
echo "<li><strong>upload_tmp_dir:</strong> " . (ini_get('upload_tmp_dir') ?: 'Default') . "</li>";
echo "<li><strong>max_execution_time:</strong> " . ini_get('max_execution_time') . " seconds</li>";
echo "<li><strong>memory_limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "</ul>";

// Test form
if ($_POST && isset($_FILES['debug_slip'])) {
    echo "<hr><h3>🧪 Upload Test Results:</h3>";
    
    try {
        $file = $_FILES['debug_slip'];
        
        echo "<p><strong>File Info:</strong></p>";
        echo "<ul>";
        echo "<li>Name: " . htmlspecialchars($file['name']) . "</li>";
        echo "<li>Size: " . number_format($file['size']) . " bytes</li>";
        echo "<li>Type: " . htmlspecialchars($file['type']) . "</li>";
        echo "<li>Error: " . $file['error'] . "</li>";
        echo "<li>Temp file: " . htmlspecialchars($file['tmp_name']) . "</li>";
        echo "</ul>";
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            echo "<p>✅ File uploaded successfully to temp location</p>";
            
            // Check if it's an image
            $image_info = getimagesize($file['tmp_name']);
            if ($image_info) {
                echo "<p>✅ Valid image: {$image_info[0]}x{$image_info[1]} pixels</p>";
                
                // Calculate hash
                $file_hash = hash_file('sha256', $file['tmp_name']);
                echo "<p><strong>File hash:</strong> {$file_hash}</p>";
                
                // Try to move file
                $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $new_filename = 'debug_' . time() . '_' . uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;
                
                if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                    echo "<p>✅ File moved successfully to: {$upload_path}</p>";
                    
                    // Verify file exists
                    if (file_exists($upload_path)) {
                        echo "<p>✅ File verified on disk</p>";
                        
                        // Try to update database (simulate)
                        echo "<p><strong>Database update simulation:</strong></p>";
                        
                        // Find a pending payment to update
                        $stmt = $pdo->prepare("
                            SELECT id FROM payment_transactions 
                            WHERE user_id = ? AND status = 'pending'
                            ORDER BY created_at DESC LIMIT 1
                        ");
                        $stmt->execute([$_SESSION['user_id']]);
                        $payment = $stmt->fetch();
                        
                        if ($payment) {
                            echo "<p>✅ Found pending payment ID: {$payment['id']}</p>";
                            
                            // Update with slip info
                            $stmt = $pdo->prepare("
                                UPDATE payment_transactions
                                SET slip_image = ?, slip_hash = ?, updated_at = NOW()
                                WHERE id = ?
                            ");
                            $result = $stmt->execute([$upload_path, $file_hash, $payment['id']]);
                            
                            if ($result) {
                                echo "<p>✅ Database updated successfully!</p>";
                            } else {
                                echo "<p>❌ Database update failed</p>";
                            }
                        } else {
                            echo "<p>⚠️ No pending payment found to update</p>";
                        }
                        
                    } else {
                        echo "<p>❌ File not found after move</p>";
                    }
                } else {
                    echo "<p>❌ Failed to move file</p>";
                }
            } else {
                echo "<p>❌ Not a valid image file</p>";
            }
        } else {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'File too large (ini_size)',
                UPLOAD_ERR_FORM_SIZE => 'File too large (form_size)',
                UPLOAD_ERR_PARTIAL => 'File partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'No temp directory',
                UPLOAD_ERR_CANT_WRITE => 'Cannot write file',
                UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
            ];
            echo "<p>❌ Upload error: " . ($upload_errors[$file['error']] ?? 'Unknown error') . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Slip Upload</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .form-group { margin: 20px 0; }
        input[type="file"] { padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        hr { margin: 30px 0; }
    </style>
</head>
<body>
    <hr>
    <h3>🧪 Test Upload Form</h3>
    <form method="POST" enctype="multipart/form-data">
        <div class="form-group">
            <label for="debug_slip">Select image file to test upload:</label>
            <input type="file" id="debug_slip" name="debug_slip" accept="image/*" required>
        </div>
        <button type="submit">Test Upload</button>
    </form>
</body>
</html>
