# Cron Job Setup for Jellyfin Registration System
# 
# To set up the cron job, run the following command:
# crontab -e
# 
# Then add this line to run the transaction check every 5 minutes:

*/5 * * * * /usr/bin/php /path/to/your/site/cron/check_transactions.php >> /path/to/your/site/logs/cron.log 2>&1

# Replace "/path/to/your/site" with the actual path to your website
# For example, if your site is in /var/www/html/jellyfin-registration:
# */5 * * * * /usr/bin/php /var/www/html/jellyfin-registration/cron/check_transactions.php >> /var/www/html/jellyfin-registration/logs/cron.log 2>&1

# Alternative: Run every 10 minutes (less frequent)
# */10 * * * * /usr/bin/php /path/to/your/site/cron/check_transactions.php >> /path/to/your/site/logs/cron.log 2>&1

# To check if cron job is running:
# crontab -l

# To view cron logs:
# tail -f /path/to/your/site/logs/cron.log
# tail -f /path/to/your/site/logs/transaction_check.log

# Note: Make sure PHP CLI is installed and the path to PHP is correct
# You can find the PHP path with: which php
