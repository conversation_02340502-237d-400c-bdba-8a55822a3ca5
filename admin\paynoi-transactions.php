<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/PayNoiAPIClass.php';

// Require admin access
require_login();
require_admin();

$message = '';
$message_type = 'info';
$transactions = [];
$api_status = '';

// Handle form submission
if ($_POST && isset($_POST['fetch_transactions'])) {
    try {
        $paynoiAPI = new PayNoiAPI();

        // Get date range from form
        $start_date = $_POST['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
        $end_date = $_POST['end_date'] ?? date('Y-m-d');
        $limit = intval($_POST['limit'] ?? 50);

        // Debug: Test API connection first
        $debug_info = [];

        // Test 1: Try to get recent transactions
        try {
            $recent = $paynoiAPI->getRecentTransactions();
            $debug_info['recent_test'] = $recent ? 'SUCCESS' : 'NO_DATA';
        } catch (Exception $e) {
            $debug_info['recent_test'] = 'ERROR: ' . $e->getMessage();
        }

        // Test 2: Get raw response for debugging
        $startDateTime = $start_date . ' 00:00:00';
        $endDateTime = $end_date . ' 23:59:59';
        $raw_response = $paynoiAPI->getTransactions($startDateTime, $endDateTime);
        $debug_info['raw_response'] = $raw_response;

        // Test 3: Fetch transactions by date range
        $transactions = $paynoiAPI->getTransactionsByDateRange($start_date, $end_date, $limit);
        $debug_info['processed_transactions'] = $transactions;

        if ($transactions === false) {
            $message = 'ไม่สามารถดึงข้อมูลจาก PayNoi API ได้<br>';
            $message .= 'Debug Info: <pre>' . json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            $message_type = 'error';
            $api_status = 'error';
        } elseif (empty($transactions)) {
            $message = 'ไม่พบธุรกรรมในช่วงวันที่ที่เลือก (' . $start_date . ' ถึง ' . $end_date . ')<br>';
            $message .= 'แต่ API ทำงานได้ปกติ ลองเปลี่ยนช่วงวันที่หรือดูข้อมูล debug ด้านล่าง<br>';
            $message .= '<details><summary>ดู Debug Info</summary><pre>' . json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre></details>';
            $message_type = 'warning';
            $api_status = 'no_data';
        } else {
            $message = 'ดึงข้อมูลธุรกรรมสำเร็จ พบ ' . count($transactions) . ' รายการ';
            $message_type = 'success';
            $api_status = 'success';
        }

    } catch (Exception $e) {
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $message .= '<br>File: ' . $e->getFile() . ' Line: ' . $e->getLine();
        $message_type = 'error';
        $api_status = 'error';
    }
}

// Get default date range
$default_start = date('Y-m-d', strtotime('-7 days'));
$default_end = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ธุรกรรม PayNoi - Jellyfin by James</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .transactions-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .filter-form {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #333;
            margin-bottom: 2rem;
        }
        .form-row {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }
        .form-group {
            flex: 1;
            min-width: 150px;
        }
        .transactions-table {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #333;
            overflow: hidden;
            margin: 1rem 0;
        }
        .transactions-table th,
        .transactions-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #333;
            font-size: 0.9rem;
        }
        .transactions-table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            color: #fff;
            position: sticky;
            top: 0;
        }
        .transactions-table tbody {
            max-height: 600px;
            overflow-y: auto;
        }
        .amount-in {
            color: #28a745;
            font-weight: bold;
        }
        .amount-out {
            color: #dc3545;
            font-weight: bold;
        }
        .transaction-type {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .type-in {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        .type-out {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        .api-status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .api-status.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }
        .api-status.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        .api-status.no_data {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .summary-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #333;
            text-align: center;
        }
        .summary-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .summary-label {
            color: #ccc;
            font-size: 0.9rem;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #333;
            border-radius: 8px;
        }
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="film-grain-overlay"></div>
    
    <header class="main-header">
        <div class="container">
            <h1 class="logo fade-in-up">
                <a href="/" style="color: inherit; text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                    <img src="../assets/images/logo.svg" alt="Jellyfin by James Cinema Logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-flex';">
                    <span class="logo-fallback" style="display: none; align-items: center; gap: 10px; font-size: 2rem;">
                        🎬
                    </span>
                    <span class="logo-text">
                        Jellyfin by James
                        <small class="cinema-subtitle">Cinema</small>
                    </span>
                </a>
            </h1>
            <nav class="main-nav">
                <a href="../dashboard.php" class="nav-link">หน้าหลัก</a>
                <a href="../packages.php" class="nav-link">แพ็คเกจ</a>
                <div class="admin-dropdown" id="adminDropdown">
                    <a href="#" class="nav-link active" onclick="toggleDropdown('adminDropdown'); return false;">⚙️ Admin Console</a>
                    <div class="dropdown-menu">
                        <a href="server-control.php" class="dropdown-item">เซิร์ฟเวอร์</a>
                        <a href="users.php" class="dropdown-item">ผู้ใช้</a>
                        <a href="packages.php" class="dropdown-item">แพ็คเกจ</a>
                        <a href="manage-packages.php" class="dropdown-item">จัดการแพ็คเกจ</a>
                        <a href="transaction-check.php" class="dropdown-item">ตรวจสอบธุรกรรม</a>
                        <a href="paynoi-transactions.php" class="dropdown-item" style="background: rgba(255, 193, 7, 0.2); color: #ffc107;">PayNoi</a>
                    </div>
                </div>
                <div class="profile-dropdown" id="profileDropdown">
                    <a href="#" class="nav-link" onclick="toggleDropdown('profileDropdown'); return false;">👤 <?php echo htmlspecialchars($_SESSION['username'] ?? 'ผู้ใช้'); ?></a>
                    <div class="dropdown-menu">
                        <a href="../profile.php" class="dropdown-item">โปรไฟล์</a>
                        <a href="../logout.php" class="dropdown-item logout">ออกจากระบบ</a>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="transactions-container">
            <div class="hero-section" style="padding: 2rem 0;">
                <h2 class="fade-in-up">ธุรกรรมเงินเข้าจาก PayNoi</h2>
                <p class="fade-in-up delay-1" style="color: #ccc;">ดูรายการธุรกรรมเงินเข้าจาก PayNoi API</p>
            </div>

            <?php if ($message): ?>
                <div class="flash-message flash-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Filter Form -->
            <div class="filter-form fade-in-scroll">
                <h3>กรองข้อมูล</h3>
                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="start_date">วันที่เริ่มต้น:</label>
                            <input type="date" id="start_date" name="start_date" class="form-control"
                                   value="<?php echo $_POST['start_date'] ?? $default_start; ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="end_date">วันที่สิ้นสุด:</label>
                            <input type="date" id="end_date" name="end_date" class="form-control"
                                   value="<?php echo $_POST['end_date'] ?? $default_end; ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="limit">จำนวนรายการ:</label>
                            <select id="limit" name="limit" class="form-control">
                                <option value="50" <?php echo ($_POST['limit'] ?? 50) == 50 ? 'selected' : ''; ?>>50 รายการ</option>
                                <option value="100" <?php echo ($_POST['limit'] ?? 50) == 100 ? 'selected' : ''; ?>>100 รายการ</option>
                                <option value="200" <?php echo ($_POST['limit'] ?? 50) == 200 ? 'selected' : ''; ?>>200 รายการ</option>
                                <option value="500" <?php echo ($_POST['limit'] ?? 50) == 500 ? 'selected' : ''; ?>>500 รายการ</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button type="submit" name="fetch_transactions" class="btn btn-primary noir-button">
                                🔍 ดึงข้อมูล
                            </button>
                        </div>
                    </div>

                    <!-- Quick Date Buttons -->
                    <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button type="button" onclick="setDateRange(1)" class="btn btn-secondary" style="font-size: 0.8rem;">วันนี้</button>
                        <button type="button" onclick="setDateRange(3)" class="btn btn-secondary" style="font-size: 0.8rem;">3 วันที่ผ่านมา</button>
                        <button type="button" onclick="setDateRange(7)" class="btn btn-secondary" style="font-size: 0.8rem;">7 วันที่ผ่านมา</button>
                        <button type="button" onclick="setDateRange(30)" class="btn btn-secondary" style="font-size: 0.8rem;">30 วันที่ผ่านมา</button>
                    </div>
                </form>
            </div>

            <script>
            function setDateRange(days) {
                const today = new Date();
                const startDate = new Date(today);
                startDate.setDate(today.getDate() - days);

                document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
                document.getElementById('end_date').value = today.toISOString().split('T')[0];
            }
            </script>

            <?php if ($api_status && !empty($transactions)): ?>
                <!-- Summary Cards -->
                <?php
                $total_in = 0;
                $total_out = 0;
                $count_in = 0;
                $count_out = 0;
                
                foreach ($transactions as $transaction) {
                    if ($transaction['type'] === 'in' || $transaction['type'] === 'credit') {
                        $total_in += floatval($transaction['amount']);
                        $count_in++;
                    } else {
                        $total_out += floatval($transaction['amount']);
                        $count_out++;
                    }
                }
                ?>
                <div class="summary-cards fade-in-scroll">
                    <div class="summary-card">
                        <div class="summary-number amount-in"><?php echo format_currency($total_in); ?></div>
                        <div class="summary-label">เงินเข้าทั้งหมด</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number"><?php echo $count_in; ?></div>
                        <div class="summary-label">รายการเงินเข้า</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number amount-out"><?php echo format_currency($total_out); ?></div>
                        <div class="summary-label">เงินออกทั้งหมด</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number"><?php echo $count_out; ?></div>
                        <div class="summary-label">รายการเงินออก</div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Transactions Table -->
            <?php if ($api_status === 'success' && !empty($transactions)): ?>
                <div class="fade-in-scroll">
                    <h3>รายการธุรกรรม</h3>
                    <div class="table-container">
                        <table class="transactions-table">
                            <thead>
                                <tr>
                                    <th>วันที่/เวลา</th>
                                    <th>ประเภท</th>
                                    <th>จำนวนเงิน</th>
                                    <th>จาก/ไป</th>
                                    <th>รายละเอียด</th>
                                    <th>อ้างอิง</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <?php 
                                            $date = date('d/m/Y H:i', strtotime($transaction['datetime'] ?? $transaction['timestamp'] ?? 'now'));
                                            echo $date;
                                            ?>
                                        </td>
                                        <td>
                                            <?php 
                                            $type = $transaction['type'] ?? 'unknown';
                                            $type_class = ($type === 'in' || $type === 'credit') ? 'type-in' : 'type-out';
                                            $type_text = ($type === 'in' || $type === 'credit') ? 'เงินเข้า' : 'เงินออก';
                                            ?>
                                            <span class="transaction-type <?php echo $type_class; ?>">
                                                <?php echo $type_text; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            $amount_class = ($type === 'in' || $type === 'credit') ? 'amount-in' : 'amount-out';
                                            ?>
                                            <span class="<?php echo $amount_class; ?>">
                                                <?php echo format_currency($transaction['amount']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            if ($type === 'in' || $type === 'credit') {
                                                echo htmlspecialchars($transaction['from'] ?? $transaction['sender'] ?? '-');
                                            } else {
                                                echo htmlspecialchars($transaction['to'] ?? $transaction['receiver'] ?? '-');
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($transaction['description'] ?? $transaction['memo'] ?? '-'); ?>
                                        </td>
                                        <td>
                                            <small style="font-family: monospace;">
                                                <?php echo htmlspecialchars($transaction['reference'] ?? $transaction['id'] ?? '-'); ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php elseif ($api_status === 'no_data'): ?>
                <div class="no-data fade-in-scroll">
                    <h3>ไม่พบข้อมูล</h3>
                    <p>ไม่พบธุรกรรมในช่วงวันที่ที่เลือก</p>
                    <p style="color: #888;">ลองเปลี่ยนช่วงวันที่หรือตรวจสอบการตั้งค่า PayNoi API</p>
                </div>
            <?php elseif ($api_status === 'error'): ?>
                <div class="no-data fade-in-scroll">
                    <h3>เกิดข้อผิดพลาด</h3>
                    <p>ไม่สามารถดึงข้อมูลจาก PayNoi API ได้</p>
                    <div style="margin-top: 1rem;">
                        <a href="../check_php.php" style="color: #ffc107; text-decoration: underline;">
                            ตรวจสอบการตั้งค่า
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="no-data fade-in-scroll">
                    <h3>เริ่มต้นใช้งาน</h3>
                    <p>เลือกช่วงวันที่และคลิก "ดึงข้อมูล" เพื่อดูรายการธุรกรรม</p>
                </div>
            <?php endif; ?>

            <!-- API Info -->
            <div style="background: rgba(23, 162, 184, 0.2); border: 1px solid #17a2b8; padding: 1rem; border-radius: 4px; margin: 2rem 0;">
                <h4 style="color: #17a2b8;">ข้อมูล PayNoi API</h4>
                <ul style="color: #ccc; font-size: 0.9rem; margin: 0.5rem 0;">
                    <li>ข้อมูลดึงมาจาก PayNoi API แบบ real-time</li>
                    <li>แสดงเฉพาะธุรกรรมที่เกี่ยวข้องกับบัญชี 082-072-2972</li>
                    <li>ข้อมูลอาจมีความล่าช้า 1-2 นาทีจากธุรกรรมจริง</li>
                    <li>สำหรับการตรวจสอบและจับคู่การชำระเงิน</li>
                </ul>
            </div>
        </div>
    </main>

    <footer class="main-footer">
        <div class="container">
            <p>&copy; 2024 Jellyfin by James Registration System. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dropdown.js"></script>
</body>
</html>
