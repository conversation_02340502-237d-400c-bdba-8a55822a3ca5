# 🎬 Jellyfin by <PERSON> - Production Deployment Guide

## 🌐 Production Environment
- **Server**: *************
- **Domain**: https://embyjames.xyz
- **Database**: jellyfin_registration
- **OS**: Linux (Ubuntu/Debian)

---

## 🚀 Quick Deployment

### 1. Upload Files to Server
```bash
scp *.py *.sh *.service *.txt root@*************:/tmp/
```

### 2. Run Automated Installation
```bash
ssh root@*************
cd /tmp
chmod +x install_production.sh
./install_production.sh
```

### 3. Verify Installation
```bash
python3 monitor_production.py
```

---

## 🔄 Automated Services

### 🔥 Continuous Payment Checker (Every 5 seconds)
- **Service**: `jellyfin-payment-checker`
- **Location**: `/opt/jellyfin/continuous_payment_checker.py`
- **Logs**: `/var/log/jellyfin/continuous_payment.log`

**Management:**
```bash
sudo systemctl status jellyfin-payment-checker
sudo systemctl start jellyfin-payment-checker
sudo systemctl stop jellyfin-payment-checker
sudo systemctl restart jellyfin-payment-checker
```

### ⏰ Cron Jobs
- **Full system check**: Every 5 minutes
- **Health monitoring**: Every minute
- **Payment backup check**: Every 30 seconds
- **Log cleanup**: Daily at midnight
- **Database optimization**: Monthly

**View cron jobs:**
```bash
sudo -u www-data crontab -l
```

---

## 📊 Monitoring & Logs

### 🔍 Real-time Monitoring
```bash
# Complete system status
python3 monitor_production.py

# Specific checks
python3 monitor_production.py api      # API health
python3 monitor_production.py service  # Service status
python3 monitor_production.py logs     # Log files
python3 monitor_production.py activity # Recent activity
python3 monitor_production.py database # Database status
```

### 📋 Log Files
```bash
# Continuous payment processing
tail -f /var/log/jellyfin/continuous_payment.log

# Cron job activities
tail -f /var/log/jellyfin/payment_cron.log

# Health checks
tail -f /var/log/jellyfin/cron_health.log

# System service logs
sudo journalctl -u jellyfin-payment-checker -f
```

---

## 🛠️ Manual Operations

### 💰 Payment Processing
```bash
# Check payments only
python3 /opt/jellyfin/production_cron.py payment

# Full system check (payments + expiration)
python3 /opt/jellyfin/production_cron.py full

# Health check only
python3 /opt/jellyfin/production_cron.py health
```

### 🔧 Service Management
```bash
# Enable auto-start on boot
sudo systemctl enable jellyfin-payment-checker

# Disable auto-start
sudo systemctl disable jellyfin-payment-checker

# View service configuration
sudo systemctl cat jellyfin-payment-checker
```

---

## 🌐 API Endpoints

All APIs automatically detect production environment:

- **Health Check**: `https://embyjames.xyz/api/health.php`
- **Payment Processing**: `https://embyjames.xyz/api/payment-check.php`
- **Manual Verification**: `https://embyjames.xyz/api/manual-check.php`
- **User Expiration**: `https://embyjames.xyz/api/expiration-check.php`

### 🧪 API Testing
```bash
# Test all endpoints
curl -X POST https://embyjames.xyz/api/health.php
curl -X POST https://embyjames.xyz/api/payment-check.php
curl -X POST https://embyjames.xyz/api/manual-check.php
curl -X POST https://embyjames.xyz/api/expiration-check.php
```

---

## 🗄️ Database Configuration

### Production Database
- **Host**: *************
- **Database**: jellyfin_registration
- **User**: root
- **Tables**: users, payment_transaction, packages

### Manual Database Access
```bash
mysql -h ************* -u root -p jellyfin_registration
```

---

## 🚨 Troubleshooting

### Service Issues
```bash
# Check service status
sudo systemctl status jellyfin-payment-checker

# View detailed logs
sudo journalctl -u jellyfin-payment-checker --no-pager

# Check for errors
sudo journalctl -u jellyfin-payment-checker -p err
```

### API Issues
```bash
# Test API connectivity
python3 monitor_production.py api

# Check web server logs
sudo tail -f /var/log/apache2/error.log
sudo tail -f /var/log/nginx/error.log
```

### Database Issues
```bash
# Test database connection
python3 monitor_production.py database

# Check MySQL status
sudo systemctl status mysql
```

### Permission Issues
```bash
# Fix log permissions
sudo chown -R www-data:www-data /var/log/jellyfin

# Fix script permissions
sudo chmod +x /opt/jellyfin/*.py
```

---

## 📈 Performance Monitoring

### Resource Usage
- **Memory**: ~50MB per service
- **CPU**: <5% average load
- **Disk**: Logs auto-rotate weekly
- **Network**: Minimal API calls only

### Performance Checks
```bash
# Check memory usage
ps aux | grep jellyfin

# Check disk usage
du -sh /var/log/jellyfin/

# Check network connections
netstat -tulpn | grep python3
```

---

## 🔐 Security Features

- **Service isolation**: Runs as www-data user
- **Resource limits**: Memory and CPU quotas
- **Log rotation**: Prevents disk filling
- **HTTPS**: All API calls encrypted
- **Database**: Secured credentials

---

## 📅 Maintenance Schedule

### Daily
- ✅ Automatic log rotation
- ✅ Payment processing (continuous)
- ✅ Health monitoring

### Weekly
- ✅ Old log cleanup
- ✅ Temporary file cleanup

### Monthly
- ✅ Database optimization
- 🔧 Manual system review

---

## 🎯 Success Indicators

### ✅ System is Working When:
- Service status shows "active (running)"
- API health checks return 200 OK
- Recent activity in logs (< 5 minutes)
- Database connections successful
- No error messages in logs

### ❌ Issues to Watch For:
- Service status "failed" or "inactive"
- API timeouts or connection errors
- Stale log files (> 1 hour old)
- Database connection failures
- High error rates in logs

---

## 📞 Support

For issues or questions:
1. Check logs: `python3 monitor_production.py`
2. Review service status: `sudo systemctl status jellyfin-payment-checker`
3. Test APIs: `python3 monitor_production.py api`
4. Check database: `python3 monitor_production.py database`

**The production system provides 24/7 automated payment processing with comprehensive monitoring!** 🎉
