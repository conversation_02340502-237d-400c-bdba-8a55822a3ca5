<?php
/**
 * Test PHP syntax for all cron files
 * This script checks if all cron files have valid PHP syntax
 */

echo "🔍 Testing PHP syntax for all cron files...\n\n";

$cron_files = [
    'cron/linux_payment_check.php',
    'cron/linux_manual_check.php', 
    'cron/linux_health_check.php',
    'cron/linux_expiration_check.php',
    'cron/check_transactions.php',
    'cron/check_expiration.php'
];

$errors = [];
$success = [];

foreach ($cron_files as $file) {
    if (!file_exists($file)) {
        echo "❌ File not found: {$file}\n";
        $errors[] = $file . " (not found)";
        continue;
    }
    
    // Test PHP syntax
    $output = [];
    $return_code = 0;
    exec("php -l \"{$file}\" 2>&1", $output, $return_code);
    
    if ($return_code === 0) {
        echo "✅ {$file} - Syntax OK\n";
        $success[] = $file;
    } else {
        echo "❌ {$file} - Syntax Error:\n";
        foreach ($output as $line) {
            echo "   {$line}\n";
        }
        $errors[] = $file . " (" . implode(', ', $output) . ")";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 Summary:\n";
echo "✅ Success: " . count($success) . " files\n";
echo "❌ Errors: " . count($errors) . " files\n";

if (!empty($errors)) {
    echo "\n🚨 Files with errors:\n";
    foreach ($errors as $error) {
        echo "   - {$error}\n";
    }
} else {
    echo "\n🎉 All cron files have valid PHP syntax!\n";
}

// Test if we can include the files without errors
echo "\n🧪 Testing file inclusion...\n";
foreach ($success as $file) {
    try {
        // Capture any output
        ob_start();
        
        // Temporarily disable HTTP_HOST check for testing
        $_SERVER_BACKUP = $_SERVER;
        unset($_SERVER['HTTP_HOST']);
        
        // Include the file
        include_once $file;
        
        // Restore $_SERVER
        $_SERVER = $_SERVER_BACKUP;
        
        $output = ob_get_clean();
        echo "✅ {$file} - Include OK\n";
        
    } catch (Exception $e) {
        echo "❌ {$file} - Include Error: " . $e->getMessage() . "\n";
    } catch (Error $e) {
        echo "❌ {$file} - Fatal Error: " . $e->getMessage() . "\n";
    }
}

echo "\n✨ Syntax check completed!\n";
?>
